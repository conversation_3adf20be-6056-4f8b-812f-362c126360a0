using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MolySite.Identity.Models;

namespace MolySite.Identity.Data.Migrations
{
    /// <summary>
    /// 角色系统迁移服务
    /// </summary>
    public class RoleSystemMigrationService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<RoleSystemMigrationService> _logger;

        public RoleSystemMigrationService(
            ApplicationDbContext context,
            ILogger<RoleSystemMigrationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 执行角色系统迁移
        /// </summary>
        public async Task<bool> MigrateRoleSystemAsync()
        {
            try
            {
                _logger.LogInformation("开始执行角色系统迁移...");

                // 1. 检查是否需要迁移
                if (await IsAlreadyMigratedAsync())
                {
                    _logger.LogInformation("角色系统已经迁移过，跳过迁移");
                    return true;
                }

                // 2. 开始事务
                using var transaction = await _context.Database.BeginTransactionAsync();

                try
                {
                    // 3. 更新平台角色
                    await UpdatePlatformRolesAsync();

                    // 4. 验证数据完整性
                    await ValidateDataIntegrityAsync();

                    // 5. 创建索引
                    await CreateOptimizationIndexesAsync();

                    // 6. 提交事务
                    await transaction.CommitAsync();

                    _logger.LogInformation("角色系统迁移完成");
                    return true;
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    _logger.LogError(ex, "角色系统迁移失败，已回滚");
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行角色系统迁移时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 检查是否已经迁移过
        /// </summary>
        private async Task<bool> IsAlreadyMigratedAsync()
        {
            // 检查是否还有旧的角色值
            var hasOldRoles = await _context.Users
                .AnyAsync(u => u.PlatformRole == (PlatformRole)3 || // 旧的SiteEditor
                              u.PlatformRoles.Any(r => r == "SiteOwner" || r == "SiteEditor"));

            return !hasOldRoles;
        }

        /// <summary>
        /// 更新平台角色
        /// </summary>
        private async Task UpdatePlatformRolesAsync()
        {
            _logger.LogInformation("更新用户平台角色...");

            // 获取所有需要更新的用户
            var usersToUpdate = await _context.Users
                .Where(u => u.PlatformRole == (PlatformRole)2 || // SiteOwner
                           u.PlatformRole == (PlatformRole)3 || // SiteEditor
                           u.PlatformRoles.Any(r => r == "SiteOwner" || r == "SiteEditor"))
                .ToListAsync();

            foreach (var user in usersToUpdate)
            {
                // 将所有非SuperAdmin用户设置为User
                if (user.PlatformRole != PlatformRole.SuperAdmin)
                {
                    user.PlatformRole = PlatformRole.User;
                    user.PlatformRoles = new List<string> { "User" };
                }
            }

            await _context.SaveChangesAsync();
            _logger.LogInformation("已更新 {Count} 个用户的平台角色", usersToUpdate.Count);
        }

        /// <summary>
        /// 验证数据完整性
        /// </summary>
        private async Task ValidateDataIntegrityAsync()
        {
            _logger.LogInformation("验证数据完整性...");

            // 检查是否有无效的平台角色
            var invalidRoleUsers = await _context.Users
                .Where(u => u.PlatformRole != PlatformRole.SuperAdmin && 
                           u.PlatformRole != PlatformRole.User)
                .CountAsync();

            if (invalidRoleUsers > 0)
            {
                throw new InvalidOperationException($"发现 {invalidRoleUsers} 个用户的平台角色无效");
            }

            // 检查角色字符串表示
            var invalidRoleStringUsers = await _context.Users
                .Where(u => u.PlatformRoles.Any(r => r != "SuperAdmin" && r != "User"))
                .CountAsync();

            if (invalidRoleStringUsers > 0)
            {
                throw new InvalidOperationException($"发现 {invalidRoleStringUsers} 个用户的角色字符串无效");
            }

            // 统计信息
            var totalUsers = await _context.Users.CountAsync();
            var superAdminCount = await _context.Users.CountAsync(u => u.PlatformRole == PlatformRole.SuperAdmin);
            var userCount = await _context.Users.CountAsync(u => u.PlatformRole == PlatformRole.User);

            _logger.LogInformation("数据完整性验证通过:");
            _logger.LogInformation("总用户数: {TotalUsers}", totalUsers);
            _logger.LogInformation("SuperAdmin 用户数: {SuperAdminCount}", superAdminCount);
            _logger.LogInformation("User 用户数: {UserCount}", userCount);

            if (superAdminCount + userCount != totalUsers)
            {
                throw new InvalidOperationException("角色分配不完整");
            }
        }

        /// <summary>
        /// 创建优化索引
        /// </summary>
        private async Task CreateOptimizationIndexesAsync()
        {
            _logger.LogInformation("创建优化索引...");

            try
            {
                // 为关注功能创建索引
                await _context.Database.ExecuteSqlRawAsync(@"
                    CREATE INDEX IF NOT EXISTS ""IX_SiteUserRoles_Role_IsActive"" 
                    ON ""SiteUserRoles"" (""Role"", ""IsActive"") 
                    WHERE ""Role"" = 3");

                await _context.Database.ExecuteSqlRawAsync(@"
                    CREATE INDEX IF NOT EXISTS ""IX_SiteUserRoles_UserId_Role_IsActive"" 
                    ON ""SiteUserRoles"" (""UserId"", ""Role"", ""IsActive"")");

                await _context.Database.ExecuteSqlRawAsync(@"
                    CREATE INDEX IF NOT EXISTS ""IX_SiteUserRoles_SiteId_Role_IsActive"" 
                    ON ""SiteUserRoles"" (""SiteId"", ""Role"", ""IsActive"")");

                _logger.LogInformation("优化索引创建完成");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "创建优化索引时发生警告，但不影响迁移");
            }
        }

        /// <summary>
        /// 获取迁移统计信息
        /// </summary>
        public async Task<MigrationStats> GetMigrationStatsAsync()
        {
            var stats = new MigrationStats
            {
                TotalUsers = await _context.Users.CountAsync(),
                SuperAdminCount = await _context.Users.CountAsync(u => u.PlatformRole == PlatformRole.SuperAdmin),
                UserCount = await _context.Users.CountAsync(u => u.PlatformRole == PlatformRole.User),
                TotalSiteRoles = await _context.SiteUserRoles.CountAsync(),
                OwnerRoles = await _context.SiteUserRoles.CountAsync(sr => sr.Role == SiteRoleType.Owner),
                ActiveSiteRoles = await _context.SiteUserRoles.CountAsync(sr => sr.IsActive)
            };

            return stats;
        }
    }

    /// <summary>
    /// 迁移统计信息
    /// </summary>
    public class MigrationStats
    {
        public int TotalUsers { get; set; }
        public int SuperAdminCount { get; set; }
        public int UserCount { get; set; }
        public int TotalSiteRoles { get; set; }
        public int OwnerRoles { get; set; }
        public int ActiveSiteRoles { get; set; }

        public override string ToString()
        {
            return $@"迁移统计信息:
总用户数: {TotalUsers}
SuperAdmin: {SuperAdminCount}
User: {UserCount}
总网站角色: {TotalSiteRoles}
Owner角色: {OwnerRoles}
活跃角色: {ActiveSiteRoles}";
        }
    }
}
