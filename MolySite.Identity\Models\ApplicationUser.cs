using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Identity;
using MolySite.Shared.Dtos;

namespace MolySite.Identity.Models
{
    /// <summary>
    /// 应用用户模型，支持多网站和细粒度权限
    /// </summary>
    public class ApplicationUser : IdentityUser<Guid>
    {
        /// <summary>
        /// 覆盖基类的 UserName 属性
        /// </summary>
        public override string? UserName { get; set; }

        /// <summary>
        /// 平台角色（SuperAdmin, User）
        /// </summary>
        public PlatformRole PlatformRole { get; set; } = PlatformRole.User;

        /// <summary>
        /// 平台角色字符串表示（用于兼容ASP.NET Core Identity）
        /// </summary>
        public List<string> PlatformRoles { get; set; } = new List<string> { "User" };

        /// <summary>
        /// 用户在各个网站中的角色关系
        /// </summary>
        public List<SiteUserRole> SiteRoles { get; set; } = new List<SiteUserRole>();

        /// <summary>
        /// 用户拥有的网站
        /// </summary>
        public List<Site> OwnedSites { get; set; } = new List<Site>();

        /// <summary>
        /// 用户权限（平台级权限）
        /// </summary>
        public List<string> Permissions { get; set; } = new List<string>();

        /// <summary>
        /// 账户是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 用户创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime? LastLoginAt { get; set; }

        /// <summary>
        /// 密码重置令牌
        /// </summary>
        public string? PasswordResetToken { get; set; }

        /// <summary>
        /// 密码重置令牌过期时间
        /// </summary>
        public DateTime? PasswordResetTokenExpiration { get; set; }

        /// <summary>
        /// 用户头像URL
        /// </summary>
        public string? AvatarUrl { get; set; }

        /// <summary>
        /// 用户显示名称
        /// </summary>
        public string? DisplayName { get; set; }

        /// <summary>
        /// 用户简介
        /// </summary>
        public string? Bio { get; set; }

        /// <summary>
        /// 用户时区
        /// </summary>
        public string TimeZone { get; set; } = "UTC";

        /// <summary>
        /// 用户语言偏好
        /// </summary>
        public string Language { get; set; } = "zh-CN";

        /// <summary>
        /// 是否接收邮件通知
        /// </summary>
        public bool EmailNotifications { get; set; } = true;

        /// <summary>
        /// 获取用户在指定网站的角色
        /// </summary>
        public SiteRoleType? GetRoleInSite(Guid siteId)
        {
            var siteRole = SiteRoles.FirstOrDefault(sr => sr.SiteId == siteId && sr.IsActive);
            return siteRole?.Role;
        }

        /// <summary>
        /// 检查用户是否拥有指定网站
        /// </summary>
        public bool OwnsWebsite(Guid siteId)
        {
            return OwnedSites.Any(s => s.Id == siteId) ||
                   SiteRoles.Any(sr => sr.SiteId == siteId && sr.Role == SiteRoleType.Owner && sr.IsActive);
        }

        /// <summary>
        /// 检查用户是否可以访问指定网站
        /// </summary>
        public bool CanAccessSite(Guid siteId)
        {
            return SiteRoles.Any(sr => sr.SiteId == siteId && sr.IsActive);
        }
    }
} 