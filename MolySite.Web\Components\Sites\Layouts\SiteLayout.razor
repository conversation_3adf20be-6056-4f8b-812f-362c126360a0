@inherits LayoutComponentBase
@inject IHttpContextAccessor HttpContextAccessor
@inject IJSRuntime JSRuntime

<div class="site-layout" data-site-id="@Site?.Id">
    <!-- 临时简单布局 - 第2周将完善 -->
    <div class="min-h-screen bg-gray-50">
        <header class="bg-white shadow-sm">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center">
                        @if (!string.IsNullOrEmpty(Site?.LogoUrl))
                        {
                            <img src="@Site.LogoUrl" alt="@Site.Name" class="h-8 w-auto mr-3" />
                        }
                        <h1 class="text-xl font-bold text-gray-900">@(Site?.Name ?? "网站")</h1>
                    </div>
                    @if (!string.IsNullOrEmpty(Site?.Description))
                    {
                        <p class="hidden md:block text-gray-600">@Site.Description</p>
                    }
                </div>
            </div>
        </header>

        <main class="flex-1">
            @Body
        </main>

        <footer class="bg-gray-800 text-white py-8">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <p>&copy; @DateTime.Now.Year @(Site?.Name ?? "网站"). All rights reserved.</p>
                @if (!string.IsNullOrEmpty(Site?.FooterText))
                {
                    <p class="mt-2 text-gray-400">@Site.FooterText</p>
                }
            </div>
        </footer>
    </div>

    <!-- 动态注入网站自定义CSS -->
    @if (!string.IsNullOrEmpty(Site?.CustomCss))
    {
        <style>@((MarkupString)Site.CustomCss)</style>
    }
    
    <!-- 动态注入网站自定义JavaScript -->
    @if (!string.IsNullOrEmpty(Site?.CustomJavaScript))
    {
        <script>@((MarkupString)Site.CustomJavaScript)</script>
    }
</div>

@code {
    private SiteDto? Site;

    protected override void OnInitialized()
    {
        var httpContext = HttpContextAccessor.HttpContext;
        Site = httpContext?.Items["CurrentSite"] as SiteDto;
    }
}
