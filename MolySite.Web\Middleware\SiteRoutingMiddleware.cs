using MolySite.Web.Services;
using MolySite.Shared.Dtos;

namespace MolySite.Web.Middleware
{
    /// <summary>
    /// 智能路由中间件 - 区分平台请求和用户网站请求
    /// </summary>
    public class SiteRoutingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly MolySite.Web.Services.ISiteService _siteService;
        private readonly ILogger<SiteRoutingMiddleware> _logger;

        public SiteRoutingMiddleware(
            RequestDelegate next,
            MolySite.Web.Services.ISiteService siteService,
            ILogger<SiteRoutingMiddleware> logger)
        {
            _next = next;
            _siteService = siteService;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                var siteRouter = new SiteRouter();
                var host = context.Request.Host.Host;
                
                _logger.LogDebug("处理请求: {Host}{Path}", host, context.Request.Path);

                if (siteRouter.IsSiteRequest(context))
                {
                    // 用户网站请求
                    var subdomain = siteRouter.GetSubdomain(host);
                    _logger.LogDebug("检测到网站请求: {Subdomain}", subdomain);

                    var site = await _siteService.GetSiteByDomainAsync(subdomain);

                    if (site != null && site.IsPublished)
                    {
                        // 设置网站上下文
                        context.Items["IsSiteRequest"] = true;
                        context.Items["CurrentSite"] = site;
                        context.Items["SiteDomain"] = subdomain;
                        
                        _logger.LogInformation("网站请求成功: {SiteName} ({SiteId})", site.Name, site.Id);
                    }
                    else
                    {
                        // 网站不存在或未发布
                        _logger.LogWarning("网站不存在或未发布: {Subdomain}", subdomain);
                        context.Items["IsSiteRequest"] = true;
                        context.Items["SiteNotFound"] = true;
                        context.Items["SiteDomain"] = subdomain;
                    }
                }
                else
                {
                    // 平台请求
                    context.Items["IsSiteRequest"] = false;
                    _logger.LogDebug("平台请求: {Host}", host);
                }

                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "路由中间件处理异常: {Host}{Path}", 
                    context.Request.Host.Host, context.Request.Path);
                
                // 发生异常时，默认作为平台请求处理
                context.Items["IsSiteRequest"] = false;
                await _next(context);
            }
        }
    }

    /// <summary>
    /// 网站路由器 - 判断请求类型
    /// </summary>
    public class SiteRouter
    {
        private readonly string[] _platformSubdomains = { "www", "api", "admin", "cdn", "static" };

        /// <summary>
        /// 判断是否为用户网站请求
        /// </summary>
        public bool IsSiteRequest(HttpContext context)
        {
            var host = context.Request.Host.Host;
            var subdomain = GetSubdomain(host);
            
            // 没有子域名或是平台保留子域名，则为平台请求
            if (string.IsNullOrEmpty(subdomain) || _platformSubdomains.Contains(subdomain.ToLower()))
            {
                return false;
            }
            
            // 其他子域名视为用户网站请求
            return true;
        }

        /// <summary>
        /// 提取子域名
        /// </summary>
        public string GetSubdomain(string host)
        {
            if (string.IsNullOrEmpty(host))
                return string.Empty;

            // 移除端口号
            var hostWithoutPort = host.Split(':')[0];
            
            // 分割域名部分
            var parts = hostWithoutPort.Split('.');
            
            // 如果只有一个部分（如localhost）或两个部分（如molysite.com），则没有子域名
            if (parts.Length <= 2)
                return string.Empty;
            
            // 返回第一个部分作为子域名
            return parts[0];
        }
    }
}
