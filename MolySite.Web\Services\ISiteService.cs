using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MolySite.Shared.Dtos;

namespace MolySite.Web.Services
{
    /// <summary>
    /// 网站管理服务接口
    /// </summary>
    public interface ISiteService
    {
        /// <summary>
        /// 获取当前用户的所有网站
        /// </summary>
        /// <returns>网站列表</returns>
        Task<List<SiteDto>> GetMySitesAsync();

        /// <summary>
        /// 获取所有网站（管理员权限）
        /// </summary>
        /// <returns>网站列表</returns>
        Task<List<SiteDto>> GetAllSitesAsync();

        /// <summary>
        /// 获取指定网站信息
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>网站信息</returns>
        Task<SiteDto?> GetSiteAsync(Guid siteId);

        /// <summary>
        /// 根据ID获取网站信息
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>网站信息</returns>
        Task<SiteDto?> GetSiteByIdAsync(Guid siteId);

        /// <summary>
        /// 根据域名获取网站信息
        /// </summary>
        /// <param name="domain">域名</param>
        /// <returns>网站信息</returns>
        Task<SiteDto?> GetSiteByDomainAsync(string domain);

        /// <summary>
        /// 创建新网站
        /// </summary>
        /// <param name="createSiteDto">创建网站DTO</param>
        /// <returns>创建的网站信息</returns>
        Task<SiteDto?> CreateSiteAsync(CreateSiteDto createSiteDto);

        /// <summary>
        /// 更新网站信息
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <param name="updateSiteDto">更新网站DTO</param>
        /// <returns>更新后的网站信息</returns>
        Task<SiteDto?> UpdateSiteAsync(Guid siteId, UpdateSiteDto updateSiteDto);

        /// <summary>
        /// 删除网站
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteSiteAsync(Guid siteId);

        /// <summary>
        /// 检查域名是否可用
        /// </summary>
        /// <param name="domain">域名</param>
        /// <returns>是否可用</returns>
        Task<bool> IsDomainAvailableAsync(string domain);
    }
}
