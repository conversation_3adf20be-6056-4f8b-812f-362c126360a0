@* 统一Dashboard入口页面 *@
@page "/dashboard"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using MolySite.Web.Services
@using MolySite.Web.Components.Pages.Dashboard.Components
@using MolySite.Shared.Dtos
@using MolySite.Web.Components.Layout
@layout DashboardLayout
@attribute [Authorize]
@inject AuthenticationStateProvider AuthStateProvider
@inject IPermissionService PermissionService
@inject ISiteService SiteService
@inject IUserService UserService
@inject ILogger<Index> Logger

<PageTitle>管理中心 - MolySite</PageTitle>

@if (_loading)
{
    <div class="flex items-center justify-center min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div class="text-center">
            <div class="loading-spinner h-16 w-16 mx-auto mb-4"></div>
            <p class="text-gray-600 font-medium">正在加载管理中心...</p>
        </div>
    </div>
}
else
{
    <div class="space-y-8">
            <!-- 现代化欢迎横幅 -->
            <div class="relative overflow-hidden bg-gradient-to-r @GetWelcomeBannerGradient() rounded-2xl shadow-xl">
                <div class="absolute inset-0 bg-black/10"></div>
                <div class="relative px-8 py-12">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="flex items-center space-x-3 mb-4">
                                <div class="@GetRoleIconClass() p-3 rounded-xl shadow-lg">
                                    <i class="@GetRoleIcon() text-white text-2xl"></i>
                                </div>
                                <div>
                                    <h1 class="text-3xl font-bold text-white mb-1">
                                        欢迎回来, @_userName!
                                    </h1>
                                    <p class="text-white/80 text-lg">@GetWelcomeMessage()</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4 text-white/90">
                                <div class="flex items-center space-x-2">
                                    <i class="bi-calendar3 text-sm"></i>
                                    <span class="text-sm">@DateTime.Now.ToString("yyyy年MM月dd日")</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <i class="bi-clock text-sm"></i>
                                    <span class="text-sm">@DateTime.Now.ToString("HH:mm")</span>
                                </div>
                            </div>
                        </div>
                        <div class="hidden lg:block">
                            <div class="w-32 h-32 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm">
                                <i class="@GetRoleIcon() text-white text-4xl"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 装饰性元素 -->
                <div class="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full"></div>
                <div class="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full"></div>
            </div>

            <!-- 现代化统计面板 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach (var stat in _statistics)
                {
                    <div class="bg-white/70 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-white/20">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600 mb-1">@stat.Label</p>
                                <p class="text-3xl font-bold @GetStatColor(stat.Color)">@stat.Value</p>
                            </div>
                            <div class="@GetStatIconBg(stat.Color) p-3 rounded-xl">
                                <i class="@stat.Icon text-white text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4 flex items-center text-sm">
                            <i class="bi-arrow-up text-green-500 mr-1"></i>
                            <span class="text-green-600 font-medium">+12%</span>
                            <span class="text-gray-500 ml-1">较上月</span>
                        </div>
                    </div>
                }
            </div>

            <!-- 现代化快速操作卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach (var card in _quickActionCards)
                {
                    <div class="group bg-white/70 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-white/20 hover:scale-105">
                        <div class="flex items-start justify-between mb-4">
                            <div class="@GetCardIconBg(card.Color) p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                                <i class="@card.Icon text-white text-xl"></i>
                            </div>
                            <i class="bi-arrow-right text-gray-400 group-hover:text-gray-600 transition-colors"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900 mb-2">@card.Title</h3>
                        <p class="text-gray-600 text-sm mb-4 leading-relaxed">@card.Description</p>
                        <a href="@card.Link" class="inline-flex items-center space-x-2 @GetCardButtonClass(card.Color) px-4 py-2 rounded-lg font-medium transition-all duration-200">
                            <span>@card.ButtonText</span>
                            <i class="bi-arrow-right text-sm"></i>
                        </a>
                    </div>
                }
            </div>

            <!-- 现代化最近活动和系统状态 -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 最近活动 -->
                <div class="lg:col-span-2 bg-white/70 backdrop-blur-sm rounded-2xl shadow-lg p-6 border border-white/20">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-bold text-gray-900 flex items-center">
                            <i class="bi-clock-history mr-2 text-blue-600"></i>
                            最近活动
                        </h2>
                        <button class="text-blue-600 hover:text-blue-700 text-sm font-medium">查看全部</button>
                    </div>

                    @if (_recentActivities.Any())
                    {
                        <div class="space-y-4">
                            @foreach (var activity in _recentActivities.Take(5))
                            {
                                <div class="flex items-start space-x-4 p-3 rounded-xl hover:bg-gray-50/50 transition-colors">
                                    <div class="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <i class="@activity.Icon text-blue-600"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900">@activity.Description</p>
                                        <p class="text-xs text-gray-500 mt-1">@activity.Time.ToString("MM月dd日 HH:mm")</p>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            完成
                                        </span>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-12">
                            <i class="bi-inbox text-4xl text-gray-300 mb-4"></i>
                            <p class="text-gray-500">暂无最近活动</p>
                        </div>
                    }
                </div>

                <!-- 系统状态 -->
                <div class="bg-white/70 backdrop-blur-sm rounded-2xl shadow-lg p-6 border border-white/20">
                    <h3 class="text-lg font-bold text-gray-900 mb-6 flex items-center">
                        <i class="bi-activity mr-2 text-green-600"></i>
                        系统状态
                    </h3>

                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">服务器状态</span>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                <span class="text-sm font-medium text-green-600">正常</span>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">数据库</span>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                <span class="text-sm font-medium text-green-600">正常</span>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">存储空间</span>
                            <span class="text-sm font-medium text-gray-900">78%</span>
                        </div>

                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: 78%"></div>
                        </div>

                        <div class="pt-4 border-t border-gray-100">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">在线用户</span>
                                <span class="font-medium text-gray-900">@GetOnlineUsersCount()</span>
                            </div>
                        </div>
                    </div>
                </div>
        </div>
    </div>
}

@code {
    private bool _loading = true;
    private string _userRole = "";
    private string _userName = "";
    private List<StatisticsPanel.StatisticItem> _statistics = new();
    private List<QuickActionCard> _quickActionCards = new();
    private List<RecentActivity> _recentActivities = new();

    protected override async Task OnInitializedAsync()
    {
        try
        {
            await LoadUserInfoAsync();
            await LoadStatisticsAsync();
            await LoadQuickActionsAsync();
            await LoadRecentActivitiesAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "初始化Dashboard时发生错误");
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task LoadUserInfoAsync()
    {
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        
        if (user?.Identity?.IsAuthenticated == true)
        {
            _userName = user.Identity.Name ?? "用户";
            _userRole = PermissionService.GetUserRole(user);
        }
    }

    private async Task LoadStatisticsAsync()
    {
        _statistics.Clear();

        if (PermissionService.HasPlatformPermission(await GetCurrentUserAsync(), PlatformPermissions.ViewStatistics))
        {
            // SuperAdmin统计
            _statistics.AddRange(new[]
            {
                new StatisticsPanel.StatisticItem { Icon = "bi-people", Label = "总用户数", Value = "0", Color = "blue" },
                new StatisticsPanel.StatisticItem { Icon = "bi-building", Label = "总站点数", Value = "0", Color = "green" },
                new StatisticsPanel.StatisticItem { Icon = "bi-graph-up", Label = "活跃站点", Value = "0", Color = "purple" },
                new StatisticsPanel.StatisticItem { Icon = "bi-currency-dollar", Label = "月收入", Value = "¥0", Color = "yellow" }
            });
        }
        else if (_userRole == UserRoles.User)
        {
            // 普通用户统计
            var mySites = await SiteService.GetMySitesAsync();
            _statistics.AddRange(new[]
            {
                new StatisticsPanel.StatisticItem { Icon = "bi-house", Label = "我的站点", Value = mySites.Count.ToString(), Color = "green" },
                new StatisticsPanel.StatisticItem { Icon = "bi-heart", Label = "关注的站点", Value = "0", Color = "red" },
                new StatisticsPanel.StatisticItem { Icon = "bi-eye", Label = "总访问量", Value = "0", Color = "blue" },
                new StatisticsPanel.StatisticItem { Icon = "bi-people", Label = "关注者", Value = "0", Color = "purple" }
            });
        }
        else if (_userRole == UserRoles.SiteEditor)
        {
            // SiteEditor统计
            _statistics.AddRange(new[]
            {
                new StatisticsPanel.StatisticItem { Icon = "bi-file-text", Label = "我的内容", Value = "0", Color = "purple" },
                new StatisticsPanel.StatisticItem { Icon = "bi-clock", Label = "待审核", Value = "0", Color = "yellow" },
                new StatisticsPanel.StatisticItem { Icon = "bi-check-circle", Label = "已发布", Value = "0", Color = "green" },
                new StatisticsPanel.StatisticItem { Icon = "bi-images", Label = "媒体文件", Value = "0", Color = "blue" }
            });
        }
    }

    private async Task LoadQuickActionsAsync()
    {
        _quickActionCards.Clear();
        var user = await GetCurrentUserAsync();

        if (PermissionService.HasPlatformPermission(user, PlatformPermissions.ViewAllUsers))
        {
            _quickActionCards.Add(new QuickActionCard
            {
                Icon = "bi-people",
                Title = "用户管理",
                Description = "管理系统中的所有用户",
                Link = "/dashboard/users",
                Color = "primary",
                ButtonText = "管理用户"
            });
        }

        if (PermissionService.HasPlatformPermission(user, PlatformPermissions.ViewAllSites))
        {
            _quickActionCards.Add(new QuickActionCard
            {
                Icon = "bi-building",
                Title = "站点管理",
                Description = "管理系统中的所有站点",
                Link = "/dashboard/sites",
                Color = "success",
                ButtonText = "管理站点"
            });
        }

        if (_userRole == UserRoles.User || _userRole == UserRoles.SuperAdmin)
        {
            _quickActionCards.Add(new QuickActionCard
            {
                Icon = "bi-plus-circle",
                Title = "创建站点",
                Description = "创建一个新的网站",
                Link = "/dashboard/sites/create",
                Color = "info",
                ButtonText = "立即创建"
            });
        }

        if (_userRole == UserRoles.User)
        {
            _quickActionCards.Add(new QuickActionCard
            {
                Icon = "bi-heart",
                Title = "关注的站点",
                Description = "查看和管理您关注的网站",
                Link = "/dashboard/followed-sites",
                Color = "red",
                ButtonText = "查看关注"
            });
        }

        if (_userRole == UserRoles.SiteEditor || _userRole == UserRoles.User)
        {
            _quickActionCards.Add(new QuickActionCard
            {
                Icon = "bi-file-text",
                Title = "内容管理",
                Description = "管理网站内容和文章",
                Link = "/dashboard/content",
                Color = "purple",
                ButtonText = "管理内容"
            });
        }
    }

    private async Task LoadRecentActivitiesAsync()
    {
        // TODO: 实现最近活动加载
        _recentActivities = new List<RecentActivity>();
    }

    private async Task<System.Security.Claims.ClaimsPrincipal> GetCurrentUserAsync()
    {
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        return authState.User;
    }

    private string GetWelcomeMessage()
    {
        return _userRole switch
        {
            UserRoles.SuperAdmin => "您拥有平台管理权限，可以管理用户账户和平台设置，但不能访问用户网站隐私内容。",
            UserRoles.User => "您可以创建和管理自己的站点，关注其他网站，邀请编辑者协作。",
            UserRoles.SiteEditor => "您可以编辑被授权的站点内容，创建精彩的内容。",
            UserRoles.SiteFollower => "您可以关注感兴趣的网站，接收更新通知。",
            _ => "欢迎使用MolySite管理中心。"
        };
    }

    private string GetWelcomeBannerGradient()
    {
        return _userRole switch
        {
            UserRoles.SuperAdmin => "from-blue-600 via-blue-700 to-blue-800",
            UserRoles.User => "from-green-600 via-green-700 to-green-800",
            UserRoles.SiteEditor => "from-purple-600 via-purple-700 to-purple-800",
            UserRoles.SiteFollower => "from-pink-600 via-pink-700 to-pink-800",
            _ => "from-gray-600 via-gray-700 to-gray-800"
        };
    }

    private string GetRoleIconClass()
    {
        return _userRole switch
        {
            UserRoles.SuperAdmin => "bg-blue-500",
            UserRoles.User => "bg-green-500",
            UserRoles.SiteEditor => "bg-purple-500",
            UserRoles.SiteFollower => "bg-pink-500",
            _ => "bg-gray-500"
        };
    }

    private string GetRoleIcon()
    {
        return _userRole switch
        {
            UserRoles.SuperAdmin => "bi-shield-check",
            UserRoles.User => "bi-person-circle",
            UserRoles.SiteEditor => "bi-pencil-square",
            UserRoles.SiteFollower => "bi-heart",
            _ => "bi-person"
        };
    }

    private string GetStatColor(string color)
    {
        return color switch
        {
            "blue" => "text-blue-600",
            "green" => "text-green-600",
            "purple" => "text-purple-600",
            "yellow" => "text-yellow-600",
            "indigo" => "text-indigo-600",
            _ => "text-gray-600"
        };
    }

    private string GetStatIconBg(string color)
    {
        return color switch
        {
            "blue" => "bg-gradient-to-br from-blue-500 to-blue-600",
            "green" => "bg-gradient-to-br from-green-500 to-green-600",
            "purple" => "bg-gradient-to-br from-purple-500 to-purple-600",
            "yellow" => "bg-gradient-to-br from-yellow-500 to-yellow-600",
            "indigo" => "bg-gradient-to-br from-indigo-500 to-indigo-600",
            _ => "bg-gradient-to-br from-gray-500 to-gray-600"
        };
    }

    private string GetCardIconBg(string color)
    {
        return color switch
        {
            "primary" => "bg-gradient-to-br from-blue-500 to-blue-600",
            "success" => "bg-gradient-to-br from-green-500 to-green-600",
            "info" => "bg-gradient-to-br from-cyan-500 to-cyan-600",
            "purple" => "bg-gradient-to-br from-purple-500 to-purple-600",
            _ => "bg-gradient-to-br from-gray-500 to-gray-600"
        };
    }

    private string GetCardButtonClass(string color)
    {
        return color switch
        {
            "primary" => "bg-blue-100 text-blue-700 hover:bg-blue-200",
            "success" => "bg-green-100 text-green-700 hover:bg-green-200",
            "info" => "bg-cyan-100 text-cyan-700 hover:bg-cyan-200",
            "purple" => "bg-purple-100 text-purple-700 hover:bg-purple-200",
            _ => "bg-gray-100 text-gray-700 hover:bg-gray-200"
        };
    }

    private string GetOnlineUsersCount()
    {
        // TODO: 实现在线用户统计
        return "42";
    }

    public class QuickActionCard
    {
        public string Icon { get; set; } = "";
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Link { get; set; } = "";
        public string Color { get; set; } = "primary";
        public string ButtonText { get; set; } = "访问";
    }

    public class RecentActivity
    {
        public string Icon { get; set; } = "";
        public string Description { get; set; } = "";
        public DateTime Time { get; set; }
    }
}
