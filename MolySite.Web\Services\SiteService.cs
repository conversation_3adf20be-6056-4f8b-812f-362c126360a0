using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MolySite.Shared.Dtos;

namespace MolySite.Web.Services
{
    /// <summary>
    /// 网站管理服务实现
    /// </summary>
    public class SiteService : ISiteService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<SiteService> _logger;

        public SiteService(HttpClient httpClient, ILogger<SiteService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<List<SiteDto>> GetMySitesAsync()
        {
            try
            {
                _logger.LogInformation("获取用户网站列表");
                
                var response = await _httpClient.GetAsync("api/sites/my-sites");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var sites = JsonSerializer.Deserialize<List<SiteDto>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    _logger.LogInformation("成功获取 {Count} 个网站", sites?.Count ?? 0);
                    return sites ?? new List<SiteDto>();
                }
                else
                {
                    _logger.LogWarning("获取网站列表失败: {StatusCode}", response.StatusCode);
                    return new List<SiteDto>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取网站列表时发生异常");
                return new List<SiteDto>();
            }
        }

        /// <inheritdoc/>
        public async Task<SiteDto?> GetSiteAsync(Guid siteId)
        {
            try
            {
                _logger.LogInformation("获取网站信息: {SiteId}", siteId);
                
                var response = await _httpClient.GetAsync($"api/sites/{siteId}");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var site = JsonSerializer.Deserialize<SiteDto>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    _logger.LogInformation("成功获取网站信息: {SiteName}", site?.Name);
                    return site;
                }
                else
                {
                    _logger.LogWarning("获取网站信息失败: {StatusCode}", response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取网站信息时发生异常: {SiteId}", siteId);
                return null;
            }
        }

        /// <inheritdoc/>
        public async Task<List<SiteDto>> GetAllSitesAsync()
        {
            try
            {
                _logger.LogInformation("获取所有网站列表");

                var response = await _httpClient.GetAsync("api/sites/all");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var sites = JsonSerializer.Deserialize<List<SiteDto>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("成功获取 {Count} 个网站", sites?.Count ?? 0);
                    return sites ?? new List<SiteDto>();
                }
                else
                {
                    _logger.LogWarning("获取所有网站列表失败: {StatusCode}", response.StatusCode);
                    return new List<SiteDto>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有网站列表时发生异常");
                return new List<SiteDto>();
            }
        }

        /// <inheritdoc/>
        public async Task<SiteDto?> GetSiteByIdAsync(Guid siteId)
        {
            // 复用GetSiteAsync方法
            return await GetSiteAsync(siteId);
        }

        /// <inheritdoc/>
        public async Task<SiteDto?> GetSiteByDomainAsync(string domain)
        {
            try
            {
                _logger.LogInformation("根据域名获取网站信息: {Domain}", domain);

                var response = await _httpClient.GetAsync($"api/sites/by-domain/{Uri.EscapeDataString(domain)}");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var site = JsonSerializer.Deserialize<SiteDto>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("成功获取网站信息: {SiteName} ({Domain})", site?.Name, domain);
                    return site;
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    _logger.LogInformation("域名对应的网站不存在: {Domain}", domain);
                    return null;
                }
                else
                {
                    _logger.LogWarning("根据域名获取网站信息失败: {StatusCode}", response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据域名获取网站信息时发生异常: {Domain}", domain);
                return null;
            }
        }

        /// <inheritdoc/>
        public async Task<SiteDto?> CreateSiteAsync(CreateSiteDto createSiteDto)
        {
            try
            {
                _logger.LogInformation("创建网站: {SiteName}, {Domain}", createSiteDto.Name, createSiteDto.Domain);
                
                var response = await _httpClient.PostAsJsonAsync("api/sites", createSiteDto);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var site = JsonSerializer.Deserialize<SiteDto>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    _logger.LogInformation("成功创建网站: {SiteId}, {SiteName}", site?.Id, site?.Name);
                    return site;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning("创建网站失败: {StatusCode}, {Error}", response.StatusCode, errorContent);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建网站时发生异常: {SiteName}", createSiteDto.Name);
                return null;
            }
        }

        /// <inheritdoc/>
        public async Task<SiteDto?> UpdateSiteAsync(Guid siteId, UpdateSiteDto updateSiteDto)
        {
            try
            {
                _logger.LogInformation("更新网站: {SiteId}, {SiteName}", siteId, updateSiteDto.Name);
                
                var response = await _httpClient.PutAsJsonAsync($"api/sites/{siteId}", updateSiteDto);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var site = JsonSerializer.Deserialize<SiteDto>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    _logger.LogInformation("成功更新网站: {SiteId}, {SiteName}", site?.Id, site?.Name);
                    return site;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning("更新网站失败: {StatusCode}, {Error}", response.StatusCode, errorContent);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新网站时发生异常: {SiteId}", siteId);
                return null;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> DeleteSiteAsync(Guid siteId)
        {
            try
            {
                _logger.LogInformation("删除网站: {SiteId}", siteId);
                
                var response = await _httpClient.DeleteAsync($"api/sites/{siteId}");
                
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("成功删除网站: {SiteId}", siteId);
                    return true;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning("删除网站失败: {StatusCode}, {Error}", response.StatusCode, errorContent);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除网站时发生异常: {SiteId}", siteId);
                return false;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> IsDomainAvailableAsync(string domain)
        {
            try
            {
                _logger.LogInformation("检查域名可用性: {Domain}", domain);
                
                var response = await _httpClient.GetAsync($"api/sites/check-domain?domain={Uri.EscapeDataString(domain)}");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<JsonElement>(content);
                    
                    if (result.TryGetProperty("available", out var availableElement))
                    {
                        var isAvailable = availableElement.GetBoolean();
                        _logger.LogInformation("域名 {Domain} 可用性: {Available}", domain, isAvailable);
                        return isAvailable;
                    }
                }
                
                _logger.LogWarning("检查域名可用性失败: {StatusCode}", response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查域名可用性时发生异常: {Domain}", domain);
                return false;
            }
        }
    }
}
