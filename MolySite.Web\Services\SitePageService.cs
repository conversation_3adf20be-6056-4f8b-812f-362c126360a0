using MolySite.Shared.Dtos;
using System.Text.Json;

namespace MolySite.Web.Services
{
    /// <summary>
    /// 网站页面服务实现
    /// </summary>
    public class SitePageService : ISitePageService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<SitePageService> _logger;

        public SitePageService(HttpClient httpClient, ILogger<SitePageService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        public async Task<SitePageDto?> GetHomePageAsync(Guid siteId)
        {
            try
            {
                _logger.LogDebug("获取网站首页: {SiteId}", siteId);
                
                // 临时实现 - 返回默认首页内容
                // 第3周将实现真正的API调用
                return new SitePageDto
                {
                    Id = Guid.NewGuid(),
                    SiteId = siteId,
                    Title = "欢迎访问我们的网站",
                    Slug = "",
                    Content = GetDefaultHomeContent(),
                    Type = PageType.Home,
                    IsPublished = true,
                    SortOrder = 0,
                    CreatedAt = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取网站首页失败: {SiteId}", siteId);
                return null;
            }
        }

        public async Task<SitePageDto?> GetPageBySlugAsync(Guid siteId, string slug)
        {
            try
            {
                _logger.LogDebug("获取网站页面: {SiteId}, {Slug}", siteId, slug);
                
                // 临时实现 - 根据slug返回默认内容
                // 第3周将实现真正的API调用
                return slug.ToLower() switch
                {
                    "about" => new SitePageDto
                    {
                        Id = Guid.NewGuid(),
                        SiteId = siteId,
                        Title = "关于我们",
                        Slug = "about",
                        Content = GetDefaultAboutContent(),
                        Type = PageType.About,
                        IsPublished = true,
                        SortOrder = 1,
                        CreatedAt = DateTime.UtcNow
                    },
                    "services" => new SitePageDto
                    {
                        Id = Guid.NewGuid(),
                        SiteId = siteId,
                        Title = "我们的服务",
                        Slug = "services",
                        Content = GetDefaultServicesContent(),
                        Type = PageType.Services,
                        IsPublished = true,
                        SortOrder = 2,
                        CreatedAt = DateTime.UtcNow
                    },
                    "contact" => new SitePageDto
                    {
                        Id = Guid.NewGuid(),
                        SiteId = siteId,
                        Title = "联系我们",
                        Slug = "contact",
                        Content = GetDefaultContactContent(),
                        Type = PageType.Contact,
                        IsPublished = true,
                        SortOrder = 3,
                        CreatedAt = DateTime.UtcNow
                    },
                    _ => null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取网站页面失败: {SiteId}, {Slug}", siteId, slug);
                return null;
            }
        }

        public async Task<List<SitePageDto>> GetPublishedPagesAsync(Guid siteId)
        {
            try
            {
                _logger.LogDebug("获取网站已发布页面: {SiteId}", siteId);
                
                // 临时实现 - 返回默认页面列表
                // 第3周将实现真正的API调用
                return new List<SitePageDto>
                {
                    new SitePageDto
                    {
                        Id = Guid.NewGuid(),
                        SiteId = siteId,
                        Title = "首页",
                        Slug = "",
                        Type = PageType.Home,
                        IsPublished = true,
                        SortOrder = 0,
                        CreatedAt = DateTime.UtcNow
                    },
                    new SitePageDto
                    {
                        Id = Guid.NewGuid(),
                        SiteId = siteId,
                        Title = "关于我们",
                        Slug = "about",
                        Type = PageType.About,
                        IsPublished = true,
                        SortOrder = 1,
                        CreatedAt = DateTime.UtcNow
                    },
                    new SitePageDto
                    {
                        Id = Guid.NewGuid(),
                        SiteId = siteId,
                        Title = "我们的服务",
                        Slug = "services",
                        Type = PageType.Services,
                        IsPublished = true,
                        SortOrder = 2,
                        CreatedAt = DateTime.UtcNow
                    },
                    new SitePageDto
                    {
                        Id = Guid.NewGuid(),
                        SiteId = siteId,
                        Title = "联系我们",
                        Slug = "contact",
                        Type = PageType.Contact,
                        IsPublished = true,
                        SortOrder = 3,
                        CreatedAt = DateTime.UtcNow
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取网站已发布页面失败: {SiteId}", siteId);
                return new List<SitePageDto>();
            }
        }

        // 以下方法在第3-4周实现
        public Task<List<SitePageDto>> GetAllPagesAsync(Guid siteId)
        {
            throw new NotImplementedException("第3周实现");
        }

        public Task<SitePageDto?> CreatePageAsync(CreateSitePageDto createDto)
        {
            throw new NotImplementedException("第4周实现");
        }

        public Task<SitePageDto?> UpdatePageAsync(Guid pageId, UpdateSitePageDto updateDto)
        {
            throw new NotImplementedException("第4周实现");
        }

        public Task<bool> DeletePageAsync(Guid pageId)
        {
            throw new NotImplementedException("第4周实现");
        }

        public Task<bool> PublishPageAsync(Guid pageId)
        {
            throw new NotImplementedException("第4周实现");
        }

        public Task<bool> UnpublishPageAsync(Guid pageId)
        {
            throw new NotImplementedException("第4周实现");
        }

        // 默认内容模板
        private string GetDefaultHomeContent()
        {
            return @"
                <div class='text-center py-16'>
                    <h2 class='text-4xl font-bold text-gray-900 mb-6'>欢迎访问我们的网站</h2>
                    <p class='text-xl text-gray-600 mb-8'>这是一个使用 MolySite 创建的专业网站</p>
                    <div class='space-x-4'>
                        <a href='/about' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors'>了解更多</a>
                        <a href='/contact' class='bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors'>联系我们</a>
                    </div>
                </div>
            ";
        }

        private string GetDefaultAboutContent()
        {
            return @"
                <div class='prose max-w-none'>
                    <h2>关于我们</h2>
                    <p>我们是一家专业的公司，致力于为客户提供优质的服务。</p>
                    <h3>我们的使命</h3>
                    <p>通过创新的解决方案，帮助客户实现业务目标。</p>
                    <h3>我们的团队</h3>
                    <p>我们拥有一支经验丰富、充满激情的专业团队。</p>
                </div>
            ";
        }

        private string GetDefaultServicesContent()
        {
            return @"
                <div class='prose max-w-none'>
                    <h2>我们的服务</h2>
                    <div class='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-8'>
                        <div class='bg-white p-6 rounded-lg shadow-md'>
                            <h3 class='text-xl font-semibold mb-4'>专业咨询</h3>
                            <p>为您提供专业的咨询服务，帮助您做出明智的决策。</p>
                        </div>
                        <div class='bg-white p-6 rounded-lg shadow-md'>
                            <h3 class='text-xl font-semibold mb-4'>技术支持</h3>
                            <p>提供全面的技术支持，确保您的业务顺利运行。</p>
                        </div>
                        <div class='bg-white p-6 rounded-lg shadow-md'>
                            <h3 class='text-xl font-semibold mb-4'>定制解决方案</h3>
                            <p>根据您的具体需求，提供量身定制的解决方案。</p>
                        </div>
                    </div>
                </div>
            ";
        }

        private string GetDefaultContactContent()
        {
            return @"
                <div class='prose max-w-none'>
                    <h2>联系我们</h2>
                    <div class='grid grid-cols-1 md:grid-cols-2 gap-8 mt-8'>
                        <div>
                            <h3>联系信息</h3>
                            <div class='space-y-4'>
                                <div>
                                    <strong>地址：</strong>
                                    <p>北京市朝阳区xxx街道xxx号</p>
                                </div>
                                <div>
                                    <strong>电话：</strong>
                                    <p>+86 138-0000-0000</p>
                                </div>
                                <div>
                                    <strong>邮箱：</strong>
                                    <p><EMAIL></p>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h3>营业时间</h3>
                            <div class='space-y-2'>
                                <p><strong>周一至周五：</strong> 9:00 - 18:00</p>
                                <p><strong>周六：</strong> 9:00 - 17:00</p>
                                <p><strong>周日：</strong> 休息</p>
                            </div>
                        </div>
                    </div>
                </div>
            ";
        }
    }
}
