namespace MolySite.Core.Constants
{
    /// <summary>
    /// 平台权限常量
    /// </summary>
    public static class PlatformPermissions
    {
        /// <summary>
        /// 超级管理员权限
        /// </summary>
        public const string SuperAdmin = "Platform.SuperAdmin";

        /// <summary>
        /// 管理用户账户基本信息（非隐私）
        /// </summary>
        public const string ManageUserAccounts = "Platform.ManageUserAccounts";

        /// <summary>
        /// 查看用户列表
        /// </summary>
        public const string ViewUserList = "Platform.ViewUserList";

        /// <summary>
        /// 查看网站列表（基本信息，不包含内容）
        /// </summary>
        public const string ViewSiteList = "Platform.ViewSiteList";

        /// <summary>
        /// 管理订阅计划
        /// </summary>
        public const string ManageSubscriptionPlans = "Platform.ManageSubscriptionPlans";

        /// <summary>
        /// 查看平台统计
        /// </summary>
        public const string ViewPlatformStatistics = "Platform.ViewStatistics";

        /// <summary>
        /// 管理平台设置
        /// </summary>
        public const string ManagePlatformSettings = "Platform.ManageSettings";

        /// <summary>
        /// 创建网站（User权限）
        /// </summary>
        public const string CreateSite = "Platform.CreateSite";

        /// <summary>
        /// 暂停/恢复用户账户
        /// </summary>
        public const string SuspendUserAccount = "Platform.SuspendUserAccount";

        /// <summary>
        /// 暂停/恢复网站（不涉及内容访问）
        /// </summary>
        public const string SuspendSite = "Platform.SuspendSite";

        /// <summary>
        /// 获取所有平台权限
        /// </summary>
        public static List<string> GetAllPermissions()
        {
            return new List<string>
            {
                SuperAdmin,
                ManageUserAccounts,
                ViewUserList,
                ViewSiteList,
                ManageSubscriptionPlans,
                ViewPlatformStatistics,
                ManagePlatformSettings,
                CreateSite,
                SuspendUserAccount,
                SuspendSite
            };
        }

        /// <summary>
        /// 获取超级管理员权限
        /// </summary>
        public static List<string> GetSuperAdminPermissions()
        {
            return GetAllPermissions();
        }

        /// <summary>
        /// 获取普通用户权限
        /// </summary>
        public static List<string> GetUserPermissions()
        {
            return new List<string>
            {
                CreateSite
            };
        }
    }

    /// <summary>
    /// 网站权限常量
    /// </summary>
    public static class SitePermissions
    {
        /// <summary>
        /// 查看网站设置
        /// </summary>
        public const string ViewSettings = "Site.ViewSettings";

        /// <summary>
        /// 编辑网站设置
        /// </summary>
        public const string EditSettings = "Site.EditSettings";

        /// <summary>
        /// 管理网站用户
        /// </summary>
        public const string ManageUsers = "Site.ManageUsers";

        /// <summary>
        /// 查看网站内容
        /// </summary>
        public const string ViewContent = "Site.ViewContent";

        /// <summary>
        /// 编辑网站内容
        /// </summary>
        public const string EditContent = "Site.EditContent";

        /// <summary>
        /// 发布网站
        /// </summary>
        public const string Publish = "Site.Publish";

        /// <summary>
        /// 查看网站统计
        /// </summary>
        public const string ViewStatistics = "Site.ViewStatistics";

        /// <summary>
        /// 管理媒体文件
        /// </summary>
        public const string ManageMedia = "Site.ManageMedia";

        /// <summary>
        /// 删除网站
        /// </summary>
        public const string DeleteSite = "Site.Delete";

        /// <summary>
        /// 管理网站订阅
        /// </summary>
        public const string ManageSubscription = "Site.ManageSubscription";

        /// <summary>
        /// 关注网站
        /// </summary>
        public const string FollowSite = "Site.Follow";

        /// <summary>
        /// 接收网站通知
        /// </summary>
        public const string ReceiveNotifications = "Site.ReceiveNotifications";

        /// <summary>
        /// 联系网站
        /// </summary>
        public const string ContactSite = "Site.Contact";

        /// <summary>
        /// 查看关注者专属内容
        /// </summary>
        public const string ViewFollowerContent = "Site.ViewFollowerContent";

        /// <summary>
        /// 获取所有网站权限
        /// </summary>
        public static List<string> GetAllPermissions()
        {
            return new List<string>
            {
                ViewSettings,
                EditSettings,
                ManageUsers,
                ViewContent,
                EditContent,
                Publish,
                ViewStatistics,
                ManageMedia,
                DeleteSite,
                ManageSubscription,
                FollowSite,
                ReceiveNotifications,
                ContactSite,
                ViewFollowerContent
            };
        }

        /// <summary>
        /// 获取网站所有者权限
        /// </summary>
        public static List<string> GetOwnerPermissions()
        {
            return GetAllPermissions();
        }

        /// <summary>
        /// 获取网站编辑者权限
        /// </summary>
        public static List<string> GetEditorPermissions()
        {
            return new List<string>
            {
                ViewSettings,
                ViewContent,
                EditContent,
                ViewStatistics,
                ManageMedia
            };
        }

        /// <summary>
        /// 获取网站关注者权限
        /// </summary>
        public static List<string> GetFollowerPermissions()
        {
            return new List<string>
            {
                FollowSite,
                ReceiveNotifications,
                ContactSite,
                ViewFollowerContent
            };
        }
    }

    /// <summary>
    /// 角色常量
    /// </summary>
    public static class Roles
    {
        /// <summary>
        /// 平台超级管理员
        /// </summary>
        public const string SuperAdmin = "SuperAdmin";

        /// <summary>
        /// 普通用户（平台级角色）
        /// </summary>
        public const string User = "User";

        /// <summary>
        /// 网站编辑者（仅网站级角色）
        /// </summary>
        public const string SiteEditor = "SiteEditor";

        /// <summary>
        /// 获取所有平台级角色
        /// </summary>
        public static List<string> GetPlatformRoles()
        {
            return new List<string>
            {
                SuperAdmin,
                User
            };
        }

        /// <summary>
        /// 获取网站级角色
        /// </summary>
        public static List<string> GetSiteRoles()
        {
            return new List<string>
            {
                SiteEditor  // 注意：这里的SiteEditor是网站级角色，不是平台级
            };
        }
    }
}
