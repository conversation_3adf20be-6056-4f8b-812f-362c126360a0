-- 简化角色体系数据库迁移脚本
-- 此脚本将清理不再需要的角色数据，并确保数据一致性

BEGIN;

-- 1. 清理 SiteUserRoles 表中的非 Owner 角色
-- 由于简化后只保留 Owner 角色，删除所有 Editor 和 Follower 角色
DELETE FROM "SiteUserRoles" 
WHERE "Role" != 1; -- 只保留 Owner (1)

-- 2. 移除邀请相关的列（如果存在）
-- 注意：这些列可能已经在模型中移除，但数据库中可能仍然存在
DO $$ 
BEGIN
    -- 检查并删除 InvitationToken 列
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'SiteUserRoles' 
        AND column_name = 'InvitationToken'
    ) THEN
        ALTER TABLE "SiteUserRoles" DROP COLUMN "InvitationToken";
    END IF;

    -- 检查并删除 InvitationExpiresAt 列
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'SiteUserRoles' 
        AND column_name = 'InvitationExpiresAt'
    ) THEN
        ALTER TABLE "SiteUserRoles" DROP COLUMN "InvitationExpiresAt";
    END IF;

    -- 检查并删除 InvitationStatus 列
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'SiteUserRoles' 
        AND column_name = 'InvitationStatus'
    ) THEN
        ALTER TABLE "SiteUserRoles" DROP COLUMN "InvitationStatus";
    END IF;
END $$;

-- 3. 移除邀请相关的索引（如果存在）
DO $$ 
BEGIN
    -- 删除 InvitationToken 索引
    IF EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'IX_SiteUserRoles_InvitationToken'
    ) THEN
        DROP INDEX "IX_SiteUserRoles_InvitationToken";
    END IF;
END $$;

-- 4. 更新用户角色，将 SiteOwner 角色统一为 User
-- 这确保了向后兼容性
UPDATE "AspNetUsers" 
SET "PlatformRole" = 2 -- User = 2
WHERE "PlatformRole" = 3; -- 如果有 SiteOwner = 3，改为 User

-- 5. 清理 AspNetUserRoles 表中的旧角色
-- 删除不再使用的角色关联
DELETE FROM "AspNetUserRoles" 
WHERE "RoleId" IN (
    SELECT "Id" FROM "AspNetRoles" 
    WHERE "Name" IN ('SiteEditor', 'SiteFollower')
);

-- 6. 删除不再使用的角色定义
DELETE FROM "AspNetRoles" 
WHERE "Name" IN ('SiteEditor', 'SiteFollower');

-- 7. 确保所有网站都有 Owner
-- 为没有 Owner 的网站分配创建者为 Owner
INSERT INTO "SiteUserRoles" ("Id", "UserId", "SiteId", "Role", "GrantedAt", "GrantedBy", "IsActive")
SELECT 
    gen_random_uuid(),
    s."CreatedBy",
    s."Id",
    1, -- Owner
    s."CreatedAt",
    s."CreatedBy",
    true
FROM "Sites" s
WHERE NOT EXISTS (
    SELECT 1 FROM "SiteUserRoles" sur 
    WHERE sur."SiteId" = s."Id" 
    AND sur."Role" = 1 
    AND sur."IsActive" = true
)
AND s."CreatedBy" IS NOT NULL;

-- 8. 数据完整性检查和修复
-- 确保所有 SiteUserRoles 记录都有有效的 GrantedBy
UPDATE "SiteUserRoles" 
SET "GrantedBy" = "UserId"
WHERE "GrantedBy" IS NULL;

-- 9. 优化索引
-- 重建主要索引以提高性能
REINDEX INDEX "IX_SiteUserRoles_UserId_SiteId";

-- 10. 更新统计信息
ANALYZE "SiteUserRoles";
ANALYZE "AspNetUsers";
ANALYZE "Sites";

-- 11. 验证数据完整性
-- 检查是否有孤立的 SiteUserRoles 记录
DO $$ 
DECLARE
    orphaned_count INTEGER;
BEGIN
    -- 检查孤立的用户角色记录
    SELECT COUNT(*) INTO orphaned_count
    FROM "SiteUserRoles" sur
    LEFT JOIN "AspNetUsers" u ON sur."UserId" = u."Id"
    LEFT JOIN "Sites" s ON sur."SiteId" = s."Id"
    WHERE u."Id" IS NULL OR s."Id" IS NULL;
    
    IF orphaned_count > 0 THEN
        RAISE NOTICE '发现 % 条孤立的 SiteUserRoles 记录，正在清理...', orphaned_count;
        
        -- 删除孤立记录
        DELETE FROM "SiteUserRoles" 
        WHERE "UserId" NOT IN (SELECT "Id" FROM "AspNetUsers")
        OR "SiteId" NOT IN (SELECT "Id" FROM "Sites");
    END IF;
END $$;

COMMIT;

-- 输出迁移结果统计
SELECT 
    'SiteUserRoles' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN "Role" = 1 THEN 1 END) as owner_count,
    COUNT(CASE WHEN "IsActive" = true THEN 1 END) as active_count
FROM "SiteUserRoles"
UNION ALL
SELECT 
    'Sites' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN "IsActive" = true THEN 1 END) as active_count,
    COUNT(CASE WHEN "IsPublished" = true THEN 1 END) as published_count
FROM "Sites"
UNION ALL
SELECT 
    'AspNetUsers' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN "PlatformRole" = 1 THEN 1 END) as superadmin_count,
    COUNT(CASE WHEN "PlatformRole" = 2 THEN 1 END) as user_count
FROM "AspNetUsers";
