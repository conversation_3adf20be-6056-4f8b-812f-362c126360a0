/* _content/MolySite.Web/Components/Layout/MainLayout.razor.rz.scp.css */
/* MainLayout 现代化样式 - 专门用于开发测试页面 */

/* 全局样式 */
.main-layout[b-7mhs7g97z7] {
    min-height: 100vh;
    display: flex;
    background: #f8fafc;
}

/* 侧边栏样式 */
.sidebar[b-7mhs7g97z7] {
    width: 256px;
    background: white;
    border-right: 1px solid #e2e8f0;
    transition: transform 0.3s ease;
}

.sidebar-mobile[b-7mhs7g97z7] {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 50;
    transform: translateX(-100%);
}

.sidebar-mobile.open[b-7mhs7g97z7] {
    transform: translateX(0);
}

/* 主内容区域 */
.main-content[b-7mhs7g97z7] {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.top-navbar[b-7mhs7g97z7] {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 30;
}

/* 品牌Logo */
.brand-logo[b-7mhs7g97z7] {
    transition: transform 0.3s ease;
}

.brand-logo:hover[b-7mhs7g97z7] {
    transform: scale(1.05);
}

/* 按钮样式 */
.btn-icon[b-7mhs7g97z7] {
    @apply inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 transition-colors duration-200;
}

.btn-primary[b-7mhs7g97z7] {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200;
}

.btn-secondary[b-7mhs7g97z7] {
    @apply inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200;
}

/* 按钮样式增强 */
.btn-primary[b-7mhs7g97z7] {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border: none;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
    transition: all 0.3s ease;
}

.btn-primary:hover[b-7mhs7g97z7] {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-secondary[b-7mhs7g97z7] {
    background: white;
    border: 1px solid #d1d5db;
    color: #374151;
    transition: all 0.3s ease;
}

.btn-secondary:hover[b-7mhs7g97z7] {
    background: #f9fafb;
    border-color: #9ca3af;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 移动端菜单动画 */
.mobile-menu[b-7mhs7g97z7] {
    transition: all 0.3s ease;
    transform-origin: top;
}

.mobile-menu.show[b-7mhs7g97z7] {
    animation: slideDown-b-7mhs7g97z7 0.3s ease-out;
}

.mobile-menu.hide[b-7mhs7g97z7] {
    animation: slideUp-b-7mhs7g97z7 0.3s ease-in;
}

@keyframes slideDown-b-7mhs7g97z7 {
    from {
        opacity: 0;
        transform: scaleY(0);
    }
    to {
        opacity: 1;
        transform: scaleY(1);
    }
}

@keyframes slideUp-b-7mhs7g97z7 {
    from {
        opacity: 1;
        transform: scaleY(1);
    }
    to {
        opacity: 0;
        transform: scaleY(0);
    }
}

/* 主内容区域 */
main[b-7mhs7g97z7] {
    flex: 1;
    min-height: calc(100vh - 64px - 120px); /* 减去导航栏和页脚高度 */
}

/* 页脚样式 */
footer[b-7mhs7g97z7] {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border-top: 1px solid #e2e8f0;
}

/* 错误提示样式 */
#blazor-error-ui[b-7mhs7g97z7] {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    animation: slideInUp-b-7mhs7g97z7 0.3s ease-out;
}

@keyframes slideInUp-b-7mhs7g97z7 {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .navbar[b-7mhs7g97z7] {
        padding: 0.5rem 1rem;
    }
    
    .brand-logo[b-7mhs7g97z7] {
        font-size: 1.25rem;
    }
    
    .mobile-menu[b-7mhs7g97z7] {
        margin-top: 0.5rem;
        padding-top: 0.5rem;
        border-top: 1px solid #e5e7eb;
    }
}

/* 平滑滚动 */
html[b-7mhs7g97z7] {
    scroll-behavior: smooth;
}

/* 焦点样式 */
button:focus[b-7mhs7g97z7],
a:focus[b-7mhs7g97z7] {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* 加载状态 */
.loading[b-7mhs7g97z7] {
    opacity: 0.6;
    pointer-events: none;
}

/* 工具提示 */
[data-tooltip][b-7mhs7g97z7] {
    position: relative;
}

[data-tooltip][b-7mhs7g97z7]::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #1f2937;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s;
    z-index: 1000;
}

[data-tooltip]:hover[b-7mhs7g97z7]::before {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-4px);
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .navbar[b-7mhs7g97z7] {
        background-color: rgba(17, 24, 39, 0.95);
        border-bottom-color: #374151;
    }
    
    .nav-link[b-7mhs7g97z7] {
        color: #d1d5db;
    }
    
    .nav-link:hover[b-7mhs7g97z7] {
        color: #f9fafb;
    }
    
    footer[b-7mhs7g97z7] {
        background: linear-gradient(135deg, #1f2937, #111827);
        border-top-color: #374151;
    }
}

/* 打印样式 */
@media print {
    .navbar[b-7mhs7g97z7],
    footer[b-7mhs7g97z7],
    #blazor-error-ui[b-7mhs7g97z7] {
        display: none !important;
    }
    
    main[b-7mhs7g97z7] {
        min-height: auto;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .btn-primary[b-7mhs7g97z7] {
        background: #000;
        color: #fff;
        border: 2px solid #000;
    }
    
    .btn-secondary[b-7mhs7g97z7] {
        background: #fff;
        color: #000;
        border: 2px solid #000;
    }
    
    .nav-link[b-7mhs7g97z7] {
        color: #000;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    *[b-7mhs7g97z7],
    *[b-7mhs7g97z7]::before,
    *[b-7mhs7g97z7]::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
/* _content/MolySite.Web/Components/Layout/NavMenu.razor.rz.scp.css */
/* NavMenu 样式 - 开发导航菜单 */

.nav-menu[b-rkm042n77k] {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: white;
    border-right: 1px solid #e2e8f0;
    width: 250px;
}

.nav-header[b-rkm042n77k] {
    padding: 1rem;
    border-bottom: 1px solid #f1f5f9;
}

.nav-brand[b-rkm042n77k] {
    display: flex;
    align-items: center;
    font-size: 1.125rem;
    font-weight: 600;
}

.nav-items[b-rkm042n77k] {
    flex: 1;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    overflow-y: auto;
}

.nav-section[b-rkm042n77k] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.nav-section-title[b-rkm042n77k] {
    font-size: 0.75rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.nav-links[b-rkm042n77k] {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.nav-link[b-rkm042n77k] {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #4b5563;
    border-radius: 0.5rem;
    transition: all 0.2s;
    text-decoration: none;
}

.nav-link:hover[b-rkm042n77k] {
    background: #f9fafb;
    color: #111827;
}

.nav-link.active[b-rkm042n77k] {
    background: #eff6ff;
    color: #1d4ed8;
    border-right: 2px solid #2563eb;
}

.nav-info[b-rkm042n77k] {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-item[b-rkm042n77k] {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
}

.info-label[b-rkm042n77k] {
    color: #6b7280;
}

.info-value[b-rkm042n77k] {
    color: #111827;
    font-weight: 500;
}

.nav-footer[b-rkm042n77k] {
    padding: 1rem;
    border-top: 1px solid #f1f5f9;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .nav-menu[b-rkm042n77k] {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        z-index: 50;
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
    }

    .nav-menu.open[b-rkm042n77k] {
        transform: translateX(0);
    }
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
    .nav-menu[b-rkm042n77k] {
        background: #111827;
        border-right-color: #374151;
    }

    .nav-brand[b-rkm042n77k] {
        color: white;
    }

    .nav-section-title[b-rkm042n77k] {
        color: #9ca3af;
    }

    .nav-link[b-rkm042n77k] {
        color: #d1d5db;
    }

    .nav-link:hover[b-rkm042n77k] {
        background: #1f2937;
        color: white;
    }

    .nav-link.active[b-rkm042n77k] {
        background: #1e3a8a;
        color: #93c5fd;
        border-right-color: #3b82f6;
    }

    .info-label[b-rkm042n77k] {
        color: #9ca3af;
    }

    .info-value[b-rkm042n77k] {
        color: #e5e7eb;
    }
}

/* 滚动条样式 */
.nav-items[b-rkm042n77k]::-webkit-scrollbar {
    width: 4px;
}

.nav-items[b-rkm042n77k]::-webkit-scrollbar-track {
    background: transparent;
}

.nav-items[b-rkm042n77k]::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 2px;
}

.nav-items[b-rkm042n77k]::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* 焦点样式 */
.nav-link:focus[b-rkm042n77k] {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* 动画效果 */
.nav-link[b-rkm042n77k] {
    position: relative;
    overflow: hidden;
}

.nav-link[b-rkm042n77k]::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.nav-link:hover[b-rkm042n77k]::before {
    left: 100%;
}

/* 图标样式 */
.nav-link i[b-rkm042n77k] {
    margin-right: 0.5rem;
    width: 1rem;
    text-align: center;
}

/* 徽章样式 */
.nav-badge[b-rkm042n77k] {
    background: #ef4444;
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 9999px;
    margin-left: auto;
}

/* 分隔线 */
.nav-divider[b-rkm042n77k] {
    height: 1px;
    background: #e5e7eb;
    margin: 0.5rem 0;
}

/* 工具提示 */
.nav-tooltip[b-rkm042n77k] {
    position: relative;
}

.nav-tooltip[b-rkm042n77k]::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: #1f2937;
    color: white;
    padding: 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s;
    z-index: 1000;
    margin-left: 0.5rem;
}

.nav-tooltip:hover[b-rkm042n77k]::after {
    opacity: 1;
    visibility: visible;
}

/* 加载状态 */
.nav-loading[b-rkm042n77k] {
    opacity: 0.6;
    pointer-events: none;
}

.nav-loading[b-rkm042n77k]::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1rem;
    height: 1rem;
    border: 2px solid #e5e7eb;
    border-top-color: #3b82f6;
    border-radius: 50%;
    animation: spin-b-rkm042n77k 1s linear infinite;
    transform: translate(-50%, -50%);
}

@keyframes spin-b-rkm042n77k {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}
/* _content/MolySite.Web/Components/Layout/PublicLayout.razor.rz.scp.css */
/* PublicLayout样式 */

.public-layout[b-zljedqn1vf] {
    padding-top: 76px; /* 为固定导航栏留出空间 */
}

.main-content[b-zljedqn1vf] {
    min-height: calc(100vh - 76px);
}

.navbar-brand[b-zljedqn1vf] {
    font-size: 1.5rem;
}

.text-gray-300[b-zljedqn1vf] {
    color: #d1d5db;
}

.text-gray-400[b-zljedqn1vf] {
    color: #9ca3af;
}

.border-gray-700[b-zljedqn1vf] {
    border-color: #374151;
}

.bg-gray-900[b-zljedqn1vf] {
    background-color: #111827;
}

.hover\:text-white:hover[b-zljedqn1vf] {
    color: white;
}

/* 平滑滚动 */
html[b-zljedqn1vf] {
    scroll-behavior: smooth;
}

/* 导航链接悬停效果 */
.nav-link:hover[b-zljedqn1vf] {
    color: #0d6efd !important;
}

/* 页脚样式 */
footer[b-zljedqn1vf] {
    padding: 4rem 0;
}

/* 响应式导航 */
@media (max-width: 991.98px) {
    .navbar-collapse[b-zljedqn1vf] {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #dee2e6;
    }
}
/* _content/MolySite.Web/Components/Pages/Dashboard/Components/DashboardLayoutComponent.razor.rz.scp.css */
/* 现代化Dashboard组件样式 - 全新设计 */

/* 全局变量 */
:root[b-a02jn9xbct] {
    --dashboard-primary-blue: #2563eb;
    --dashboard-primary-emerald: #059669;
    --dashboard-primary-purple: #7c3aed;
    --dashboard-bg-light: #f8fafc;
    --dashboard-bg-white: #ffffff;
    --dashboard-border: #e2e8f0;
    --dashboard-text-primary: #1e293b;
    --dashboard-text-secondary: #64748b;
    --dashboard-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* 主题样式 */
.dashboard-theme-admin[b-a02jn9xbct] {
    --theme-primary: var(--dashboard-primary-blue);
    --theme-primary-light: #dbeafe;
    --theme-primary-dark: #1d4ed8;
}

.dashboard-theme-owner[b-a02jn9xbct] {
    --theme-primary: var(--dashboard-primary-emerald);
    --theme-primary-light: #d1fae5;
    --theme-primary-dark: #047857;
}

.dashboard-theme-editor[b-a02jn9xbct] {
    --theme-primary: var(--dashboard-primary-purple);
    --theme-primary-light: #ede9fe;
    --theme-primary-dark: #6d28d9;
}

.dashboard-theme-default[b-a02jn9xbct] {
    --theme-primary: #6b7280;
    --theme-primary-light: #f3f4f6;
    --theme-primary-dark: #374151;
}

/* 现代化动画效果 */
.fade-in[b-a02jn9xbct] {
    animation: fadeIn-b-a02jn9xbct 0.4s ease-out;
}

@keyframes fadeIn-b-a02jn9xbct {
    from {
        opacity: 0;
        transform: translateY(16px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-right[b-a02jn9xbct] {
    animation: slideInRight-b-a02jn9xbct 0.3s ease-out;
}

@keyframes slideInRight-b-a02jn9xbct {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 导航项现代化样式 */
.nav-item[b-a02jn9xbct] {
    position: relative;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-item:hover[b-a02jn9xbct] {
    background-color: #f8fafc;
    transform: translateX(2px);
}

.nav-item-active[b-a02jn9xbct] {
    background-color: #eff6ff;
    color: #1d4ed8;
    border-right: 3px solid #2563eb;
}

.nav-item-active[b-a02jn9xbct]::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
    border-radius: 0 2px 2px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .dashboard-sidebar[b-a02jn9xbct] {
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
    }
    
    .dashboard-sidebar.open[b-a02jn9xbct] {
        transform: translateX(0);
    }
}

/* 加载状态 */
.loading-skeleton[b-a02jn9xbct] {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-b-a02jn9xbct 1.5s infinite;
}

@keyframes loading-b-a02jn9xbct {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 按钮样式增强 */
.btn-dashboard[b-a02jn9xbct] {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm;
    transition: all 0.2s ease-in-out;
}

.btn-dashboard:hover[b-a02jn9xbct] {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 主题色彩变量 */
:root[b-a02jn9xbct] {
    --color-superadmin: #3b82f6;
    --color-siteowner: #10b981;
    --color-siteeditor: #8b5cf6;
    --color-superadmin-light: #dbeafe;
    --color-siteowner-light: #d1fae5;
    --color-siteeditor-light: #ede9fe;
}

/* 角色特定主题样式 */
.dashboard-theme-superadmin[b-a02jn9xbct] {
    --primary-color: var(--color-superadmin);
    --primary-light: var(--color-superadmin-light);
}

.dashboard-theme-siteowner[b-a02jn9xbct] {
    --primary-color: var(--color-siteowner);
    --primary-light: var(--color-siteowner-light);
}

.dashboard-theme-siteeditor[b-a02jn9xbct] {
    --primary-color: var(--color-siteeditor);
    --primary-light: var(--color-siteeditor-light);
}

/* 主题化的组件样式 */
.dashboard-theme-superadmin .btn-primary[b-a02jn9xbct] {
    background-color: var(--color-superadmin);
    border-color: var(--color-superadmin);
}

.dashboard-theme-superadmin .btn-primary:hover[b-a02jn9xbct] {
    background-color: #2563eb;
    border-color: #2563eb;
}

.dashboard-theme-siteowner .btn-primary[b-a02jn9xbct] {
    background-color: var(--color-siteowner);
    border-color: var(--color-siteowner);
}

.dashboard-theme-siteowner .btn-primary:hover[b-a02jn9xbct] {
    background-color: #059669;
    border-color: #059669;
}

.dashboard-theme-siteeditor .btn-primary[b-a02jn9xbct] {
    background-color: var(--color-siteeditor);
    border-color: var(--color-siteeditor);
}

.dashboard-theme-siteeditor .btn-primary:hover[b-a02jn9xbct] {
    background-color: #7c3aed;
    border-color: #7c3aed;
}

/* 角色徽章样式 */
.role-badge-superadmin[b-a02jn9xbct] {
    background-color: var(--color-superadmin-light);
    color: var(--color-superadmin);
    border: 1px solid var(--color-superadmin);
}

.role-badge-siteowner[b-a02jn9xbct] {
    background-color: var(--color-siteowner-light);
    color: var(--color-siteowner);
    border: 1px solid var(--color-siteowner);
}

.role-badge-siteeditor[b-a02jn9xbct] {
    background-color: var(--color-siteeditor-light);
    color: var(--color-siteeditor);
    border: 1px solid var(--color-siteeditor);
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .dashboard-dark[b-a02jn9xbct] {
        @apply bg-gray-900 text-white;
    }
    
    .dashboard-dark .nav-item[b-a02jn9xbct] {
        @apply text-gray-300 hover:bg-gray-800 hover:text-white;
    }
    
    .dashboard-dark .dashboard-card[b-a02jn9xbct] {
        @apply bg-gray-800 border-gray-700;
    }
}
