[{"ContainingType": "MolySite.API.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDto", "Type": "MolySite.Shared.Dtos.LoginDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.AuthController", "Method": "GetCurrentUser", "RelativePath": "api/Auth/me", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/Auth/refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "refreshToken", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "registerDto", "Type": "MolySite.Shared.Dtos.RegisterDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.DatabaseCheckController", "Method": "ReseedDatabase", "RelativePath": "api/DatabaseCheck/reseed", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.DatabaseCheckController", "Method": "GetDatabaseStatus", "RelativePath": "api/DatabaseCheck/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.DatabaseCheckController", "Method": "CheckUsersAndRoles", "RelativePath": "api/DatabaseCheck/users-and-roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.DatabaseCheckController", "Method": "GetUsersByRole", "RelativePath": "api/DatabaseCheck/users-by-role/{roleName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.DatabaseResetController", "Method": "ResetDatabase", "RelativePath": "api/DatabaseReset/reset", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.DatabaseResetController", "Method": "GetDatabaseStatus", "RelativePath": "api/DatabaseReset/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.DatabaseTestController", "Method": "TestConnection", "RelativePath": "api/DatabaseTest/connection", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.DatabaseTestController", "Method": "TestCrud", "RelativePath": "api/DatabaseTest/crud", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.DatabaseTestController", "Method": "GetDatabaseStatus", "RelativePath": "api/DatabaseTest/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.DatabaseTestController", "Method": "TestTables", "RelativePath": "api/DatabaseTest/tables", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.PermissionsController", "Method": "GetAccessibleSites", "RelativePath": "api/Permissions/accessible-sites", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.PermissionsController", "Method": "GetEditableSites", "RelativePath": "api/Permissions/editable-sites", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.PermissionsController", "Method": "CheckSiteAccess", "RelativePath": "api/Permissions/site/{siteId}/access", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "siteId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.PermissionsController", "Method": "AssignSiteRole", "RelativePath": "api/Permissions/site/{siteId}/assign-role", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "siteId", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "MolySite.API.Controllers.AssignRoleRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.PermissionsController", "Method": "CheckSitePermission", "RelativePath": "api/Permissions/site/{siteId}/check", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "siteId", "Type": "System.Guid", "IsRequired": true}, {"Name": "permission", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.PermissionsController", "Method": "RemoveSiteRole", "RelativePath": "api/Permissions/site/{siteId}/remove-role/{targetUserId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "siteId", "Type": "System.Guid", "IsRequired": true}, {"Name": "targetUserId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.PermissionsController", "Method": "GetUserSiteRole", "RelativePath": "api/Permissions/site/{siteId}/role", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "siteId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.PermissionsController", "Method": "GetSiteUserRoles", "RelativePath": "api/Permissions/site/{siteId}/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "siteId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.SitesController", "Method": "CreateSite", "RelativePath": "api/Sites", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createSiteDto", "Type": "MolySite.Shared.Dtos.CreateSiteDto", "IsRequired": true}], "ReturnTypes": [{"Type": "MolySite.Shared.Dtos.SiteDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MolySite.API.Controllers.SitesController", "Method": "GetSite", "RelativePath": "api/Sites/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "MolySite.Shared.Dtos.SiteDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MolySite.API.Controllers.SitesController", "Method": "UpdateSite", "RelativePath": "api/Sites/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "updateSiteDto", "Type": "MolySite.Shared.Dtos.UpdateSiteDto", "IsRequired": true}], "ReturnTypes": [{"Type": "MolySite.Shared.Dtos.SiteDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MolySite.API.Controllers.SitesController", "Method": "DeleteSite", "RelativePath": "api/Sites/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.SitesController", "Method": "PublishSite", "RelativePath": "api/Sites/{id}/publish", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.SitesController", "Method": "UnpublishSite", "RelativePath": "api/Sites/{id}/unpublish", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.SitesController", "Method": "GetAllSites", "RelativePath": "api/Sites/all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[MolySite.Shared.Dtos.SiteDto, MolySite.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MolySite.API.Controllers.SitesController", "Method": "GetSiteByDomain", "RelativePath": "api/Sites/by-domain/{domain}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "domain", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "MolySite.Shared.Dtos.SiteDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MolySite.API.Controllers.SitesController", "Method": "CheckDomainAvailability", "RelativePath": "api/Sites/check-domain", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "domain", "Type": "System.String", "IsRequired": false}, {"Name": "excludeSiteId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MolySite.API.Controllers.SitesController", "Method": "GetMySites", "RelativePath": "api/Sites/my-sites", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[MolySite.Shared.Dtos.SiteDto, MolySite.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]