using System;
using System.ComponentModel.DataAnnotations;
using MolySite.Shared.Dtos;

namespace MolySite.Identity.Models
{
    /// <summary>
    /// 网站用户角色关系模型
    /// </summary>
    public class SiteUserRole
    {
        /// <summary>
        /// 唯一标识符
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// 网站ID
        /// </summary>
        public Guid SiteId { get; set; }

        /// <summary>
        /// 用户在网站中的角色
        /// </summary>
        public SiteRoleType Role { get; set; }

        /// <summary>
        /// 权限授予时间
        /// </summary>
        public DateTime GrantedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 权限授予者ID（谁邀请的）
        /// </summary>
        public Guid? GrantedBy { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;



        /// <summary>
        /// 导航属性：用户
        /// </summary>
        public ApplicationUser User { get; set; } = null!;

        /// <summary>
        /// 导航属性：网站
        /// </summary>
        public Site Site { get; set; } = null!;

        /// <summary>
        /// 导航属性：权限授予者
        /// </summary>
        public ApplicationUser? GrantedByUser { get; set; }
    }

}
