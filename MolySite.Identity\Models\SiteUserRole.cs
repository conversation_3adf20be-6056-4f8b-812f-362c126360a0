using System;
using System.ComponentModel.DataAnnotations;

namespace MolySite.Identity.Models
{
    /// <summary>
    /// 网站用户角色关系模型
    /// </summary>
    public class SiteUserRole
    {
        /// <summary>
        /// 唯一标识符
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// 网站ID
        /// </summary>
        public Guid SiteId { get; set; }

        /// <summary>
        /// 用户在网站中的角色
        /// </summary>
        public SiteRoleType Role { get; set; }

        /// <summary>
        /// 权限授予时间
        /// </summary>
        public DateTime GrantedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 权限授予者ID（谁邀请的）
        /// </summary>
        public Guid? GrantedBy { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 邀请状态
        /// </summary>
        public InvitationStatus InvitationStatus { get; set; } = InvitationStatus.Accepted;

        /// <summary>
        /// 邀请令牌（用于邮件邀请）
        /// </summary>
        [MaxLength(255)]
        public string? InvitationToken { get; set; }

        /// <summary>
        /// 邀请过期时间
        /// </summary>
        public DateTime? InvitationExpiresAt { get; set; }

        /// <summary>
        /// 导航属性：用户
        /// </summary>
        public ApplicationUser User { get; set; } = null!;

        /// <summary>
        /// 导航属性：网站
        /// </summary>
        public Site Site { get; set; } = null!;

        /// <summary>
        /// 导航属性：权限授予者
        /// </summary>
        public ApplicationUser? GrantedByUser { get; set; }
    }

    /// <summary>
    /// 网站角色类型枚举
    /// </summary>
    public enum SiteRoleType
    {
        /// <summary>
        /// 网站所有者
        /// </summary>
        Owner = 1
    }

    /// <summary>
    /// 邀请状态枚举
    /// </summary>
    public enum InvitationStatus
    {
        /// <summary>
        /// 待接受
        /// </summary>
        Pending = 0,

        /// <summary>
        /// 已接受
        /// </summary>
        Accepted = 1,

        /// <summary>
        /// 已拒绝
        /// </summary>
        Rejected = 2,

        /// <summary>
        /// 已过期
        /// </summary>
        Expired = 3,

        /// <summary>
        /// 已撤销
        /// </summary>
        Revoked = 4
    }
}
