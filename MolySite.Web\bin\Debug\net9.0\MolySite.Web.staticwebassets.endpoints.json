{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "MolySite.Web.56vxggxec6.styles.css", "AssetFile": "MolySite.Web.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000237304224"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4213"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QXwHO2MGAzteDsxybLlquFOGIAgRMn3CntpAROkEm9w=\""}, {"Name": "ETag", "Value": "W/\"DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "56vxggxec6"}, {"Name": "integrity", "Value": "sha256-DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk="}, {"Name": "label", "Value": "MolySite.Web.styles.css"}]}, {"Route": "MolySite.Web.56vxggxec6.styles.css", "AssetFile": "MolySite.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "56vxggxec6"}, {"Name": "integrity", "Value": "sha256-DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk="}, {"Name": "label", "Value": "MolySite.Web.styles.css"}]}, {"Route": "MolySite.Web.56vxggxec6.styles.css.gz", "AssetFile": "MolySite.Web.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4213"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QXwHO2MGAzteDsxybLlquFOGIAgRMn3CntpAROkEm9w=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "56vxggxec6"}, {"Name": "integrity", "Value": "sha256-QXwHO2MGAzteDsxybLlquFOGIAgRMn3CntpAROkEm9w="}, {"Name": "label", "Value": "MolySite.Web.styles.css.gz"}]}, {"Route": "MolySite.Web.styles.css", "AssetFile": "MolySite.Web.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000237304224"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4213"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QXwHO2MGAzteDsxybLlquFOGIAgRMn3CntpAROkEm9w=\""}, {"Name": "ETag", "Value": "W/\"DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk="}]}, {"Route": "MolySite.Web.styles.css", "AssetFile": "MolySite.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk="}]}, {"Route": "MolySite.Web.styles.css.gz", "AssetFile": "MolySite.Web.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4213"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QXwHO2MGAzteDsxybLlquFOGIAgRMn3CntpAROkEm9w=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QXwHO2MGAzteDsxybLlquFOGIAgRMn3CntpAROkEm9w="}]}, {"Route": "app.2zmnmq58fl.css", "AssetFile": "app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000494559842"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2021"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"m3TOaMa/dbFUHAbraOyLHPIUciPPJibKnA0F3VeZ458=\""}, {"Name": "ETag", "Value": "W/\"DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2zmnmq58fl"}, {"Name": "integrity", "Value": "sha256-DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "app.2zmnmq58fl.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3624"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 13:38:46 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2zmnmq58fl"}, {"Name": "integrity", "Value": "sha256-DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "app.2zmnmq58fl.css.gz", "AssetFile": "app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2021"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"m3TOaMa/dbFUHAbraOyLHPIUciPPJibKnA0F3VeZ458=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2zmnmq58fl"}, {"Name": "integrity", "Value": "sha256-m3TOaMa/dbFUHAbraOyLHPIUciPPJibKnA0F3VeZ458="}, {"Name": "label", "Value": "app.css.gz"}]}, {"Route": "app.css", "AssetFile": "app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000494559842"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2021"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"m3TOaMa/dbFUHAbraOyLHPIUciPPJibKnA0F3VeZ458=\""}, {"Name": "ETag", "Value": "W/\"DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY="}]}, {"Route": "app.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3624"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 13:38:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY="}]}, {"Route": "app.css.gz", "AssetFile": "app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2021"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"m3TOaMa/dbFUHAbraOyLHPIUciPPJibKnA0F3VeZ458=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m3TOaMa/dbFUHAbraOyLHPIUciPPJibKnA0F3VeZ458="}]}, {"Route": "css/dashboard.5ipweew5fc.css", "AssetFile": "css/dashboard.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "css/dashboard.css"}]}, {"Route": "css/dashboard.5ipweew5fc.css", "AssetFile": "css/dashboard.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 13:44:39 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "css/dashboard.css"}]}, {"Route": "css/dashboard.5ipweew5fc.css.gz", "AssetFile": "css/dashboard.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "css/dashboard.css.gz"}]}, {"Route": "css/dashboard.css", "AssetFile": "css/dashboard.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "css/dashboard.css", "AssetFile": "css/dashboard.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 13:44:39 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "css/dashboard.css.gz", "AssetFile": "css/dashboard.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "css/tailwind.css", "AssetFile": "css/tailwind.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083696016"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11947"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+eojRBy5eYG7gLIJBwvt3+BJm9RY2TPoWTw/1/b/ef8=\""}, {"Name": "ETag", "Value": "W/\"dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU="}]}, {"Route": "css/tailwind.css", "AssetFile": "css/tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "77985"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 07:41:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU="}]}, {"Route": "css/tailwind.css.gz", "AssetFile": "css/tailwind.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11947"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+eojRBy5eYG7gLIJBwvt3+BJm9RY2TPoWTw/1/b/ef8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+eojRBy5eYG7gLIJBwvt3+BJm9RY2TPoWTw/1/b/ef8="}]}, {"Route": "css/tailwind.frz2k1ad57.css", "AssetFile": "css/tailwind.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083696016"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11947"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+eojRBy5eYG7gLIJBwvt3+BJm9RY2TPoWTw/1/b/ef8=\""}, {"Name": "ETag", "Value": "W/\"dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "frz2k1ad57"}, {"Name": "integrity", "Value": "sha256-dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU="}, {"Name": "label", "Value": "css/tailwind.css"}]}, {"Route": "css/tailwind.frz2k1ad57.css", "AssetFile": "css/tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "77985"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 07:41:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "frz2k1ad57"}, {"Name": "integrity", "Value": "sha256-dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU="}, {"Name": "label", "Value": "css/tailwind.css"}]}, {"Route": "css/tailwind.frz2k1ad57.css.gz", "AssetFile": "css/tailwind.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11947"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+eojRBy5eYG7gLIJBwvt3+BJm9RY2TPoWTw/1/b/ef8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "frz2k1ad57"}, {"Name": "integrity", "Value": "sha256-+eojRBy5eYG7gLIJBwvt3+BJm9RY2TPoWTw/1/b/ef8="}, {"Name": "label", "Value": "css/tailwind.css.gz"}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 04:09:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}, {"Name": "label", "Value": "favicon.png"}]}, {"Route": "favicon.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 04:09:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "js/app.ab62h7jd1e.js", "AssetFile": "js/app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000801924619"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1246"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Yl6RfmWHmQsMOvoTFMndXStjt6M6jFMLzvnqHNL0G5g=\""}, {"Name": "ETag", "Value": "W/\"dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ab62h7jd1e"}, {"Name": "integrity", "Value": "sha256-dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY="}, {"Name": "label", "Value": "js/app.js"}]}, {"Route": "js/app.ab62h7jd1e.js", "AssetFile": "js/app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2906"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 13:16:06 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ab62h7jd1e"}, {"Name": "integrity", "Value": "sha256-dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY="}, {"Name": "label", "Value": "js/app.js"}]}, {"Route": "js/app.ab62h7jd1e.js.gz", "AssetFile": "js/app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1246"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Yl6RfmWHmQsMOvoTFMndXStjt6M6jFMLzvnqHNL0G5g=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ab62h7jd1e"}, {"Name": "integrity", "Value": "sha256-Yl6RfmWHmQsMOvoTFMndXStjt6M6jFMLzvnqHNL0G5g="}, {"Name": "label", "Value": "js/app.js.gz"}]}, {"Route": "js/app.js", "AssetFile": "js/app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000801924619"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1246"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Yl6RfmWHmQsMOvoTFMndXStjt6M6jFMLzvnqHNL0G5g=\""}, {"Name": "ETag", "Value": "W/\"dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY="}]}, {"Route": "js/app.js", "AssetFile": "js/app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2906"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 13:16:06 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY="}]}, {"Route": "js/app.js.gz", "AssetFile": "js/app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1246"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Yl6RfmWHmQsMOvoTFMndXStjt6M6jFMLzvnqHNL0G5g=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Yl6RfmWHmQsMOvoTFMndXStjt6M6jFMLzvnqHNL0G5g="}]}, {"Route": "js/performance-monitor.1wi6zlpeou.js", "AssetFile": "js/performance-monitor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000514933059"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1941"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"W7Qr0WZF8BwwinZMll66JpF0UpCKmSQ99BfgNIjdYBc=\""}, {"Name": "ETag", "Value": "W/\"ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1wi6zlpeou"}, {"Name": "integrity", "Value": "sha256-ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc="}, {"Name": "label", "Value": "js/performance-monitor.js"}]}, {"Route": "js/performance-monitor.1wi6zlpeou.js", "AssetFile": "js/performance-monitor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6295"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 13:47:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1wi6zlpeou"}, {"Name": "integrity", "Value": "sha256-ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc="}, {"Name": "label", "Value": "js/performance-monitor.js"}]}, {"Route": "js/performance-monitor.1wi6zlpeou.js.gz", "AssetFile": "js/performance-monitor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1941"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"W7Qr0WZF8BwwinZMll66JpF0UpCKmSQ99BfgNIjdYBc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1wi6zlpeou"}, {"Name": "integrity", "Value": "sha256-W7Qr0WZF8BwwinZMll66JpF0UpCKmSQ99BfgNIjdYBc="}, {"Name": "label", "Value": "js/performance-monitor.js.gz"}]}, {"Route": "js/performance-monitor.js", "AssetFile": "js/performance-monitor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000514933059"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1941"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"W7Qr0WZF8BwwinZMll66JpF0UpCKmSQ99BfgNIjdYBc=\""}, {"Name": "ETag", "Value": "W/\"ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc="}]}, {"Route": "js/performance-monitor.js", "AssetFile": "js/performance-monitor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6295"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 13:47:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc="}]}, {"Route": "js/performance-monitor.js.gz", "AssetFile": "js/performance-monitor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1941"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"W7Qr0WZF8BwwinZMll66JpF0UpCKmSQ99BfgNIjdYBc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-W7Qr0WZF8BwwinZMll66JpF0UpCKmSQ99BfgNIjdYBc="}]}, {"Route": "lib/bootstrap-icons/font/bootstrap-icons.min.26f9b7qkas.css", "AssetFile": "lib/bootstrap-icons/font/bootstrap-icons.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000073190368"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13662"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0G38FUtwEm3D6mNy0IJsswxRAeP1u8XxqJsRyIEItlg=\""}, {"Name": "ETag", "Value": "W/\"9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "26f9b7qkas"}, {"Name": "integrity", "Value": "sha256-9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI="}, {"Name": "label", "Value": "lib/bootstrap-icons/font/bootstrap-icons.min.css"}]}, {"Route": "lib/bootstrap-icons/font/bootstrap-icons.min.26f9b7qkas.css", "AssetFile": "lib/bootstrap-icons/font/bootstrap-icons.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "85875"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 06:55:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "26f9b7qkas"}, {"Name": "integrity", "Value": "sha256-9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI="}, {"Name": "label", "Value": "lib/bootstrap-icons/font/bootstrap-icons.min.css"}]}, {"Route": "lib/bootstrap-icons/font/bootstrap-icons.min.26f9b7qkas.css.gz", "AssetFile": "lib/bootstrap-icons/font/bootstrap-icons.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13662"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0G38FUtwEm3D6mNy0IJsswxRAeP1u8XxqJsRyIEItlg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "26f9b7qkas"}, {"Name": "integrity", "Value": "sha256-0G38FUtwEm3D6mNy0IJsswxRAeP1u8XxqJsRyIEItlg="}, {"Name": "label", "Value": "lib/bootstrap-icons/font/bootstrap-icons.min.css.gz"}]}, {"Route": "lib/bootstrap-icons/font/bootstrap-icons.min.css", "AssetFile": "lib/bootstrap-icons/font/bootstrap-icons.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000073190368"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13662"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0G38FUtwEm3D6mNy0IJsswxRAeP1u8XxqJsRyIEItlg=\""}, {"Name": "ETag", "Value": "W/\"9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI="}]}, {"Route": "lib/bootstrap-icons/font/bootstrap-icons.min.css", "AssetFile": "lib/bootstrap-icons/font/bootstrap-icons.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "85875"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 06:55:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI="}]}, {"Route": "lib/bootstrap-icons/font/bootstrap-icons.min.css.gz", "AssetFile": "lib/bootstrap-icons/font/bootstrap-icons.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13662"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0G38FUtwEm3D6mNy0IJsswxRAeP1u8XxqJsRyIEItlg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:48:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0G38FUtwEm3D6mNy0IJsswxRAeP1u8XxqJsRyIEItlg="}]}]}