using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MolySite.Core.Interfaces;
using MolySite.Core.Models;
using CoreDtos = MolySite.Shared.Dtos;
using IdentityDtos = MolySite.Identity.Dtos;
using MolySite.Identity.Services;

namespace MolySite.API.Services
{
    /// <summary>
    /// AuthService适配器，将Identity层的AuthService适配到Core层的IAuthService接口
    /// </summary>
    public class AuthServiceAdapter : MolySite.Core.Interfaces.IAuthService
    {
        private readonly MolySite.Identity.Services.IAuthService _identityAuthService;
        private readonly ILogger<AuthServiceAdapter> _logger;

        public AuthServiceAdapter(
            MolySite.Identity.Services.IAuthService identityAuthService,
            ILogger<AuthServiceAdapter> logger)
        {
            _identityAuthService = identityAuthService;
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<Result<CoreDtos.LoginResponseDto>> RegisterAsync(CoreDtos.RegisterDto registerDto)
        {
            try
            {
                // 转换DTO
                var identityRegisterDto = new IdentityDtos.RegisterDto
                {
                    UserName = registerDto.UserName,
                    Email = registerDto.Email,
                    Password = registerDto.Password,
                    ConfirmPassword = registerDto.ConfirmPassword
                };

                var result = await _identityAuthService.RegisterAsync(identityRegisterDto);

                if (result.Success && result.LoginResponse != null)
                {
                    // 转换响应DTO
                    var responseDto = new CoreDtos.LoginResponseDto
                    {
                        Token = result.LoginResponse.Token,
                        RefreshToken = result.LoginResponse.RefreshToken,
                        UserId = result.LoginResponse.UserId,
                        UserName = result.LoginResponse.UserName,
                        Email = result.LoginResponse.Email,
                        Roles = result.LoginResponse.Roles
                    };

                    return Result<CoreDtos.LoginResponseDto>.Success(responseDto);
                }

                return Result<CoreDtos.LoginResponseDto>.Failure(result.ErrorMessage ?? "注册失败", "REGISTER_FAILED");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "注册适配器发生异常");
                return Result<CoreDtos.LoginResponseDto>.Failure("注册过程中发生错误", "REGISTER_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<CoreDtos.LoginResponseDto>> LoginAsync(CoreDtos.LoginDto loginDto)
        {
            try
            {
                // 转换DTO
                var identityLoginDto = new IdentityDtos.LoginDto
                {
                    UserNameOrEmail = loginDto.UserNameOrEmail,
                    Password = loginDto.Password,
                    RememberMe = loginDto.RememberMe
                };

                var result = await _identityAuthService.LoginAsync(identityLoginDto);

                if (result.Success && result.LoginResponse != null)
                {
                    // 转换响应DTO
                    var responseDto = new CoreDtos.LoginResponseDto
                    {
                        Token = result.LoginResponse.Token,
                        RefreshToken = result.LoginResponse.RefreshToken,
                        UserId = result.LoginResponse.UserId,
                        UserName = result.LoginResponse.UserName,
                        Email = result.LoginResponse.Email,
                        Roles = result.LoginResponse.Roles
                    };

                    return Result<CoreDtos.LoginResponseDto>.Success(responseDto);
                }

                return Result<CoreDtos.LoginResponseDto>.Failure(result.ErrorMessage ?? "登录失败", "LOGIN_FAILED");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "登录适配器发生异常");
                return Result<CoreDtos.LoginResponseDto>.Failure("登录过程中发生错误", "LOGIN_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<CoreDtos.LoginResponseDto>> RefreshTokenAsync(CoreDtos.RefreshTokenDto refreshTokenDto)
        {
            try
            {
                var result = await _identityAuthService.RefreshTokenAsync(refreshTokenDto.RefreshToken);

                if (result.Success && result.LoginResponse != null)
                {
                    // 转换响应DTO
                    var responseDto = new CoreDtos.LoginResponseDto
                    {
                        Token = result.LoginResponse.Token,
                        RefreshToken = result.LoginResponse.RefreshToken,
                        UserId = result.LoginResponse.UserId,
                        UserName = result.LoginResponse.UserName,
                        Email = result.LoginResponse.Email,
                        Roles = result.LoginResponse.Roles
                    };

                    return Result<CoreDtos.LoginResponseDto>.Success(responseDto);
                }

                return Result<CoreDtos.LoginResponseDto>.Failure(result.ErrorMessage ?? "刷新令牌失败", "REFRESH_FAILED");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新令牌适配器发生异常");
                return Result<CoreDtos.LoginResponseDto>.Failure("刷新令牌过程中发生错误", "REFRESH_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<bool>> LogoutAsync(Guid userId)
        {
            try
            {
                await _identityAuthService.LogoutAsync(userId);
                return Result<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "登出适配器发生异常");
                return Result<bool>.Failure("登出过程中发生错误", "LOGOUT_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<bool>> ChangePasswordAsync(Guid userId, CoreDtos.ChangePasswordDto changePasswordDto)
        {
            await Task.CompletedTask;
            _logger.LogInformation("更改密码功能暂未实现");
            return Result<bool>.Failure("更改密码功能暂未实现", "NOT_IMPLEMENTED");
        }

        /// <inheritdoc/>
        public async Task<Result<bool>> ResetPasswordAsync(CoreDtos.ResetPasswordDto resetPasswordDto)
        {
            await Task.CompletedTask;
            _logger.LogInformation("重置密码功能暂未实现");
            return Result<bool>.Failure("重置密码功能暂未实现", "NOT_IMPLEMENTED");
        }

        /// <inheritdoc/>
        public async Task<Result<bool>> ConfirmResetPasswordAsync(CoreDtos.ConfirmResetPasswordDto confirmResetDto)
        {
            await Task.CompletedTask;
            _logger.LogInformation("确认重置密码功能暂未实现");
            return Result<bool>.Failure("确认重置密码功能暂未实现", "NOT_IMPLEMENTED");
        }

        /// <inheritdoc/>
        public async Task<Result<bool>> ValidateTokenAsync(string token)
        {
            try
            {
                var isValid = await _identityAuthService.ValidateTokenAsync(token);
                return Result<bool>.Success(isValid);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证令牌适配器发生异常");
                return Result<bool>.Failure("验证令牌过程中发生错误", "VALIDATE_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<UserDto>> GetUserAsync(Guid userId)
        {
            await Task.CompletedTask;
            _logger.LogInformation("获取用户信息功能暂未实现");
            return Result<UserDto>.Failure("获取用户信息功能暂未实现", "NOT_IMPLEMENTED");
        }
    }
}
