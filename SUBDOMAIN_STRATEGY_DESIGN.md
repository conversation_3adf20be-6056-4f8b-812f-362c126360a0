# 🌐 MolySite二级域名分配策略设计

## 📊 知名平台域名策略分析

### 主流建站平台域名模式

| 平台 | 免费域名格式 | 示例 | 特点 |
|------|-------------|------|------|
| **Wix** | `username.wixsite.com` | `john.wixsite.com` | 用户名为主 |
| **WordPress.com** | `sitename.wordpress.com` | `myblog.wordpress.com` | 网站名为主 |
| **Weebly** | `sitename.weebly.com` | `mystore.weebly.com` | 网站名为主 |
| **Squarespace** | `sitename.squarespace.com` | `portfolio.squarespace.com` | 网站名为主 |
| **Shopify** | `storename.myshopify.com` | `fashion.myshopify.com` | 商店名为主 |
| **GitHub Pages** | `username.github.io` | `john.github.io` | 用户名为主 |

### 域名策略优缺点分析

**用户名模式** (`username.platform.com`):
- ✅ 优点: 唯一性强、个人品牌化、易记忆
- ❌ 缺点: 一个用户只能有一个域名、不适合多网站

**网站名模式** (`sitename.platform.com`):
- ✅ 优点: 支持多网站、语义化强、SEO友好
- ❌ 缺点: 命名冲突多、需要复杂的可用性检查

## 🎯 MolySite域名分配策略

### 核心设计原则

1. **多网站支持**: 用户可以创建多个网站
2. **语义化命名**: 域名应该反映网站内容/用途
3. **全球化友好**: 支持多语言字符
4. **SEO优化**: 有利于搜索引擎优化
5. **品牌保护**: 避免恶意抢注和品牌冲突
6. **扩展性**: 支持未来的功能扩展

### 推荐方案：混合智能分配策略

#### 方案1: 智能网站名模式（主推）
```
格式: {sitename}.molysite.com
示例: 
- myblog.molysite.com
- portfolio.molysite.com
- mystore.molysite.com
- company.molysite.com
```

#### 方案2: 用户名+网站名模式（备选）
```
格式: {username}-{sitename}.molysite.com
示例:
- john-blog.molysite.com
- mary-portfolio.molysite.com
- company-official.molysite.com
```

#### 方案3: 分类前缀模式（高级）
```
格式: {category}-{sitename}.molysite.com
示例:
- blog-tech.molysite.com
- shop-fashion.molysite.com
- portfolio-design.molysite.com
```

## 🔧 域名生成算法

### 智能域名建议系统

```javascript
// 域名生成逻辑
function generateDomainSuggestions(siteName, siteType, userName) {
    const suggestions = [];
    
    // 1. 直接使用网站名
    const cleanSiteName = sanitizeDomainName(siteName);
    suggestions.push(cleanSiteName);
    
    // 2. 添加类型前缀
    const typePrefix = getSiteTypePrefix(siteType);
    if (typePrefix) {
        suggestions.push(`${typePrefix}-${cleanSiteName}`);
    }
    
    // 3. 添加用户名前缀
    suggestions.push(`${userName}-${cleanSiteName}`);
    
    // 4. 添加数字后缀
    for (let i = 1; i <= 5; i++) {
        suggestions.push(`${cleanSiteName}${i}`);
        suggestions.push(`${cleanSiteName}-${i}`);
    }
    
    // 5. 添加年份后缀
    const year = new Date().getFullYear();
    suggestions.push(`${cleanSiteName}${year}`);
    
    return suggestions;
}
```

### 域名验证规则

```javascript
// 域名验证规则
const DOMAIN_RULES = {
    minLength: 3,
    maxLength: 63,
    allowedChars: /^[a-z0-9-]+$/,
    reservedNames: [
        'www', 'api', 'admin', 'mail', 'ftp', 'blog', 'shop',
        'app', 'mobile', 'support', 'help', 'docs', 'cdn',
        'static', 'assets', 'images', 'files', 'download'
    ],
    bannedWords: [
        'porn', 'sex', 'adult', 'casino', 'gambling',
        'drugs', 'violence', 'hate', 'spam', 'phishing'
    ]
};
```

## 🎨 用户界面设计

### 域名选择流程

```
创建网站 → 填写基本信息 → 域名配置 → 确认创建
                ↓
        [网站名称] → [自动生成域名建议]
                ↓
        [用户选择] → [可用性检查] → [确认分配]
```

### 域名配置界面

```html
<!-- 域名配置组件 -->
<div class="domain-configuration">
    <h3>选择您的网站域名</h3>
    
    <!-- 主要域名输入 -->
    <div class="primary-domain">
        <input type="text" v-model="domainName" placeholder="输入域名">
        <span>.molysite.com</span>
        <button @click="checkAvailability">检查可用性</button>
    </div>
    
    <!-- 域名建议列表 -->
    <div class="domain-suggestions">
        <h4>推荐域名</h4>
        <div v-for="suggestion in suggestions" :key="suggestion">
            <label>
                <input type="radio" v-model="selectedDomain" :value="suggestion">
                {{ suggestion }}.molysite.com
                <span class="availability">{{ getAvailability(suggestion) }}</span>
            </label>
        </div>
    </div>
    
    <!-- 高级选项 -->
    <div class="advanced-options">
        <details>
            <summary>高级选项</summary>
            <label>
                <input type="checkbox" v-model="enableCustomPrefix">
                使用自定义前缀
            </label>
            <label>
                <input type="checkbox" v-model="enableAutoNumber">
                自动添加数字后缀
            </label>
        </details>
    </div>
</div>
```

## 🔒 域名管理策略

### 域名生命周期管理

1. **预留期** (24小时)
   - 用户选择域名后预留24小时
   - 防止创建过程中被他人抢注

2. **活跃期** (网站正常使用)
   - 域名正常解析到用户网站
   - 支持自定义域名绑定

3. **暂停期** (账户暂停)
   - 域名暂时不可访问
   - 保留域名所有权

4. **回收期** (账户删除后30天)
   - 域名进入回收池
   - 30天后可被其他用户注册

### 域名冲突解决

```javascript
// 域名冲突解决策略
const CONFLICT_RESOLUTION = {
    // 1. 自动添加后缀
    autoSuffix: true,
    
    // 2. 建议相似域名
    similarSuggestions: 5,
    
    // 3. 用户名前缀
    userPrefix: true,
    
    // 4. 类型前缀
    typePrefix: true,
    
    // 5. 地区后缀
    regionSuffix: false
};
```

## 🌍 国际化支持

### 多语言域名支持

```javascript
// 国际化域名处理
function handleInternationalDomain(input, locale) {
    // 1. 中文转拼音
    if (locale === 'zh-CN') {
        return convertToPinyin(input);
    }
    
    // 2. 日文转罗马字
    if (locale === 'ja-JP') {
        return convertToRomaji(input);
    }
    
    // 3. 阿拉伯文转英文
    if (locale === 'ar') {
        return transliterateArabic(input);
    }
    
    // 4. 通用转换
    return transliterate(input);
}
```

### 地区化域名策略

```
中国用户: pinyin-based.molysite.com
日本用户: romaji-based.molysite.com
全球用户: english-based.molysite.com
```

## 📈 SEO优化策略

### SEO友好的域名规则

1. **关键词包含**: 鼓励在域名中包含相关关键词
2. **连字符使用**: 适当使用连字符分隔单词
3. **长度控制**: 保持域名简短易记
4. **避免数字**: 除非必要，避免使用数字

### 搜索引擎优化

```javascript
// SEO评分算法
function calculateSEOScore(domain) {
    let score = 100;
    
    // 长度评分
    if (domain.length > 20) score -= 10;
    if (domain.length > 30) score -= 20;
    
    // 连字符评分
    const hyphens = (domain.match(/-/g) || []).length;
    if (hyphens > 2) score -= hyphens * 5;
    
    // 数字评分
    const numbers = (domain.match(/\d/g) || []).length;
    if (numbers > 0) score -= numbers * 3;
    
    // 关键词评分
    if (containsKeywords(domain)) score += 10;
    
    return Math.max(0, score);
}
```

## 🔧 技术实现方案

### 数据库设计

```sql
-- 域名管理表
CREATE TABLE domain_allocations (
    id UUID PRIMARY KEY,
    domain_name VARCHAR(63) NOT NULL UNIQUE,
    site_id UUID REFERENCES sites(id),
    user_id UUID REFERENCES users(id),
    status VARCHAR(20) DEFAULT 'active', -- active, reserved, suspended, recycled
    allocated_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 域名预留表
CREATE TABLE domain_reservations (
    id UUID PRIMARY KEY,
    domain_name VARCHAR(63) NOT NULL,
    user_id UUID REFERENCES users(id),
    reserved_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP DEFAULT (NOW() + INTERVAL '24 hours')
);

-- 禁用域名表
CREATE TABLE banned_domains (
    id UUID PRIMARY KEY,
    domain_pattern VARCHAR(100) NOT NULL,
    reason VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW()
);
```

### API接口设计

```javascript
// 域名相关API
const domainAPI = {
    // 检查域名可用性
    checkAvailability: 'POST /api/domains/check',
    
    // 获取域名建议
    getSuggestions: 'POST /api/domains/suggestions',
    
    // 预留域名
    reserveDomain: 'POST /api/domains/reserve',
    
    // 分配域名
    allocateDomain: 'POST /api/domains/allocate',
    
    // 释放域名
    releaseDomain: 'DELETE /api/domains/{domain}'
};
```

## 🎯 推荐实施方案

### 第一阶段：基础实现
1. ✅ 实现智能网站名模式
2. ✅ 基础域名验证规则
3. ✅ 简单的可用性检查
4. ✅ 自动域名建议

### 第二阶段：增强功能
1. 🔄 域名预留机制
2. 🔄 冲突解决策略
3. 🔄 SEO评分系统
4. 🔄 国际化支持

### 第三阶段：高级特性
1. 🔮 智能域名推荐
2. 🔮 品牌保护机制
3. 🔮 域名交易市场
4. 🔮 自定义域名绑定

## 💡 创新特性

### 1. AI驱动的域名建议
- 基于网站内容智能推荐域名
- 学习用户偏好优化建议
- 考虑行业特点和趋势

### 2. 域名品质评分
- SEO友好度评分
- 品牌价值评估
- 记忆度分析

### 3. 社交化域名
- 支持表情符号域名
- 社交媒体友好格式
- 短链接生成

## 🎉 总结

MolySite的域名分配策略应该：

1. **以网站名为主** - 支持多网站，语义化强
2. **智能冲突解决** - 自动建议可用域名
3. **SEO优化** - 有利于搜索引擎收录
4. **国际化友好** - 支持多语言用户
5. **品牌保护** - 防止恶意注册
6. **用户体验优先** - 简单易用的选择流程

这个策略既借鉴了成熟平台的经验，又结合了MolySite的特色需求，能够为用户提供专业、灵活、易用的域名服务。
