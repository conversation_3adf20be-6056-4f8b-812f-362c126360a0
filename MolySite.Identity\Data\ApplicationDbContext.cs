using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using MolySite.Identity.Models;
using System;
using System.Linq;

namespace MolySite.Identity.Data
{
    /// <summary>
    /// 应用程序数据库上下文
    /// </summary>
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser, IdentityRole<Guid>, Guid>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) 
            : base(options)
        {
        }

        /// <summary>
        /// 网站集合
        /// </summary>
        public DbSet<Site> Sites { get; set; }

        /// <summary>
        /// 网站用户角色关系集合
        /// </summary>
        public DbSet<SiteUserRole> SiteUserRoles { get; set; }



        /// <summary>
        /// 角色权限映射
        /// </summary>
        public DbSet<Models.RolePermission> RolePermissions { get; set; }

        /// <summary>
        /// 令牌存储
        /// </summary>
        public DbSet<TokenStore> TokenStores { get; set; }

        /// <summary>
        /// 令牌黑名单
        /// </summary>
        public DbSet<TokenBlacklist> TokenBlacklists { get; set; }

        /// <summary>
        /// 订阅计划
        /// </summary>
        public DbSet<SubscriptionPlan> SubscriptionPlans { get; set; }

        /// <summary>
        /// 网站配置
        /// </summary>
        public DbSet<WebsiteConfig> WebsiteConfigs { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // 配置Site实体
            builder.Entity<Site>(entity =>
            {
                entity.HasKey(s => s.Id);
                entity.HasIndex(s => s.Domain).IsUnique();
                entity.HasIndex(s => s.OwnerId);

                entity.Property(s => s.Name).IsRequired().HasMaxLength(100);
                entity.Property(s => s.Domain).IsRequired().HasMaxLength(255);
                entity.Property(s => s.Template).HasMaxLength(50);
                entity.Property(s => s.PrimaryColor).HasMaxLength(7);
                entity.Property(s => s.SecondaryColor).HasMaxLength(7);
                entity.Property(s => s.SubscriptionPlan).HasMaxLength(50);
                entity.Property(s => s.FooterText).HasMaxLength(1000);
                entity.Property(s => s.SiteKeywords).HasMaxLength(500);
                entity.Property(s => s.FaviconUrl).HasMaxLength(500);
                entity.Property(s => s.LogoUrl).HasMaxLength(500);
                entity.Property(s => s.Description).HasMaxLength(500);

                // 配置与用户的关系
                entity.HasOne(s => s.Owner)
                    .WithMany(u => u.OwnedSites)
                    .HasForeignKey(s => s.OwnerId)
                    .OnDelete(DeleteBehavior.Cascade);

                // PostgreSQL 原生支持 DateTime，无需转换
                entity.Property(s => s.CreatedAt).HasColumnType("timestamp with time zone");
                entity.Property(s => s.LastModifiedAt).HasColumnType("timestamp with time zone");
                entity.Property(s => s.SubscriptionExpiresAt).HasColumnType("timestamp with time zone");
            });

            // 配置SiteUserRole实体
            builder.Entity<SiteUserRole>(entity =>
            {
                entity.HasKey(sur => sur.Id);
                entity.HasIndex(sur => new { sur.UserId, sur.SiteId });


                // 配置关系
                entity.HasOne(sur => sur.User)
                    .WithMany(u => u.SiteRoles)
                    .HasForeignKey(sur => sur.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(sur => sur.Site)
                    .WithMany(s => s.SiteUsers)
                    .HasForeignKey(sur => sur.SiteId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(sur => sur.GrantedByUser)
                    .WithMany()
                    .HasForeignKey(sur => sur.GrantedBy)
                    .OnDelete(DeleteBehavior.SetNull);

                // PostgreSQL 原生支持 DateTime，无需转换
                entity.Property(sur => sur.GrantedAt).HasColumnType("timestamp with time zone");
            });

            // 配置ApplicationUser实体的新属性
            builder.Entity<ApplicationUser>(entity =>
            {
                entity.Property(u => u.PlatformRoles)
                    .HasConversion(
                        v => string.Join(',', v),
                        v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList()
                    )
                    .Metadata.SetValueComparer(new ValueComparer<List<string>>(
                        (c1, c2) => c1!.SequenceEqual(c2!),
                        c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                        c => c.ToList()));

                entity.Property(u => u.Permissions)
                    .HasConversion(
                        v => string.Join(',', v),
                        v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList()
                    )
                    .Metadata.SetValueComparer(new ValueComparer<List<string>>(
                        (c1, c2) => c1!.SequenceEqual(c2!),
                        c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                        c => c.ToList()));

                entity.Property(u => u.DisplayName).HasMaxLength(100);
                entity.Property(u => u.Bio).HasMaxLength(500);
                entity.Property(u => u.TimeZone).HasMaxLength(50);
                entity.Property(u => u.Language).HasMaxLength(10);
                entity.Property(u => u.AvatarUrl).HasMaxLength(500);

                // PostgreSQL 原生支持 DateTime，无需转换
                entity.Property(u => u.CreatedAt).HasColumnType("timestamp with time zone");
                entity.Property(u => u.LastLoginAt).HasColumnType("timestamp with time zone");
                entity.Property(u => u.PasswordResetTokenExpiration).HasColumnType("timestamp with time zone");
            });



            // 配置角色权限关系
            builder.Entity<Models.RolePermission>()
                .HasKey(rp => new { rp.RoleId, rp.Permission });

            builder.Entity<Models.RolePermission>()
                .HasOne<IdentityRole<Guid>>()
                .WithMany()
                .HasForeignKey(rp => rp.RoleId);

            // 配置令牌存储
            builder.Entity<TokenStore>()
                .HasIndex(t => t.UserId);

            builder.Entity<TokenBlacklist>()
                .HasIndex(t => t.Token)
                .IsUnique();

            // PostgreSQL 原生支持 DateTime，配置时区类型
            builder.Entity<TokenStore>()
                .Property(t => t.AccessTokenExpiration)
                .HasColumnType("timestamp with time zone");

            builder.Entity<TokenStore>()
                .Property(t => t.RefreshTokenExpiration)
                .HasColumnType("timestamp with time zone");

            builder.Entity<TokenStore>()
                .Property(t => t.CreatedAt)
                .HasColumnType("timestamp with time zone");

            builder.Entity<TokenStore>()
                .Property(t => t.LastUsedAt)
                .HasColumnType("timestamp with time zone");

            builder.Entity<TokenBlacklist>()
                .Property(t => t.RevokedAt)
                .HasColumnType("timestamp with time zone");

            builder.Entity<TokenBlacklist>()
                .Property(t => t.Expiration)
                .HasColumnType("timestamp with time zone");



            // 配置订阅计划唯一名称
            builder.Entity<SubscriptionPlan>()
                .HasIndex(sp => sp.Name)
                .IsUnique();
        }
    }
} 