using System;
using System.Threading.Tasks;
using MolySite.Core.Models;
using MolySite.Shared.Dtos;

namespace MolySite.Core.Interfaces
{
    /// <summary>
    /// 认证服务接口
    /// </summary>
    public interface IAuthService
    {
        /// <summary>
        /// 用户注册
        /// </summary>
        /// <param name="registerDto">注册信息</param>
        /// <returns>注册结果</returns>
        Task<Result<LoginResponseDto>> RegisterAsync(RegisterDto registerDto);

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="loginDto">登录信息</param>
        /// <returns>登录结果</returns>
        Task<Result<LoginResponseDto>> LoginAsync(LoginDto loginDto);

        /// <summary>
        /// 刷新令牌
        /// </summary>
        /// <param name="refreshTokenDto">刷新令牌信息</param>
        /// <returns>新的令牌</returns>
        Task<Result<LoginResponseDto>> RefreshTokenAsync(RefreshTokenDto refreshTokenDto);

        /// <summary>
        /// 用户登出
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>登出结果</returns>
        Task<Result<bool>> LogoutAsync(Guid userId);

        /// <summary>
        /// 更改密码
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="changePasswordDto">更改密码信息</param>
        /// <returns>更改结果</returns>
        Task<Result<bool>> ChangePasswordAsync(Guid userId, ChangePasswordDto changePasswordDto);

        /// <summary>
        /// 重置密码
        /// </summary>
        /// <param name="resetPasswordDto">重置密码信息</param>
        /// <returns>重置结果</returns>
        Task<Result<bool>> ResetPasswordAsync(ResetPasswordDto resetPasswordDto);

        /// <summary>
        /// 确认重置密码
        /// </summary>
        /// <param name="confirmResetDto">确认重置信息</param>
        /// <returns>确认结果</returns>
        Task<Result<bool>> ConfirmResetPasswordAsync(ConfirmResetPasswordDto confirmResetDto);

        /// <summary>
        /// 验证令牌
        /// </summary>
        /// <param name="token">令牌</param>
        /// <returns>验证结果</returns>
        Task<Result<bool>> ValidateTokenAsync(string token);

        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息</returns>
        Task<Result<UserDto>> GetUserAsync(Guid userId);
    }
}
