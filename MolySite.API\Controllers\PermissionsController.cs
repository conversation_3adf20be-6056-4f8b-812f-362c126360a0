using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MolySite.Core.Controllers;
using MolySite.Core.Interfaces;
using MolySite.Shared.Dtos;

namespace MolySite.API.Controllers
{
    /// <summary>
    /// 权限管理控制器
    /// </summary>
    [Route("api/[controller]")]
    [Authorize]
    public class PermissionsController : BaseApiController
    {
        private readonly IPermissionService _permissionService;
        private readonly ILogger<PermissionsController> _logger;

        public PermissionsController(
            IPermissionService permissionService,
            ILogger<PermissionsController> logger)
        {
            _permissionService = permissionService;
            _logger = logger;
        }

        /// <summary>
        /// 检查用户是否有指定网站权限
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <param name="permission">权限名称</param>
        /// <returns>是否有权限</returns>
        [HttpGet("site/{siteId}/check")]
        public async Task<IActionResult> CheckSitePermission(Guid siteId, [FromQuery] string permission)
        {
            var userId = GetCurrentUserId();
            var result = await _permissionService.HasSitePermissionAsync(userId, siteId, permission);
            
            return HandleResult(result);
        }

        /// <summary>
        /// 检查用户是否可以访问指定网站
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>是否可以访问</returns>
        [HttpGet("site/{siteId}/access")]
        public async Task<IActionResult> CheckSiteAccess(Guid siteId)
        {
            var userId = GetCurrentUserId();
            var result = await _permissionService.CanUserAccessSiteAsync(userId, siteId);
            
            return HandleResult(result);
        }

        /// <summary>
        /// 获取用户可访问的网站ID列表
        /// </summary>
        /// <returns>网站ID列表</returns>
        [HttpGet("accessible-sites")]
        public async Task<IActionResult> GetAccessibleSites()
        {
            var userId = GetCurrentUserId();
            var result = await _permissionService.GetUserAccessibleSiteIdsAsync(userId);
            
            return HandleResult(result);
        }

        /// <summary>
        /// 获取用户可编辑的网站ID列表
        /// </summary>
        /// <returns>网站ID列表</returns>
        [HttpGet("editable-sites")]
        public async Task<IActionResult> GetEditableSites()
        {
            var userId = GetCurrentUserId();
            var result = await _permissionService.GetUserEditableSiteIdsAsync(userId);
            
            return HandleResult(result);
        }

        /// <summary>
        /// 获取用户在指定网站的角色
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>用户角色</returns>
        [HttpGet("site/{siteId}/role")]
        public async Task<IActionResult> GetUserSiteRole(Guid siteId)
        {
            var userId = GetCurrentUserId();
            var result = await _permissionService.GetUserSitePermissionsAsync(userId, siteId);
            
            return HandleResult(result);
        }

        /// <summary>
        /// 获取网站的用户角色列表
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>用户角色列表</returns>
        [HttpGet("site/{siteId}/users")]
        public async Task<IActionResult> GetSiteUserRoles(Guid siteId)
        {
            var userId = GetCurrentUserId();
            var result = await _permissionService.GetSiteUserRolesAsync(siteId);
            
            return HandleResult(result);
        }

        /// <summary>
        /// 为用户分配网站角色
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <param name="request">分配角色请求</param>
        /// <returns>分配结果</returns>
        [HttpPost("site/{siteId}/assign-role")]
        public async Task<IActionResult> AssignSiteRole(Guid siteId, [FromBody] AssignRoleRequest request)
        {
            var userId = GetCurrentUserId();
            var result = await _permissionService.AssignSiteRoleAsync(request.UserId, siteId, request.Role, userId);
            
            return HandleResult(result);
        }

        /// <summary>
        /// 移除用户的网站角色
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <param name="targetUserId">目标用户ID</param>
        /// <returns>移除结果</returns>
        [HttpDelete("site/{siteId}/remove-role/{targetUserId}")]
        public async Task<IActionResult> RemoveSiteRole(Guid siteId, Guid targetUserId)
        {
            var userId = GetCurrentUserId();
            var result = await _permissionService.RemoveSiteRoleAsync(targetUserId, siteId, userId);
            
            return HandleResult(result);
        }
    }

    /// <summary>
    /// 分配角色请求
    /// </summary>
    public class AssignRoleRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// 角色类型
        /// </summary>
        public SiteRoleType Role { get; set; }
    }
}
