using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MolySite.Identity.Models;

namespace MolySite.Identity.Services
{
    /// <summary>
    /// 网站服务接口
    /// </summary>
    public interface ISiteService
    {
        /// <summary>
        /// 创建网站
        /// </summary>
        /// <param name="site">网站信息</param>
        /// <param name="ownerId">所有者ID</param>
        /// <returns>创建的网站</returns>
        Task<Site> CreateSiteAsync(Site site, Guid ownerId);

        /// <summary>
        /// 获取网站信息
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>网站信息</returns>
        Task<Site?> GetSiteByIdAsync(Guid siteId);

        /// <summary>
        /// 根据域名获取网站
        /// </summary>
        /// <param name="domain">域名</param>
        /// <returns>网站信息</returns>
        Task<Site?> GetSiteByDomainAsync(string domain);

        /// <summary>
        /// 更新网站信息
        /// </summary>
        /// <param name="site">网站信息</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateSiteAsync(Site site);

        /// <summary>
        /// 删除网站
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteSiteAsync(Guid siteId);

        /// <summary>
        /// 获取用户的所有网站
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>网站列表</returns>
        Task<List<Site>> GetUserSitesAsync(Guid userId);

        /// <summary>
        /// 获取所有网站（SuperAdmin专用）
        /// </summary>
        /// <returns>网站列表</returns>
        Task<List<Site>> GetAllSitesAsync();

        /// <summary>
        /// 检查域名是否可用
        /// </summary>
        /// <param name="domain">域名</param>
        /// <param name="excludeSiteId">排除的网站ID</param>
        /// <returns>是否可用</returns>
        Task<bool> IsDomainAvailableAsync(string domain, Guid? excludeSiteId = null);



        /// <summary>
        /// 获取网站的所有用户
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>用户角色列表</returns>
        Task<List<SiteUserRole>> GetSiteUsersAsync(Guid siteId);

        /// <summary>
        /// 发布网站
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>是否成功</returns>
        Task<bool> PublishSiteAsync(Guid siteId);

        /// <summary>
        /// 取消发布网站
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>是否成功</returns>
        Task<bool> UnpublishSiteAsync(Guid siteId);

        /// <summary>
        /// 更新网站订阅计划
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <param name="planName">计划名称</param>
        /// <param name="expiryDate">过期时间</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateSiteSubscriptionAsync(Guid siteId, string planName, DateTime? expiryDate);

        /// <summary>
        /// 获取网站统计信息
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>统计信息</returns>
        Task<SiteStatistics> GetSiteStatisticsAsync(Guid siteId);

        /// <summary>
        /// 检查网站存储空间使用情况
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>存储使用情况</returns>
        Task<StorageUsage> GetSiteStorageUsageAsync(Guid siteId);
    }

    /// <summary>
    /// 网站统计信息
    /// </summary>
    public class SiteStatistics
    {
        public int PageViews { get; set; }
        public int UniqueVisitors { get; set; }
        public DateTime LastVisit { get; set; }
        public int TotalPages { get; set; }
        public int TotalUsers { get; set; }
    }

    /// <summary>
    /// 存储使用情况
    /// </summary>
    public class StorageUsage
    {
        public int UsedMB { get; set; }
        public int MaxMB { get; set; }
        public double UsagePercentage => MaxMB > 0 ? (double)UsedMB / MaxMB * 100 : 0;
        public bool IsOverLimit => UsedMB > MaxMB;
    }
}
