using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MolySite.Core.Services;

namespace MolySite.Core.Extensions
{
    /// <summary>
    /// 简化角色体系迁移扩展方法
    /// </summary>
    public static class SimplifyRoleSystemMigrationExtensions
    {
        /// <summary>
        /// 添加简化角色体系迁移服务
        /// </summary>
        public static IServiceCollection AddSimplifyRoleSystemMigration(this IServiceCollection services)
        {
            services.AddScoped<SimplifyRoleSystemMigrationService>();
            return services;
        }

        /// <summary>
        /// 执行简化角色体系迁移
        /// </summary>
        public static async Task<IHost> MigrateSimplifyRoleSystemAsync(this IHost host)
        {
            using var scope = host.Services.CreateScope();
            var services = scope.ServiceProvider;
            var logger = services.GetRequiredService<ILogger<SimplifyRoleSystemMigrationService>>();

            try
            {
                var migrationService = services.GetRequiredService<SimplifyRoleSystemMigrationService>();

                // 检查是否需要迁移
                if (await migrationService.IsMigrationNeededAsync())
                {
                    logger.LogInformation("检测到需要执行简化角色体系迁移");
                    
                    var success = await migrationService.ExecuteMigrationAsync();
                    
                    if (success)
                    {
                        logger.LogInformation("简化角色体系迁移执行成功");
                    }
                    else
                    {
                        logger.LogError("简化角色体系迁移执行失败");
                    }
                }
                else
                {
                    logger.LogInformation("无需执行简化角色体系迁移");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "执行简化角色体系迁移时发生错误");
                // 不抛出异常，避免影响应用启动
            }

            return host;
        }
    }
}
