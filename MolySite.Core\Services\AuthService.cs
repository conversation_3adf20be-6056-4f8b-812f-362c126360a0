using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using MolySite.Core.Interfaces;
using MolySite.Core.Models;
using MolySite.Core.Exceptions;
using MolySite.Core.Constants;
using MolySite.Identity.Models;
using MolySite.Identity.Data;
using MolySite.Shared.Dtos;
using Microsoft.EntityFrameworkCore;

namespace MolySite.Core.Services
{
    /// <summary>
    /// Core层认证服务实现
    /// </summary>
    public class AuthService : IAuthService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly ApplicationDbContext _context;
        private readonly ITokenService _tokenService;
        private readonly ILogger<AuthService> _logger;

        public AuthService(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            ApplicationDbContext context,
            ITokenService tokenService,
            ILogger<AuthService> logger)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _context = context;
            _tokenService = tokenService;
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<Result<Guid>> RegisterAsync(RegisterDto registerDto)
        {
            try
            {
                _logger.LogInformation("开始用户注册流程: {UserName}, {Email}", registerDto.UserName, registerDto.Email);

                // 验证用户名和邮箱是否已存在
                var existingUser = await _userManager.FindByNameAsync(registerDto.UserName);
                if (existingUser != null)
                {
                    return Result<Guid>.ValidationFailure("UserName", "用户名已存在");
                }

                existingUser = await _userManager.FindByEmailAsync(registerDto.Email);
                if (existingUser != null)
                {
                    return Result<Guid>.ValidationFailure("Email", "邮箱已被注册");
                }

                // 创建用户
                var user = new ApplicationUser
                {
                    UserName = registerDto.UserName,
                    Email = registerDto.Email,
                    IsActive = true,
                    PlatformRole = PlatformRole.User,
                    PlatformRoles = new List<string> { Roles.User },
                    Permissions = PlatformPermissions.GetUserPermissions(),
                    CreatedAt = DateTime.UtcNow,
                    DisplayName = registerDto.UserName,
                    TimeZone = "UTC",
                    Language = "zh-CN",
                    EmailNotifications = true
                };

                var result = await _userManager.CreateAsync(user, registerDto.Password);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    _logger.LogWarning("用户创建失败: {Errors}", errors);
                    return Result<Guid>.Failure(errors, "USER_CREATION_FAILED");
                }

                // 添加角色
                await _userManager.AddToRoleAsync(user, Roles.User);

                _logger.LogInformation("用户注册成功: {UserId}", user.Id);
                return Result<Guid>.Success(user.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "用户注册过程中发生错误");
                return Result<Guid>.Failure("注册过程中发生错误", "REGISTRATION_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<LoginResponseDto>> LoginAsync(LoginDto loginDto)
        {
            try
            {
                _logger.LogInformation("开始用户登录流程: {UserNameOrEmail}", loginDto.UserNameOrEmail);

                // 查找用户
                var user = await _userManager.FindByNameAsync(loginDto.UserNameOrEmail) ??
                          await _userManager.FindByEmailAsync(loginDto.UserNameOrEmail);

                if (user == null)
                {
                    _logger.LogWarning("登录失败: 用户不存在 {UserNameOrEmail}", loginDto.UserNameOrEmail);
                    return Result<LoginResponseDto>.Failure("用户名或密码错误", "INVALID_CREDENTIALS");
                }

                if (!user.IsActive)
                {
                    _logger.LogWarning("登录失败: 用户已被禁用 {UserId}", user.Id);
                    return Result<LoginResponseDto>.Failure("账户已被禁用", "ACCOUNT_DISABLED");
                }

                // 验证密码
                var passwordResult = await _signInManager.CheckPasswordSignInAsync(user, loginDto.Password, false);
                if (!passwordResult.Succeeded)
                {
                    _logger.LogWarning("登录失败: 密码错误 {UserId}", user.Id);
                    return Result<LoginResponseDto>.Failure("用户名或密码错误", "INVALID_CREDENTIALS");
                }

                // 获取用户角色和权限
                var roles = await _userManager.GetRolesAsync(user);
                var permissions = user.Permissions ?? new List<string>();

                // 生成令牌
                var accessToken = _tokenService.GenerateAccessToken(
                    user.Id, 
                    user.UserName!, 
                    user.Email!, 
                    roles.ToList(), 
                    permissions);
                var refreshToken = _tokenService.GenerateRefreshToken();

                // 存储令牌
                await _tokenService.StoreTokenAsync(user.Id, accessToken, refreshToken);

                // 更新最后登录时间
                user.LastLoginAt = DateTime.UtcNow;
                await _userManager.UpdateAsync(user);

                var loginResponse = new LoginResponseDto
                {
                    Token = accessToken,
                    RefreshToken = refreshToken,
                    UserId = user.Id,
                    UserName = user.UserName!,
                    Email = user.Email!,
                    Roles = roles.ToList(),
                    Permissions = permissions
                };

                _logger.LogInformation("用户登录成功: {UserId}", user.Id);
                return Result<LoginResponseDto>.Success(loginResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "用户登录过程中发生错误");
                return Result<LoginResponseDto>.Failure("登录过程中发生错误", "LOGIN_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<LoginResponseDto>> RefreshTokenAsync(string refreshToken)
        {
            try
            {
                _logger.LogInformation("开始刷新令牌流程");

                var userIdResult = await _tokenService.ValidateRefreshTokenAsync(refreshToken);
                if (!userIdResult.IsSuccess)
                {
                    return Result<LoginResponseDto>.Failure("无效的刷新令牌", "INVALID_REFRESH_TOKEN");
                }

                var user = await _userManager.FindByIdAsync(userIdResult.Data.ToString());
                if (user == null || !user.IsActive)
                {
                    return Result<LoginResponseDto>.Failure("用户不存在或已被禁用", "USER_NOT_FOUND");
                }

                // 获取用户角色和权限
                var roles = await _userManager.GetRolesAsync(user);
                var permissions = user.Permissions ?? new List<string>();

                // 生成新令牌
                var newAccessToken = _tokenService.GenerateAccessToken(
                    user.Id, 
                    user.UserName!, 
                    user.Email!, 
                    roles.ToList(), 
                    permissions);
                var newRefreshToken = _tokenService.GenerateRefreshToken();

                // 存储新令牌
                await _tokenService.StoreTokenAsync(user.Id, newAccessToken, newRefreshToken);

                var loginResponse = new LoginResponseDto
                {
                    Token = newAccessToken,
                    RefreshToken = newRefreshToken,
                    UserId = user.Id,
                    UserName = user.UserName!,
                    Email = user.Email!,
                    Roles = roles.ToList(),
                    Permissions = permissions
                };

                _logger.LogInformation("令牌刷新成功: {UserId}", user.Id);
                return Result<LoginResponseDto>.Success(loginResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新令牌过程中发生错误");
                return Result<LoginResponseDto>.Failure("刷新令牌过程中发生错误", "REFRESH_TOKEN_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<bool>> ValidateTokenAsync(string token)
        {
            try
            {
                var validationResult = _tokenService.ValidateAccessToken(token);
                if (!validationResult.IsSuccess)
                {
                    return Result<bool>.Success(false);
                }

                // 检查令牌是否被撤销
                var isRevokedResult = await _tokenService.IsTokenRevokedAsync(token);
                if (!isRevokedResult.IsSuccess)
                {
                    return Result<bool>.Failure("检查令牌状态失败", "TOKEN_CHECK_ERROR");
                }

                return Result<bool>.Success(!isRevokedResult.Data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证令牌过程中发生错误");
                return Result<bool>.Failure("验证令牌过程中发生错误", "TOKEN_VALIDATION_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result> LogoutAsync(Guid userId, string? token = null)
        {
            try
            {
                _logger.LogInformation("开始用户注销流程: {UserId}", userId);

                // 撤销用户的令牌
                await _tokenService.RevokeTokenAsync(userId, token);

                _logger.LogInformation("用户注销成功: {UserId}", userId);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "用户注销过程中发生错误: {UserId}", userId);
                return Result.Failure("注销过程中发生错误", "LOGOUT_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<UserDto>> GetUserAsync(Guid userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null)
                {
                    return Result<UserDto>.Failure("用户不存在", "USER_NOT_FOUND");
                }

                var roles = await _userManager.GetRolesAsync(user);

                var userDto = new UserDto
                {
                    Id = user.Id,
                    UserName = user.UserName!,
                    Email = user.Email!,
                    DisplayName = user.DisplayName,
                    Bio = user.Bio,
                    AvatarUrl = user.AvatarUrl,
                    TimeZone = user.TimeZone,
                    Language = user.Language,
                    EmailNotifications = user.EmailNotifications,
                    IsActive = user.IsActive,
                    CreatedAt = user.CreatedAt,
                    LastLoginAt = user.LastLoginAt,
                    PlatformRoles = roles.ToList(),
                    Permissions = user.Permissions ?? new List<string>()
                };

                return Result<UserDto>.Success(userDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户信息过程中发生错误: {UserId}", userId);
                return Result<UserDto>.Failure("获取用户信息失败", "GET_USER_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result> UpdateUserAsync(Guid userId, UpdateUserDto updateDto)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null)
                {
                    return Result.Failure("用户不存在", "USER_NOT_FOUND");
                }

                // 更新用户信息
                if (updateDto.DisplayName != null)
                    user.DisplayName = updateDto.DisplayName;
                
                if (updateDto.Bio != null)
                    user.Bio = updateDto.Bio;
                
                if (updateDto.AvatarUrl != null)
                    user.AvatarUrl = updateDto.AvatarUrl;
                
                if (updateDto.TimeZone != null)
                    user.TimeZone = updateDto.TimeZone;
                
                if (updateDto.Language != null)
                    user.Language = updateDto.Language;
                
                if (updateDto.EmailNotifications.HasValue)
                    user.EmailNotifications = updateDto.EmailNotifications.Value;

                var result = await _userManager.UpdateAsync(user);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return Result.Failure(errors, "USER_UPDATE_FAILED");
                }

                _logger.LogInformation("用户信息更新成功: {UserId}", userId);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户信息过程中发生错误: {UserId}", userId);
                return Result.Failure("更新用户信息失败", "UPDATE_USER_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result> ChangePasswordAsync(Guid userId, string currentPassword, string newPassword)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null)
                {
                    return Result.Failure("用户不存在", "USER_NOT_FOUND");
                }

                var result = await _userManager.ChangePasswordAsync(user, currentPassword, newPassword);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return Result.Failure(errors, "PASSWORD_CHANGE_FAILED");
                }

                _logger.LogInformation("用户密码修改成功: {UserId}", userId);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "修改密码过程中发生错误: {UserId}", userId);
                return Result.Failure("修改密码失败", "CHANGE_PASSWORD_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result> ResetPasswordAsync(string email)
        {
            try
            {
                var user = await _userManager.FindByEmailAsync(email);
                if (user == null)
                {
                    // 为了安全，即使用户不存在也返回成功
                    return Result.Success();
                }

                var token = await _userManager.GeneratePasswordResetTokenAsync(user);
                user.PasswordResetToken = token;
                user.PasswordResetTokenExpiration = DateTime.UtcNow.AddHours(1);
                
                await _userManager.UpdateAsync(user);

                // TODO: 发送重置密码邮件
                _logger.LogInformation("密码重置令牌已生成: {UserId}", user.Id);
                
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置密码过程中发生错误: {Email}", email);
                return Result.Failure("重置密码失败", "RESET_PASSWORD_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result> ConfirmResetPasswordAsync(string email, string token, string newPassword)
        {
            try
            {
                var user = await _userManager.FindByEmailAsync(email);
                if (user == null)
                {
                    return Result.Failure("用户不存在", "USER_NOT_FOUND");
                }

                if (user.PasswordResetToken != token || 
                    user.PasswordResetTokenExpiration == null || 
                    user.PasswordResetTokenExpiration < DateTime.UtcNow)
                {
                    return Result.Failure("重置令牌无效或已过期", "INVALID_RESET_TOKEN");
                }

                var result = await _userManager.ResetPasswordAsync(user, token, newPassword);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return Result.Failure(errors, "PASSWORD_RESET_FAILED");
                }

                // 清除重置令牌
                user.PasswordResetToken = null;
                user.PasswordResetTokenExpiration = null;
                await _userManager.UpdateAsync(user);

                _logger.LogInformation("密码重置成功: {UserId}", user.Id);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "确认重置密码过程中发生错误: {Email}", email);
                return Result.Failure("确认重置密码失败", "CONFIRM_RESET_PASSWORD_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result> UpdateUserAsync(Guid userId, UpdateUserDto updateDto)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null)
                {
                    return Result.Failure("用户不存在", "USER_NOT_FOUND");
                }

                // 更新用户信息
                if (updateDto.DisplayName != null)
                    user.DisplayName = updateDto.DisplayName;

                if (updateDto.Bio != null)
                    user.Bio = updateDto.Bio;

                if (updateDto.AvatarUrl != null)
                    user.AvatarUrl = updateDto.AvatarUrl;

                if (updateDto.TimeZone != null)
                    user.TimeZone = updateDto.TimeZone;

                if (updateDto.Language != null)
                    user.Language = updateDto.Language;

                if (updateDto.EmailNotifications.HasValue)
                    user.EmailNotifications = updateDto.EmailNotifications.Value;

                var result = await _userManager.UpdateAsync(user);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return Result.Failure(errors, "USER_UPDATE_FAILED");
                }

                _logger.LogInformation("用户信息更新成功: {UserId}", userId);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户信息过程中发生错误: {UserId}", userId);
                return Result.Failure("更新用户信息失败", "UPDATE_USER_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result> ChangePasswordAsync(Guid userId, string currentPassword, string newPassword)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null)
                {
                    return Result.Failure("用户不存在", "USER_NOT_FOUND");
                }

                var result = await _userManager.ChangePasswordAsync(user, currentPassword, newPassword);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return Result.Failure(errors, "PASSWORD_CHANGE_FAILED");
                }

                _logger.LogInformation("用户密码修改成功: {UserId}", userId);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "修改密码过程中发生错误: {UserId}", userId);
                return Result.Failure("修改密码失败", "CHANGE_PASSWORD_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result> ResetPasswordAsync(string email)
        {
            try
            {
                var user = await _userManager.FindByEmailAsync(email);
                if (user == null)
                {
                    // 为了安全，即使用户不存在也返回成功
                    return Result.Success();
                }

                var token = await _userManager.GeneratePasswordResetTokenAsync(user);
                user.PasswordResetToken = token;
                user.PasswordResetTokenExpiration = DateTime.UtcNow.AddHours(1);

                await _userManager.UpdateAsync(user);

                // TODO: 发送重置密码邮件
                _logger.LogInformation("密码重置令牌已生成: {UserId}", user.Id);

                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置密码过程中发生错误: {Email}", email);
                return Result.Failure("重置密码失败", "RESET_PASSWORD_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result> ConfirmResetPasswordAsync(string email, string token, string newPassword)
        {
            try
            {
                var user = await _userManager.FindByEmailAsync(email);
                if (user == null)
                {
                    return Result.Failure("用户不存在", "USER_NOT_FOUND");
                }

                if (user.PasswordResetToken != token ||
                    user.PasswordResetTokenExpiration == null ||
                    user.PasswordResetTokenExpiration < DateTime.UtcNow)
                {
                    return Result.Failure("重置令牌无效或已过期", "INVALID_RESET_TOKEN");
                }

                var result = await _userManager.ResetPasswordAsync(user, token, newPassword);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return Result.Failure(errors, "PASSWORD_RESET_FAILED");
                }

                // 清除重置令牌
                user.PasswordResetToken = null;
                user.PasswordResetTokenExpiration = null;
                await _userManager.UpdateAsync(user);

                _logger.LogInformation("密码重置成功: {UserId}", user.Id);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "确认重置密码过程中发生错误: {Email}", email);
                return Result.Failure("确认重置密码失败", "CONFIRM_RESET_PASSWORD_ERROR");
            }
        }
    }
}
