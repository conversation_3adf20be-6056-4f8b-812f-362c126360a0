/* PublicLayout样式 */

.public-layout[b-zljedqn1vf] {
    padding-top: 76px; /* 为固定导航栏留出空间 */
}

.main-content[b-zljedqn1vf] {
    min-height: calc(100vh - 76px);
}

.navbar-brand[b-zljedqn1vf] {
    font-size: 1.5rem;
}

.text-gray-300[b-zljedqn1vf] {
    color: #d1d5db;
}

.text-gray-400[b-zljedqn1vf] {
    color: #9ca3af;
}

.border-gray-700[b-zljedqn1vf] {
    border-color: #374151;
}

.bg-gray-900[b-zljedqn1vf] {
    background-color: #111827;
}

.hover\:text-white:hover[b-zljedqn1vf] {
    color: white;
}

/* 平滑滚动 */
html[b-zljedqn1vf] {
    scroll-behavior: smooth;
}

/* 导航链接悬停效果 */
.nav-link:hover[b-zljedqn1vf] {
    color: #0d6efd !important;
}

/* 页脚样式 */
footer[b-zljedqn1vf] {
    padding: 4rem 0;
}

/* 响应式导航 */
@media (max-width: 991.98px) {
    .navbar-collapse[b-zljedqn1vf] {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #dee2e6;
    }
}
