{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"MolySite.Identity/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.0", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "9.0.0", "Microsoft.EntityFrameworkCore": "9.0.0", "Microsoft.EntityFrameworkCore.Design": "9.0.0", "MolySite.Shared": "1.0.0", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.0", "System.IdentityModel.Tokens.Jwt": "8.0.1"}, "runtime": {"MolySite.Identity.dll": {}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.0": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.0": {}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "9.0.0"}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/9.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.0", "Microsoft.Extensions.Identity.Stores": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Build.Framework/17.8.3": {}, "Microsoft.Build.Locator/1.7.8": {"runtime": {"lib/net6.0/Microsoft.Build.Locator.dll": {"assemblyVersion": "*******", "fileVersion": "1.7.8.28074"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {}, "Microsoft.CodeAnalysis.Common/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "7.0.0", "System.Reflection.Metadata": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "System.Composition": "7.0.0", "System.IO.Pipelines": "7.0.0", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"dependencies": {"Microsoft.Build.Framework": "17.8.3", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}, "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.EntityFrameworkCore/9.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.0", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.0", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52902"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.0": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52902"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.0": {}, "Microsoft.EntityFrameworkCore.Design/9.0.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Build.Framework": "17.8.3", "Microsoft.Build.Locator": "1.7.8", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.MSBuild": "4.8.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.0", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Mono.TextTemplating": "3.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52902"}}}, "Microsoft.EntityFrameworkCore.Relational/9.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.0", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52902"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.Extensions.Caching.Memory/9.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {}, "Microsoft.Extensions.DependencyModel/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Identity.Core/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}}, "Microsoft.Extensions.Identity.Stores/9.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.0", "Microsoft.Extensions.Identity.Core": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0"}}, "Microsoft.Extensions.Logging/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}}, "Microsoft.Extensions.Options/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.Extensions.Primitives/9.0.0": {}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Logging/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Protocols/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.0.1", "System.IdentityModel.Tokens.Jwt": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Tokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Mono.TextTemplating/3.0.0": {"dependencies": {"System.CodeDom": "6.0.0"}, "runtime": {"lib/net6.0/Mono.TextTemplating.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.1"}}}, "Npgsql/9.0.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.0", "Npgsql": "9.0.0"}, "runtime": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.CodeDom/6.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Collections.Immutable/7.0.0": {}, "System.Composition/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}}, "System.Composition.AttributedModel/7.0.0": {"runtime": {"lib/net7.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Convention/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Hosting/7.0.0": {"dependencies": {"System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Runtime/7.0.0": {"runtime": {"lib/net7.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.TypedParts/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "System.IO.Pipelines/7.0.0": {}, "System.Reflection.Metadata/7.0.0": {"dependencies": {"System.Collections.Immutable": "7.0.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Text.Json/9.0.0": {}, "System.Threading.Channels/7.0.0": {}, "MolySite.Shared/1.0.0": {"runtime": {"MolySite.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"MolySite.Identity/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bs+1Pq3vQdS2lTyxNUd9fEhtMsq3eLUpK36k2t56iDMVrk6OrAoFtvrQrTK0Y0OetTcJrUkGU7hBlf+ORzHLqQ==", "path": "microsoft.aspnetcore.authentication.jwtbearer/9.0.0", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-M1dzTEl+2+RqT4vWcqEpWasPXHd58wC93U7QMlmPSmx+qixyVxCQjZ183wr7Wa68b4pF7wC501MU9rdA0ZNhMg==", "path": "microsoft.aspnetcore.cryptography.internal/9.0.0", "hashPath": "microsoft.aspnetcore.cryptography.internal.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9X4cx2IHNpYb9ka984BjDpJnKkindW17Z2kR/RI5pbTcbVUVMJjiAKnBhAqH24KtAEf1AU64LD60byzCn0/n8w==", "path": "microsoft.aspnetcore.cryptography.keyderivation/9.0.0", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fwQkBQGaiRKDQWBc7PXxDiDXtsUsRPL88Jp0CqjqoDhd9p5uHSyuv6g3ALq2EbCvKcWk/7pOKsJiDomPvp/ptA==", "path": "microsoft.aspnetcore.identity.entityframeworkcore/9.0.0", "hashPath": "microsoft.aspnetcore.identity.entityframeworkcore.9.0.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512"}, "Microsoft.Build.Framework/17.8.3": {"type": "package", "serviceable": true, "sha512": "sha512-NrQZJW8TlKVPx72yltGb8SVz3P5mNRk9fNiD/ao8jRSk48WqIIdCn99q4IjlVmPcruuQ+yLdjNQLL8Rb4c916g==", "path": "microsoft.build.framework/17.8.3", "hashPath": "microsoft.build.framework.17.8.3.nupkg.sha512"}, "Microsoft.Build.Locator/1.7.8": {"type": "package", "serviceable": true, "sha512": "sha512-sPy10x527Ph16S2u0yGME4S6ohBKJ69WfjeGG/bvELYeZVmJdKjxgnlL8cJJJLGV/cZIRqSfB12UDB8ICakOog==", "path": "microsoft.build.locator/1.7.8", "hashPath": "microsoft.build.locator.1.7.8.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hashPath": "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "path": "microsoft.codeanalysis.common/4.8.0", "hashPath": "microsoft.codeanalysis.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+3+qfdb/aaGD8PZRCrsdobbzGs1m9u119SkkJt8e/mk3xLJz/udLtS2T6nY27OTXxBBw10HzAbC8Z9w08VyP/g==", "path": "microsoft.codeanalysis.csharp/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-3amm4tq4Lo8/BGvg9p3BJh3S9nKq2wqCXfS7138i69TUpo/bD+XvD0hNurpEBtcNZhi1FyutiomKJqVF39ugYA==", "path": "microsoft.codeanalysis.csharp.workspaces/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-LXyV+MJKsKRu3FGJA3OmSk40OUIa/dQCFLOnm5X8MNcujx7hzGu8o+zjXlb/cy5xUdZK2UKYb9YaQ2E8m9QehQ==", "path": "microsoft.codeanalysis.workspaces.common/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-IEYreI82QZKklp54yPHxZNG9EKSK6nHEkeuf+0Asie9llgS1gp0V1hw7ODG+QyoB7MuAnNQHmeV1Per/ECpv6A==", "path": "microsoft.codeanalysis.workspaces.msbuild/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wpG+nfnfDAw87R3ovAsUmjr3MZ4tYXf6bFqEPVAIKE6IfPml3DS//iX0DBnf8kWn5ZHSO5oi1m4d/Jf+1LifJQ==", "path": "microsoft.entityframeworkcore/9.0.0", "hashPath": "microsoft.entityframeworkcore.9.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fnmifFL8KaA4ZNLCVgfjCWhZUFxkrDInx5hR4qG7Q8IEaSiy/6VOSRFyx55oH7MV4y7wM3J3EE90nSpcVBI44Q==", "path": "microsoft.entityframeworkcore.abstractions/9.0.0", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qje+DzXJOKiXF72SL0XxNlDtTkvWWvmwknuZtFahY5hIQpRKO59qnGuERIQ3qlzuq5x4bAJ8WMbgU5DLhBgeOQ==", "path": "microsoft.entityframeworkcore.analyzers/9.0.0", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Pqo8I+yHJ3VQrAoY0hiSncf+5P7gN/RkNilK5e+/K/yKh+yAWxdUAI6t0TG26a9VPlCa9FhyklzyFvRyj3YG9A==", "path": "microsoft.entityframeworkcore.design/9.0.0", "hashPath": "microsoft.entityframeworkcore.design.9.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-j+msw6fWgAE9M3Q/5B9Uhv7pdAdAQUvFPJAiBJmoy+OXvehVbfbCE8ftMAa51Uo2ZeiqVnHShhnv4Y4UJJmUzA==", "path": "microsoft.entityframeworkcore.relational/9.0.0", "hashPath": "microsoft.entityframeworkcore.relational.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FPWZAa9c0H4dvOj351iR1jkUIs4u9ykL4Bm592yhjDyO5lCoWd+TMAHx2EMbarzUvCvgjWjJIoC6//Q9kH6YhA==", "path": "microsoft.extensions.caching.abstractions/9.0.0", "hashPath": "microsoft.extensions.caching.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zbnPX/JQ0pETRSUG9fNPBvpIq42Aufvs15gGYyNIMhCun9yhmWihz0WgsI7bSDPjxWTKBf8oX/zv6v2uZ3W9OQ==", "path": "microsoft.extensions.caching.memory/9.0.0", "hashPath": "microsoft.extensions.caching.memory.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lqvd7W3FGKUO1+ZoUEMaZ5XDJeWvjpy2/M/ptCGz3tXLD4HWVaSzjufsAsjemasBEg+2SxXVtYVvGt5r2nKDlg==", "path": "microsoft.extensions.configuration.abstractions/9.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MCPrg7v3QgNMr0vX4vzRXvkNGgLg8vKWX0nKCWUxu2uPyMsaRgiRc1tHBnbTcfJMhMKj2slE/j2M9oGkd25DNw==", "path": "microsoft.extensions.dependencyinjection/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+6f2qv2a3dLwd5w6JanPIPs47CxRbnk+ZocMJUhv9NxP88VlOcJYZs9jY+MYSjxvady08bUZn6qgiNh7DadGgg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-saxr2XzwgDU77LaQfYFXmddEDRUKHF4DaGMZkNB3qjdVSZlax3//dGJagJkKrGMIPNZs2jVFXITyCCR6UHJNdA==", "path": "microsoft.extensions.dependencymodel/9.0.0", "hashPath": "microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+cQjUs8PIheIMALzrf/e4gW6A/yOK8XYBxeEmAfLvVIaV9lsBGvVT0zjEZ1KPQDJ9nUeQ9uAw077J7LPUwv8wA==", "path": "microsoft.extensions.identity.core/9.0.0", "hashPath": "microsoft.extensions.identity.core.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Stores/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XG3opf0KgWoYAUdLRhrIvI46W+/E45Ov8rzgwr0omrq5u06MCrsuMm0nPmd+pIWjMXRxbBk1uL47zGyW1lI5Hw==", "path": "microsoft.extensions.identity.stores/9.0.0", "hashPath": "microsoft.extensions.identity.stores.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-crj<PERSON>yORoug0kK7RSNJBTeSE6VX8IQgLf3nUpTB9m62bPXp/tzbnOsnbe8TXEG0AASNaKZddnpHKw7fET8E++Pg==", "path": "microsoft.extensions.logging/9.0.0", "hashPath": "microsoft.extensions.logging.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-g0UfujELzlLbHoVG8kPKVBaW470Ewi+jnptGS9KUi6jcb+k2StujtK3m26DFSGGwQ/+bVgZfsWqNzlP6YOejvw==", "path": "microsoft.extensions.logging.abstractions/9.0.0", "hashPath": "microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-y2146b3jrPI3Q0lokKXdKLpmXqakYbDIPDV6r3M8SqvSf45WwOTzkyfDpxnZXJsJQEpAsAqjUq5Pu8RCJMjubg==", "path": "microsoft.extensions.options/9.0.0", "hashPath": "microsoft.extensions.options.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N3qEBzmLMYiASUlKxxFIISP4AiwuPTHF5uCh+2CWSwwzAJiIYx0kBJsS30cp1nvhSySFAVi30jecD307jV+8Kg==", "path": "microsoft.extensions.primitives/9.0.0", "hashPath": "microsoft.extensions.primitives.9.0.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OtlIWcyX01olfdevPKZdIPfBEvbcioDyBiE/Z2lHsopsMD7twcKtlN9kMevHmI5IIPhFpfwCIiR6qHQz1WHUIw==", "path": "microsoft.identitymodel.abstractions/8.0.1", "hashPath": "microsoft.identitymodel.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s6++gF9x0rQApQzOBbSyp4jUaAlwm+DroKfL8gdOHxs83k8SJfUXhuc46rDB3rNXBQ1MVRxqKUrqFhO/M0E97g==", "path": "microsoft.identitymodel.jsonwebtokens/8.0.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UCPF2exZqBXe7v/6sGNiM6zCQOUXXQ9+v5VTb9gPB8ZSUPnX53BxlN78v2jsbIvK9Dq4GovQxo23x8JgWvm/Qg==", "path": "microsoft.identitymodel.logging/8.0.1", "hashPath": "microsoft.identitymodel.logging.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uA2vpKqU3I2mBBEaeJAWPTjT9v1TZrGWKdgK6G5qJd03CLx83kdiqO9cmiK8/n1erkHzFBwU/RphP83aAe3i3g==", "path": "microsoft.identitymodel.protocols/8.0.1", "hashPath": "microsoft.identitymodel.protocols.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-AQDbfpL+yzuuGhO/mQhKNsp44pm5Jv8/BI4KiFXR7beVGZoSH35zMV3PrmcfvSTsyI6qrcR898NzUauD6SRigg==", "path": "microsoft.identitymodel.protocols.openidconnect/8.0.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-kDimB6Dkd3nkW2oZPDkMkVHfQt3IDqO5gL0oa8WVy3OP4uE8Ij+8TXnqg9TOd9ufjsY3IDiGz7pCUbnfL18tjg==", "path": "microsoft.identitymodel.tokens/8.0.1", "hashPath": "microsoft.identitymodel.tokens.8.0.1.nupkg.sha512"}, "Mono.TextTemplating/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YqueG52R/Xej4VVbKuRIodjiAhV0HR/XVbLbNrJhCZnzjnSjgMJ/dCdV0akQQxavX6hp/LC6rqLGLcXeQYU7XA==", "path": "mono.texttemplating/3.0.0", "hashPath": "mono.texttemplating.3.0.0.nupkg.sha512"}, "Npgsql/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zu1nCRt0gWP/GR0reYgg0Bl5o8qyNV7mVAgzAbVLRiAd1CYXcf/9nrubPH0mt93u8iGTKmYqWaLVECEAcE6IfQ==", "path": "npgsql/9.0.0", "hashPath": "npgsql.9.0.0.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ObngKFRLMBAeMqQzK7SC0Q6WZtWw0imPmEkVPo12yLVF3fioz2TN+w0mhNMJ5cVd/sLB2u+jei0bmA9sDMtkMw==", "path": "npgsql.entityframeworkcore.postgresql/9.0.0", "hashPath": "npgsql.entityframeworkcore.postgresql.9.0.0.nupkg.sha512"}, "System.CodeDom/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "path": "system.codedom/6.0.0", "hashPath": "system.codedom.6.0.0.nupkg.sha512"}, "System.Collections.Immutable/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dQPcs0U1IKnBdRDBkrCTi1FoajSTBzLcVTpjO4MBCMC7f4pDOIPzgBoX8JjG7X6uZRJ8EBxsi8+DR1JuwjnzOQ==", "path": "system.collections.immutable/7.0.0", "hashPath": "system.collections.immutable.7.0.0.nupkg.sha512"}, "System.Composition/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tRwgcAkDd85O8Aq6zHDANzQaq380cek9lbMg5Qma46u5BZXq/G+XvIYmu+UI+BIIZ9zssXLYrkTykEqxxvhcmg==", "path": "system.composition/7.0.0", "hashPath": "system.composition.7.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2QzClqjElKxgI1jK1Jztnq44/8DmSuTSGGahXqQ4TdEV0h9s2KikQZIgcEqVzR7OuWDFPGLHIprBJGQEPr8fAQ==", "path": "system.composition.attributedmodel/7.0.0", "hashPath": "system.composition.attributedmodel.7.0.0.nupkg.sha512"}, "System.Composition.Convention/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IMhTlpCs4HmlD8B+J8/kWfwX7vrBBOs6xyjSTzBlYSs7W4OET4tlkR/Sg9NG8jkdJH9Mymq0qGdYS1VPqRTBnQ==", "path": "system.composition.convention/7.0.0", "hashPath": "system.composition.convention.7.0.0.nupkg.sha512"}, "System.Composition.Hosting/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eB6gwN9S+54jCTBJ5bpwMOVerKeUfGGTYCzz3QgDr1P55Gg/Wb27ShfPIhLMjmZ3MoAKu8uUSv6fcCdYJTN7Bg==", "path": "system.composition.hosting/7.0.0", "hashPath": "system.composition.hosting.7.0.0.nupkg.sha512"}, "System.Composition.Runtime/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aZJ1Zr5Txe925rbo4742XifEyW0MIni1eiUebmcrP3HwLXZ3IbXUj4MFMUH/RmnJOAQiS401leg/2Sz1MkApDw==", "path": "system.composition.runtime/7.0.0", "hashPath": "system.composition.runtime.7.0.0.nupkg.sha512"}, "System.Composition.TypedParts/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZK0KNPfbtxVceTwh+oHNGUOYV2WNOHReX2AXipuvkURC7s/jPwoWfsu3SnDBDgofqbiWr96geofdQ2erm/KTHg==", "path": "system.composition.typedparts/7.0.0", "hashPath": "system.composition.typedparts.7.0.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GJw3bYkWpOgvN3tJo5X4lYUeIFA2HD293FPUhKmp7qxS+g5ywAb34Dnd3cDAFLkcMohy5XTpoaZ4uAHuw0uSPQ==", "path": "system.identitymodel.tokens.jwt/8.0.1", "hashPath": "system.identitymodel.tokens.jwt.8.0.1.nupkg.sha512"}, "System.IO.Pipelines/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jRn6JYnNPW6xgQazROBLSfpdoczRw694vO5kKvMcNnpXuolEixUyw6IBuBs2Y2mlSX/LdLvyyWmfXhaI3ND1Yg==", "path": "system.io.pipelines/7.0.0", "hashPath": "system.io.pipelines.7.0.0.nupkg.sha512"}, "System.Reflection.Metadata/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MclTG61lsD9sYdpNz9xsKBzjsmsfCtcMZYXz/IUr2zlhaTaABonlr1ESeompTgM+Xk+IwtGYU7/voh3YWB/fWw==", "path": "system.reflection.metadata/7.0.0", "hashPath": "system.reflection.metadata.7.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Text.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-js7+qAu/9mQvnhA4EfGMZNEzXtJCDxgkgj8ohuxq/Qxv+R56G+ljefhiJHOxTNiw54q8vmABCWUwkMulNdlZ4A==", "path": "system.text.json/9.0.0", "hashPath": "system.text.json.9.0.0.nupkg.sha512"}, "System.Threading.Channels/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA==", "path": "system.threading.channels/7.0.0", "hashPath": "system.threading.channels.7.0.0.nupkg.sha512"}, "MolySite.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}