using MolySite.Core.Models;
using MolySite.Identity.Models;

namespace MolySite.Core.Services
{
    /// <summary>
    /// 网站关注服务接口
    /// </summary>
    public interface IFollowService
    {
        /// <summary>
        /// 关注网站
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="siteId">网站ID</param>
        /// <returns>关注结果</returns>
        Task<Result> FollowSiteAsync(Guid userId, Guid siteId);

        /// <summary>
        /// 取消关注网站
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="siteId">网站ID</param>
        /// <returns>取消关注结果</returns>
        Task<Result> UnfollowSiteAsync(Guid userId, Guid siteId);

        /// <summary>
        /// 检查用户是否关注了网站
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="siteId">网站ID</param>
        /// <returns>是否关注</returns>
        Task<Result<bool>> IsFollowingSiteAsync(Guid userId, Guid siteId);

        /// <summary>
        /// 获取用户关注的网站列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>关注的网站列表</returns>
        Task<Result<List<Site>>> GetFollowedSitesAsync(Guid userId);

        /// <summary>
        /// 获取网站的关注者列表
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>关注者列表</returns>
        Task<Result<List<ApplicationUser>>> GetSiteFollowersAsync(Guid siteId);

        /// <summary>
        /// 获取网站的关注者数量
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>关注者数量</returns>
        Task<Result<int>> GetSiteFollowerCountAsync(Guid siteId);

        /// <summary>
        /// 向网站关注者发送通知
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <param name="title">通知标题</param>
        /// <param name="content">通知内容</param>
        /// <param name="senderId">发送者ID</param>
        /// <returns>发送结果</returns>
        Task<Result> SendNotificationToFollowersAsync(Guid siteId, string title, string content, Guid senderId);
    }
}
