{"Version": 1, "Hash": "Vew/jdfR13yrA0fF7MPtOKcsCl2OiAbg4jxpgOkT1Mw=", "Source": "MolySite.Web", "BasePath": "_content/MolySite.Web", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "MolySite.Web\\wwwroot", "Source": "MolySite.Web", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "Pattern": "**"}], "Assets": [{"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\4yau8vpo2c-2zmnmq58fl.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "app#[.{fingerprint=2zmnmq58fl}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mzw5ltmlnn", "Integrity": "vmCTBoClvSCFZfmb8tAJuYUolfSfB2KO9sVfZTLEF6s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\app.css", "FileLength": 2083, "LastWriteTime": "2025-06-24T15:26:39+00:00"}, {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\9qndxilbbr-26f9b7qkas.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap-icons/font/bootstrap-icons.min#[.{fingerprint=26f9b7qkas}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap-icons\\font\\bootstrap-icons.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6um1ts2axh", "Integrity": "IglclAz7SLu0YmGB7PxVX8owYskkCvVdjd0rSP0X+mw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap-icons\\font\\bootstrap-icons.min.css", "FileLength": 13894, "LastWriteTime": "2025-06-24T15:26:39+00:00"}, {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\if8keuzty8-56vxggxec6.gz", "SourceId": "MolySite.Web", "SourceType": "Computed", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "MolySite.Web#[.{fingerprint=56vxggxec6}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\MolySite.Web.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "03s87cqhwi", "Integrity": "1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\MolySite.Web.bundle.scp.css", "FileLength": 4197, "LastWriteTime": "2025-06-24T15:26:39+00:00"}, {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\ll5cbn73iy-56vxggxec6.gz", "SourceId": "MolySite.Web", "SourceType": "Computed", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "MolySite.Web#[.{fingerprint=56vxggxec6}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\MolySite.Web.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "03s87cqhwi", "Integrity": "1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\MolySite.Web.styles.css", "FileLength": 4197, "LastWriteTime": "2025-06-24T15:26:39+00:00"}, {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\qftk4dc618-5ipweew5fc.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "css/dashboard#[.{fingerprint=5ipweew5fc}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\dashboard.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\dashboard.css", "FileLength": 0, "LastWriteTime": "2025-06-24T15:26:39+00:00"}, {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\x7wqaayaif-1wi6zlpeou.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "js/performance-monitor#[.{fingerprint=1wi6zlpeou}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\performance-monitor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kefh7utro5", "Integrity": "xGhPw4+DdRO5TZd2TfPCMXv9GqX/iHWf6ehSOEtuFjg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\performance-monitor.js", "FileLength": 1957, "LastWriteTime": "2025-06-24T15:26:39+00:00"}, {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\y2fooq3isi-frz2k1ad57.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "css/tailwind#[.{fingerprint=frz2k1ad57}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9ixafax6j8", "Integrity": "IS/sCGGsIsV2JtjyIh3KAp86XMLn8tE3QRPaQgNo51k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\tailwind.css", "FileLength": 11985, "LastWriteTime": "2025-06-24T15:26:39+00:00"}, {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\yygdecoiyq-ab62h7jd1e.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "js/app#[.{fingerprint=ab62h7jd1e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pbntl70d18", "Integrity": "VQz0orU4C6NuYKE1xPMzkdBfauBccLAUYOmEliARlx0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\app.js", "FileLength": 1255, "LastWriteTime": "2025-06-24T15:26:39+00:00"}, {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\MolySite.Web.styles.css", "SourceId": "MolySite.Web", "SourceType": "Computed", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\", "BasePath": "_content/MolySite.Web", "RelativePath": "MolySite.Web#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "56vxggxec6", "Integrity": "DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\MolySite.Web.styles.css", "FileLength": 19394, "LastWriteTime": "2025-06-24T15:26:39+00:00"}, {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\MolySite.Web.bundle.scp.css", "SourceId": "MolySite.Web", "SourceType": "Computed", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\", "BasePath": "_content/MolySite.Web", "RelativePath": "MolySite.Web#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "56vxggxec6", "Integrity": "DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\MolySite.Web.bundle.scp.css", "FileLength": 19394, "LastWriteTime": "2025-06-24T15:26:39+00:00"}, {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\app.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2zmnmq58fl", "Integrity": "DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 3624, "LastWriteTime": "2025-06-23T13:38:46+00:00"}, {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\dashboard.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "css/dashboard#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\dashboard.css", "FileLength": 0, "LastWriteTime": "2025-06-13T13:44:39+00:00"}, {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\tailwind.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "css/tailwind#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "frz2k1ad57", "Integrity": "dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\tailwind.css", "FileLength": 77985, "LastWriteTime": "2025-06-20T07:41:23+00:00"}, {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\favicon.png", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-06-12T04:09:57+00:00"}, {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\app.js", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "js/app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ab62h7jd1e", "Integrity": "dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\app.js", "FileLength": 2906, "LastWriteTime": "2025-06-23T13:16:06+00:00"}, {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\performance-monitor.js", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "js/performance-monitor#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1wi6zlpeou", "Integrity": "ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\performance-monitor.js", "FileLength": 6295, "LastWriteTime": "2025-06-23T13:47:53+00:00"}, {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap-icons\\font\\bootstrap-icons.min.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap-icons/font/bootstrap-icons.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "26f9b7qkas", "Integrity": "9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap-icons\\font\\bootstrap-icons.min.css", "FileLength": 85875, "LastWriteTime": "2025-06-16T06:55:12+00:00"}], "Endpoints": [{"Route": "app.2zmnmq58fl.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\4yau8vpo2c-2zmnmq58fl.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000479846449"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2083"}, {"Name": "ETag", "Value": "\"vmCTBoClvSCFZfmb8tAJuYUolfSfB2KO9sVfZTLEF6s=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2zmnmq58fl"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY="}]}, {"Route": "app.2zmnmq58fl.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3624"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 13:38:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2zmnmq58fl"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY="}]}, {"Route": "app.2zmnmq58fl.css.gz", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\4yau8vpo2c-2zmnmq58fl.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2083"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vmCTBoClvSCFZfmb8tAJuYUolfSfB2KO9sVfZTLEF6s=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2zmnmq58fl"}, {"Name": "label", "Value": "app.css.gz"}, {"Name": "integrity", "Value": "sha256-vmCTBoClvSCFZfmb8tAJuYUolfSfB2KO9sVfZTLEF6s="}]}, {"Route": "app.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\4yau8vpo2c-2zmnmq58fl.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000479846449"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2083"}, {"Name": "ETag", "Value": "\"vmCTBoClvSCFZfmb8tAJuYUolfSfB2KO9sVfZTLEF6s=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY="}]}, {"Route": "app.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3624"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 13:38:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY="}]}, {"Route": "app.css.gz", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\4yau8vpo2c-2zmnmq58fl.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2083"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vmCTBoClvSCFZfmb8tAJuYUolfSfB2KO9sVfZTLEF6s=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vmCTBoClvSCFZfmb8tAJuYUolfSfB2KO9sVfZTLEF6s="}]}, {"Route": "css/dashboard.5ipweew5fc.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\qftk4dc618-5ipweew5fc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "css/dashboard.css"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "css/dashboard.5ipweew5fc.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\dashboard.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 13:44:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "css/dashboard.css"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "css/dashboard.5ipweew5fc.css.gz", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\qftk4dc618-5ipweew5fc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "css/dashboard.css.gz"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "css/dashboard.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\qftk4dc618-5ipweew5fc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "css/dashboard.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\dashboard.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 13:44:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "css/dashboard.css.gz", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\qftk4dc618-5ipweew5fc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "css/tailwind.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\y2fooq3isi-frz2k1ad57.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083430669"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11985"}, {"Name": "ETag", "Value": "\"IS/sCGGsIsV2JtjyIh3KAp86XMLn8tE3QRPaQgNo51k=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU="}]}, {"Route": "css/tailwind.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "77985"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 07:41:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU="}]}, {"Route": "css/tailwind.css.gz", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\y2fooq3isi-frz2k1ad57.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11985"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IS/sCGGsIsV2JtjyIh3KAp86XMLn8tE3QRPaQgNo51k=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IS/sCGGsIsV2JtjyIh3KAp86XMLn8tE3QRPaQgNo51k="}]}, {"Route": "css/tailwind.frz2k1ad57.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\y2fooq3isi-frz2k1ad57.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083430669"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11985"}, {"Name": "ETag", "Value": "\"IS/sCGGsIsV2JtjyIh3KAp86XMLn8tE3QRPaQgNo51k=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "frz2k1ad57"}, {"Name": "label", "Value": "css/tailwind.css"}, {"Name": "integrity", "Value": "sha256-dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU="}]}, {"Route": "css/tailwind.frz2k1ad57.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "77985"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 07:41:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "frz2k1ad57"}, {"Name": "label", "Value": "css/tailwind.css"}, {"Name": "integrity", "Value": "sha256-dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU="}]}, {"Route": "css/tailwind.frz2k1ad57.css.gz", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\y2fooq3isi-frz2k1ad57.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11985"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IS/sCGGsIsV2JtjyIh3KAp86XMLn8tE3QRPaQgNo51k=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "frz2k1ad57"}, {"Name": "label", "Value": "css/tailwind.css.gz"}, {"Name": "integrity", "Value": "sha256-IS/sCGGsIsV2JtjyIh3KAp86XMLn8tE3QRPaQgNo51k="}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 04:09:57 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "label", "Value": "favicon.png"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "favicon.png", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 04:09:57 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "js/app.ab62h7jd1e.js", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\yygdecoiyq-ab62h7jd1e.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000796178344"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1255"}, {"Name": "ETag", "Value": "\"VQz0orU4C6NuYKE1xPMzkdBfauBccLAUYOmEliARlx0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ab62h7jd1e"}, {"Name": "label", "Value": "js/app.js"}, {"Name": "integrity", "Value": "sha256-dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY="}]}, {"Route": "js/app.ab62h7jd1e.js", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2906"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 13:16:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ab62h7jd1e"}, {"Name": "label", "Value": "js/app.js"}, {"Name": "integrity", "Value": "sha256-dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY="}]}, {"Route": "js/app.ab62h7jd1e.js.gz", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\yygdecoiyq-ab62h7jd1e.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1255"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VQz0orU4C6NuYKE1xPMzkdBfauBccLAUYOmEliARlx0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ab62h7jd1e"}, {"Name": "label", "Value": "js/app.js.gz"}, {"Name": "integrity", "Value": "sha256-VQz0orU4C6NuYKE1xPMzkdBfauBccLAUYOmEliARlx0="}]}, {"Route": "js/app.js", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\yygdecoiyq-ab62h7jd1e.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000796178344"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1255"}, {"Name": "ETag", "Value": "\"VQz0orU4C6NuYKE1xPMzkdBfauBccLAUYOmEliARlx0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY="}]}, {"Route": "js/app.js", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2906"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 13:16:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY="}]}, {"Route": "js/app.js.gz", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\yygdecoiyq-ab62h7jd1e.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1255"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VQz0orU4C6NuYKE1xPMzkdBfauBccLAUYOmEliARlx0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VQz0orU4C6NuYKE1xPMzkdBfauBccLAUYOmEliARlx0="}]}, {"Route": "js/performance-monitor.1wi6zlpeou.js", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\x7wqaayaif-1wi6zlpeou.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000510725230"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1957"}, {"Name": "ETag", "Value": "\"xGhPw4+DdRO5TZd2TfPCMXv9GqX/iHWf6ehSOEtuFjg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1wi6zlpeou"}, {"Name": "label", "Value": "js/performance-monitor.js"}, {"Name": "integrity", "Value": "sha256-ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc="}]}, {"Route": "js/performance-monitor.1wi6zlpeou.js", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\performance-monitor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6295"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 13:47:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1wi6zlpeou"}, {"Name": "label", "Value": "js/performance-monitor.js"}, {"Name": "integrity", "Value": "sha256-ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc="}]}, {"Route": "js/performance-monitor.1wi6zlpeou.js.gz", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\x7wqaayaif-1wi6zlpeou.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1957"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xGhPw4+DdRO5TZd2TfPCMXv9GqX/iHWf6ehSOEtuFjg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1wi6zlpeou"}, {"Name": "label", "Value": "js/performance-monitor.js.gz"}, {"Name": "integrity", "Value": "sha256-xGhPw4+DdRO5TZd2TfPCMXv9GqX/iHWf6ehSOEtuFjg="}]}, {"Route": "js/performance-monitor.js", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\x7wqaayaif-1wi6zlpeou.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000510725230"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1957"}, {"Name": "ETag", "Value": "\"xGhPw4+DdRO5TZd2TfPCMXv9GqX/iHWf6ehSOEtuFjg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc="}]}, {"Route": "js/performance-monitor.js", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\performance-monitor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6295"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 13:47:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc="}]}, {"Route": "js/performance-monitor.js.gz", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\x7wqaayaif-1wi6zlpeou.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1957"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xGhPw4+DdRO5TZd2TfPCMXv9GqX/iHWf6ehSOEtuFjg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xGhPw4+DdRO5TZd2TfPCMXv9GqX/iHWf6ehSOEtuFjg="}]}, {"Route": "lib/bootstrap-icons/font/bootstrap-icons.min.26f9b7qkas.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\9qndxilbbr-26f9b7qkas.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071968334"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13894"}, {"Name": "ETag", "Value": "\"IglclAz7SLu0YmGB7PxVX8owYskkCvVdjd0rSP0X+mw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "26f9b7qkas"}, {"Name": "label", "Value": "lib/bootstrap-icons/font/bootstrap-icons.min.css"}, {"Name": "integrity", "Value": "sha256-9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI="}]}, {"Route": "lib/bootstrap-icons/font/bootstrap-icons.min.26f9b7qkas.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap-icons\\font\\bootstrap-icons.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85875"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 06:55:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "26f9b7qkas"}, {"Name": "label", "Value": "lib/bootstrap-icons/font/bootstrap-icons.min.css"}, {"Name": "integrity", "Value": "sha256-9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI="}]}, {"Route": "lib/bootstrap-icons/font/bootstrap-icons.min.26f9b7qkas.css.gz", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\9qndxilbbr-26f9b7qkas.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13894"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IglclAz7SLu0YmGB7PxVX8owYskkCvVdjd0rSP0X+mw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "26f9b7qkas"}, {"Name": "label", "Value": "lib/bootstrap-icons/font/bootstrap-icons.min.css.gz"}, {"Name": "integrity", "Value": "sha256-IglclAz7SLu0YmGB7PxVX8owYskkCvVdjd0rSP0X+mw="}]}, {"Route": "lib/bootstrap-icons/font/bootstrap-icons.min.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\9qndxilbbr-26f9b7qkas.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071968334"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13894"}, {"Name": "ETag", "Value": "\"IglclAz7SLu0YmGB7PxVX8owYskkCvVdjd0rSP0X+mw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI="}]}, {"Route": "lib/bootstrap-icons/font/bootstrap-icons.min.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap-icons\\font\\bootstrap-icons.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85875"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 06:55:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI="}]}, {"Route": "lib/bootstrap-icons/font/bootstrap-icons.min.css.gz", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\9qndxilbbr-26f9b7qkas.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13894"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IglclAz7SLu0YmGB7PxVX8owYskkCvVdjd0rSP0X+mw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IglclAz7SLu0YmGB7PxVX8owYskkCvVdjd0rSP0X+mw="}]}, {"Route": "MolySite.Web.56vxggxec6.bundle.scp.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\if8keuzty8-56vxggxec6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000238208671"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4197"}, {"Name": "ETag", "Value": "\"1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "56vxggxec6"}, {"Name": "label", "Value": "MolySite.Web.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk="}]}, {"Route": "MolySite.Web.56vxggxec6.bundle.scp.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\MolySite.Web.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "56vxggxec6"}, {"Name": "label", "Value": "MolySite.Web.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk="}]}, {"Route": "MolySite.Web.56vxggxec6.bundle.scp.css.gz", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\if8keuzty8-56vxggxec6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4197"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "56vxggxec6"}, {"Name": "label", "Value": "MolySite.Web.bundle.scp.css.gz"}, {"Name": "integrity", "Value": "sha256-1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU="}]}, {"Route": "MolySite.Web.56vxggxec6.styles.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\ll5cbn73iy-56vxggxec6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000238208671"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4197"}, {"Name": "ETag", "Value": "\"1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "56vxggxec6"}, {"Name": "label", "Value": "MolySite.Web.styles.css"}, {"Name": "integrity", "Value": "sha256-DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk="}]}, {"Route": "MolySite.Web.56vxggxec6.styles.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\MolySite.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "56vxggxec6"}, {"Name": "label", "Value": "MolySite.Web.styles.css"}, {"Name": "integrity", "Value": "sha256-DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk="}]}, {"Route": "MolySite.Web.56vxggxec6.styles.css.gz", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\ll5cbn73iy-56vxggxec6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4197"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "56vxggxec6"}, {"Name": "label", "Value": "MolySite.Web.styles.css.gz"}, {"Name": "integrity", "Value": "sha256-1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU="}]}, {"Route": "MolySite.Web.bundle.scp.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\if8keuzty8-56vxggxec6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000238208671"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4197"}, {"Name": "ETag", "Value": "\"1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk="}]}, {"Route": "MolySite.Web.bundle.scp.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\MolySite.Web.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk="}]}, {"Route": "MolySite.Web.bundle.scp.css.gz", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\if8keuzty8-56vxggxec6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4197"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU="}]}, {"Route": "MolySite.Web.styles.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\ll5cbn73iy-56vxggxec6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000238208671"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4197"}, {"Name": "ETag", "Value": "\"1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk="}]}, {"Route": "MolySite.Web.styles.css", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\MolySite.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk="}]}, {"Route": "MolySite.Web.styles.css.gz", "AssetFile": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\ll5cbn73iy-56vxggxec6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4197"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:26:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU="}]}]}