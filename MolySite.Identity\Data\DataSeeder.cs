using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using MolySite.Identity.Authorization;
using MolySite.Identity.Models;
using MolySite.Shared.Dtos;

namespace MolySite.Identity.Data
{
    /// <summary>
    /// 数据种子类，用于初始化租户、角色和权限
    /// </summary>
    public static class DataSeeder
    {
        /// <summary>
        /// 种子数据初始化方法
        /// </summary>
        public static async Task SeedDataAsync(
            ApplicationDbContext context, 
            UserManager<ApplicationUser> userManager, 
            RoleManager<IdentityRole<Guid>> roleManager)
        {
            // 确保数据库已创建
            await context.Database.MigrateAsync();

            // 种子订阅计划数据
            await SeedSubscriptionPlansAsync(context);



            // 种子角色数据
            await SeedRolesAsync(roleManager);

            // 种子管理员用户
            await SeedAdminUserAsync(context, userManager);

            // 种子网站配置数据
            await SeedWebsiteConfigsAsync(context);

            // 种子示例网站
            await SeedSitesAsync(context, userManager);
        }

        /// <summary>
        /// 种子订阅计划数据
        /// </summary>
        private static async Task SeedSubscriptionPlansAsync(ApplicationDbContext context)
        {
            if (!await context.SubscriptionPlans.AnyAsync())
            {
                var plans = new List<SubscriptionPlan>
                {
                    new SubscriptionPlan
                    {
                        Id = Guid.NewGuid(),
                        Name = "Free",
                        Description = "适用于个人或小型项目的免费计划",
                        MonthlyPrice = 0,
                        YearlyPrice = 0,
                        MaxUsers = 5,
                        MaxStorageMB = 100,
                        SupportsCustomDomain = false,
                        SupportsAdvancedThemes = false,
                        SupportsApiAccess = false,
                        SupportsPrioritySupport = false,
                        IsDefault = true,
                        IsActive = true
                    },
                    new SubscriptionPlan
                    {
                        Id = Guid.NewGuid(),
                        Name = "Basic",
                        Description = "适用于小型团队的基础计划",
                        MonthlyPrice = 9.99m,
                        YearlyPrice = 99.99m,
                        MaxUsers = 10,
                        MaxStorageMB = 500,
                        SupportsCustomDomain = true,
                        SupportsAdvancedThemes = false,
                        SupportsApiAccess = false,
                        SupportsPrioritySupport = false,
                        IsDefault = false,
                        IsActive = true
                    },
                    new SubscriptionPlan
                    {
                        Id = Guid.NewGuid(),
                        Name = "Premium",
                        Description = "适用于中型团队的高级计划",
                        MonthlyPrice = 29.99m,
                        YearlyPrice = 299.99m,
                        MaxUsers = 50,
                        MaxStorageMB = 2048,
                        SupportsCustomDomain = true,
                        SupportsAdvancedThemes = true,
                        SupportsApiAccess = true,
                        SupportsPrioritySupport = false,
                        IsDefault = false,
                        IsActive = true
                    },
                    new SubscriptionPlan
                    {
                        Id = Guid.NewGuid(),
                        Name = "Enterprise",
                        Description = "适用于大型企业的企业级计划",
                        MonthlyPrice = 99.99m,
                        YearlyPrice = 999.99m,
                        MaxUsers = 200,
                        MaxStorageMB = 10240,
                        SupportsCustomDomain = true,
                        SupportsAdvancedThemes = true,
                        SupportsApiAccess = true,
                        SupportsPrioritySupport = true,
                        IsDefault = false,
                        IsActive = true
                    }
                };

                context.SubscriptionPlans.AddRange(plans);
                await context.SaveChangesAsync();
            }
        }



        /// <summary>
        /// 种子网站配置数据
        /// </summary>
        private static Task SeedWebsiteConfigsAsync(ApplicationDbContext context)
        {
            // 网站配置现在与Site关联，不再需要预先种子数据
            // 配置将在创建站点时自动创建
            return Task.CompletedTask;
        }

        /// <summary>
        /// 种子角色数据（新架构：三层角色体系）
        /// </summary>
        private static async Task SeedRolesAsync(RoleManager<IdentityRole<Guid>> roleManager)
        {
            var roles = new[]
            {
                "SuperAdmin",  // 平台超级管理员
                "SiteOwner",   // 网站所有者（注册默认角色）
                "SiteEditor"   // 网站编辑者
            };

            foreach (var roleName in roles)
            {
                if (!await roleManager.RoleExistsAsync(roleName))
                {
                    var role = new IdentityRole<Guid>
                    {
                        Name = roleName,
                        NormalizedName = roleName.ToUpper()
                    };
                    await roleManager.CreateAsync(role);
                }
            }
        }

        /// <summary>
        /// 种子管理员用户（新架构）
        /// </summary>
        private static async Task SeedAdminUserAsync(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager)
        {
            // 检查是否已存在超级管理员用户
            if (await userManager.FindByNameAsync("superadmin") == null)
            {
                var superAdminUser = new ApplicationUser
                {
                    UserName = "superadmin",
                    Email = "<EMAIL>",
                    IsActive = true,
                    PlatformRole = PlatformRole.SuperAdmin,
                    PlatformRoles = new List<string> { "SuperAdmin" },
                    Permissions = SitePermissions.GetSuperAdminPermissions(),
                    CreatedAt = DateTime.UtcNow,
                    DisplayName = "超级管理员",
                    TimeZone = "UTC",
                    Language = "zh-CN",
                    EmailNotifications = true
                };

                var result = await userManager.CreateAsync(superAdminUser, "MolySite@2024!");
                if (result.Succeeded)
                {
                    // 为管理员用户分配角色
                    await userManager.AddToRoleAsync(superAdminUser, "SuperAdmin");


                }
            }

            // 创建演示SiteOwner用户
            if (await userManager.FindByNameAsync("demo") == null)
            {
                var demoUser = new ApplicationUser
                {
                    UserName = "demo",
                    Email = "<EMAIL>",
                    IsActive = true,
                    PlatformRole = PlatformRole.User,
                    PlatformRoles = new List<string> { "User" },
                    Permissions = SitePermissions.GetUserPermissions(),
                    CreatedAt = DateTime.UtcNow,
                    DisplayName = "演示用户",
                    TimeZone = "UTC",
                    Language = "zh-CN",
                    EmailNotifications = true
                };

                var result = await userManager.CreateAsync(demoUser, "MolySite@2024!");
                if (result.Succeeded)
                {
                    // 为用户分配角色
                    await userManager.AddToRoleAsync(demoUser, "User");


                }
            }
        }

        /// <summary>
        /// 种子示例网站数据
        /// </summary>
        private static async Task SeedSitesAsync(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager)
        {
            // 检查是否已有网站数据
            if (!await context.Sites.AnyAsync())
            {
                // 获取演示用户
                var demoUser = await userManager.FindByNameAsync("demo");
                if (demoUser != null)
                {
                    // 创建演示网站
                    var demoSite = new Site
                    {
                        Id = Guid.NewGuid(),
                        Name = "演示网站",
                        Domain = "demo.molysite.com",
                        Description = "这是一个演示网站，展示MolySite平台的功能",
                        OwnerId = demoUser.Id,
                        Template = "Default",
                        PrimaryColor = "#3b82f6",
                        SecondaryColor = "#10b981",
                        IsPublished = true,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        SubscriptionPlan = "Free",
                        MaxStorageMB = 100,
                        UsedStorageMB = 0,
                        FooterText = "© 2024 演示网站. 由 MolySite 提供技术支持。",
                        EnableComments = true,
                        EnableSEO = true,
                        SiteKeywords = "演示,MolySite,建站平台"
                    };

                    context.Sites.Add(demoSite);

                    // 为演示用户创建网站所有者角色
                    var siteOwnerRole = new SiteUserRole
                    {
                        Id = Guid.NewGuid(),
                        UserId = demoUser.Id,
                        SiteId = demoSite.Id,
                        Role = SiteRoleType.Owner,
                        GrantedAt = DateTime.UtcNow,
                        GrantedBy = demoUser.Id,
                        IsActive = true,

                    };

                    context.SiteUserRoles.Add(siteOwnerRole);
                    await context.SaveChangesAsync();
                }
            }
        }
    }
}