using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using MolySite.Identity.Authorization;
using MolySite.Identity.Models;
using MolySite.Shared.Dtos;

namespace MolySite.Identity.Data
{
    /// <summary>
    /// 数据种子类，用于初始化租户、角色和权限
    /// </summary>
    public static class DataSeeder
    {
        /// <summary>
        /// 种子数据初始化方法
        /// </summary>
        public static async Task SeedDataAsync(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole<Guid>> roleManager)
        {
            // 确保数据库已创建
            await context.Database.MigrateAsync();

            // 种子订阅计划数据
            await SeedSubscriptionPlansAsync(context);

            // 种子角色数据
            await SeedRolesAsync(roleManager);

            // 种子管理员用户
            await SeedAdminUserAsync(context, userManager);

            // 种子网站配置数据
            await SeedWebsiteConfigsAsync(context);

            // 种子示例网站
            await SeedSitesAsync(context, userManager);
        }

        /// <summary>
        /// 完全重置数据库并重新种子数据（用于开发环境）
        /// </summary>
        public static async Task ResetAndSeedDataAsync(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole<Guid>> roleManager)
        {
            // 警告：这将删除所有数据！
            await context.Database.EnsureDeletedAsync();
            await context.Database.MigrateAsync();

            // 重新种子所有数据
            await SeedDataAsync(context, userManager, roleManager);
        }

        /// <summary>
        /// 种子订阅计划数据
        /// </summary>
        private static async Task SeedSubscriptionPlansAsync(ApplicationDbContext context)
        {
            if (!await context.SubscriptionPlans.AnyAsync())
            {
                var plans = new List<SubscriptionPlan>
                {
                    new SubscriptionPlan
                    {
                        Id = Guid.NewGuid(),
                        Name = "Free",
                        Description = "适用于个人或小型项目的免费计划",
                        MonthlyPrice = 0,
                        YearlyPrice = 0,
                        MaxUsers = 5,
                        MaxStorageMB = 100,
                        SupportsCustomDomain = false,
                        SupportsAdvancedThemes = false,
                        SupportsApiAccess = false,
                        SupportsPrioritySupport = false,
                        IsDefault = true,
                        IsActive = true
                    },
                    new SubscriptionPlan
                    {
                        Id = Guid.NewGuid(),
                        Name = "Basic",
                        Description = "适用于小型团队的基础计划",
                        MonthlyPrice = 9.99m,
                        YearlyPrice = 99.99m,
                        MaxUsers = 10,
                        MaxStorageMB = 500,
                        SupportsCustomDomain = true,
                        SupportsAdvancedThemes = false,
                        SupportsApiAccess = false,
                        SupportsPrioritySupport = false,
                        IsDefault = false,
                        IsActive = true
                    },
                    new SubscriptionPlan
                    {
                        Id = Guid.NewGuid(),
                        Name = "Premium",
                        Description = "适用于中型团队的高级计划",
                        MonthlyPrice = 29.99m,
                        YearlyPrice = 299.99m,
                        MaxUsers = 50,
                        MaxStorageMB = 2048,
                        SupportsCustomDomain = true,
                        SupportsAdvancedThemes = true,
                        SupportsApiAccess = true,
                        SupportsPrioritySupport = false,
                        IsDefault = false,
                        IsActive = true
                    },
                    new SubscriptionPlan
                    {
                        Id = Guid.NewGuid(),
                        Name = "Enterprise",
                        Description = "适用于大型企业的企业级计划",
                        MonthlyPrice = 99.99m,
                        YearlyPrice = 999.99m,
                        MaxUsers = 200,
                        MaxStorageMB = 10240,
                        SupportsCustomDomain = true,
                        SupportsAdvancedThemes = true,
                        SupportsApiAccess = true,
                        SupportsPrioritySupport = true,
                        IsDefault = false,
                        IsActive = true
                    }
                };

                context.SubscriptionPlans.AddRange(plans);
                await context.SaveChangesAsync();
            }
        }



        /// <summary>
        /// 种子网站配置数据
        /// </summary>
        private static Task SeedWebsiteConfigsAsync(ApplicationDbContext context)
        {
            // 网站配置现在与Site关联，不再需要预先种子数据
            // 配置将在创建站点时自动创建
            return Task.CompletedTask;
        }

        /// <summary>
        /// 清理并重新种子角色数据（简化架构：只保留必要角色）
        /// </summary>
        private static async Task SeedRolesAsync(RoleManager<IdentityRole<Guid>> roleManager)
        {
            // 清理旧角色
            await CleanupOldRolesAsync(roleManager);

            // 只保留必要的角色
            var roles = new[]
            {
                "SuperAdmin",  // 平台超级管理员
                "User",        // 普通用户（注册默认角色）
                "Owner"        // 网站所有者（网站级角色，用于兼容性）
            };

            foreach (var roleName in roles)
            {
                if (!await roleManager.RoleExistsAsync(roleName))
                {
                    var role = new IdentityRole<Guid>
                    {
                        Name = roleName,
                        NormalizedName = roleName.ToUpper()
                    };
                    await roleManager.CreateAsync(role);
                }
            }
        }

        /// <summary>
        /// 清理旧的不需要的角色
        /// </summary>
        private static async Task CleanupOldRolesAsync(RoleManager<IdentityRole<Guid>> roleManager)
        {
            var rolesToDelete = new[]
            {
                "SiteOwner",   // 已合并到User
                "SiteEditor",  // 已删除
                "Editor",      // 已删除
                "Follower",    // 已删除
                "Viewer"       // 已删除
            };

            foreach (var roleName in rolesToDelete)
            {
                var role = await roleManager.FindByNameAsync(roleName);
                if (role != null)
                {
                    await roleManager.DeleteAsync(role);
                }
            }
        }

        /// <summary>
        /// 清理并重新种子用户数据（简化架构：只保留必要账号）
        /// </summary>
        private static async Task SeedAdminUserAsync(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager)
        {
            // 清理旧用户数据
            await CleanupOldUsersAsync(context, userManager);

            // 创建SuperAdmin用户
            if (await userManager.FindByNameAsync("superadmin") == null)
            {
                var superAdminUser = new ApplicationUser
                {
                    UserName = "superadmin",
                    Email = "<EMAIL>",
                    IsActive = true,
                    PlatformRole = PlatformRole.SuperAdmin,
                    PlatformRoles = new List<string> { "SuperAdmin" },
                    Permissions = SitePermissions.GetSuperAdminPermissions(),
                    CreatedAt = DateTime.UtcNow,
                    DisplayName = "超级管理员",
                    TimeZone = "Asia/Shanghai",
                    Language = "zh-CN",
                    EmailNotifications = true,
                    EmailConfirmed = true
                };

                var result = await userManager.CreateAsync(superAdminUser, "SuperAdmin@2024!");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(superAdminUser, "SuperAdmin");
                }
            }

            // 创建普通User用户
            if (await userManager.FindByNameAsync("user") == null)
            {
                var normalUser = new ApplicationUser
                {
                    UserName = "user",
                    Email = "<EMAIL>",
                    IsActive = true,
                    PlatformRole = PlatformRole.User,
                    PlatformRoles = new List<string> { "User" },
                    Permissions = SitePermissions.GetUserPermissions(),
                    CreatedAt = DateTime.UtcNow,
                    DisplayName = "普通用户",
                    TimeZone = "Asia/Shanghai",
                    Language = "zh-CN",
                    EmailNotifications = true,
                    EmailConfirmed = true
                };

                var result = await userManager.CreateAsync(normalUser, "User@2024!");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(normalUser, "User");
                }
            }
        }

        /// <summary>
        /// 清理旧的用户数据
        /// </summary>
        private static async Task CleanupOldUsersAsync(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager)
        {
            // 获取所有非必要的用户（除了superadmin和user）
            var usersToDelete = await context.Users
                .Where(u => u.UserName != "superadmin" && u.UserName != "user")
                .ToListAsync();

            foreach (var user in usersToDelete)
            {
                // 删除用户的网站角色关系
                var userSiteRoles = await context.SiteUserRoles
                    .Where(sur => sur.UserId == user.Id)
                    .ToListAsync();
                context.SiteUserRoles.RemoveRange(userSiteRoles);

                // 删除用户拥有的网站
                var userSites = await context.Sites
                    .Where(s => s.OwnerId == user.Id)
                    .ToListAsync();
                context.Sites.RemoveRange(userSites);

                // 删除用户
                await userManager.DeleteAsync(user);
            }

            await context.SaveChangesAsync();
        }

        /// <summary>
        /// 清理并重新种子网站数据
        /// </summary>
        private static async Task SeedSitesAsync(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager)
        {
            // 清理旧网站数据
            await CleanupOldSitesAsync(context);

            // 获取普通用户
            var normalUser = await userManager.FindByNameAsync("user");
            if (normalUser != null)
            {
                // 为普通用户创建演示网站
                var demoSite = new Site
                {
                    Id = Guid.NewGuid(),
                    Name = "我的第一个网站",
                    Domain = "mysite.molysite.com",
                    Description = "这是使用MolySite平台创建的第一个网站",
                    OwnerId = normalUser.Id,
                    Template = "Default",
                    PrimaryColor = "#3b82f6",
                    SecondaryColor = "#10b981",
                    IsPublished = true,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    LastModifiedAt = DateTime.UtcNow,
                    SubscriptionPlan = "Free",
                    MaxStorageMB = 100,
                    UsedStorageMB = 5,
                    FooterText = "© 2024 我的第一个网站. 由 MolySite 提供技术支持。",
                    EnableComments = true,
                    EnableSEO = true,
                    SiteKeywords = "个人网站,MolySite,建站平台"
                };

                context.Sites.Add(demoSite);

                // 为用户创建网站所有者角色
                var siteOwnerRole = new SiteUserRole
                {
                    Id = Guid.NewGuid(),
                    UserId = normalUser.Id,
                    SiteId = demoSite.Id,
                    Role = SiteRoleType.Owner,
                    GrantedAt = DateTime.UtcNow,
                    GrantedBy = normalUser.Id,
                    IsActive = true
                };

                context.SiteUserRoles.Add(siteOwnerRole);
                await context.SaveChangesAsync();
            }
        }

        /// <summary>
        /// 清理旧的网站数据
        /// </summary>
        private static async Task CleanupOldSitesAsync(ApplicationDbContext context)
        {
            // 删除所有现有的网站用户角色关系
            var allSiteUserRoles = await context.SiteUserRoles.ToListAsync();
            context.SiteUserRoles.RemoveRange(allSiteUserRoles);

            // 删除所有现有的网站
            var allSites = await context.Sites.ToListAsync();
            context.Sites.RemoveRange(allSites);

            await context.SaveChangesAsync();
        }
    }
}