/* MainLayout 现代化样式 - 专门用于开发测试页面 */

/* 全局样式 */
.main-layout[b-7mhs7g97z7] {
    min-height: 100vh;
    display: flex;
    background: #f8fafc;
}

/* 侧边栏样式 */
.sidebar[b-7mhs7g97z7] {
    width: 256px;
    background: white;
    border-right: 1px solid #e2e8f0;
    transition: transform 0.3s ease;
}

.sidebar-mobile[b-7mhs7g97z7] {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 50;
    transform: translateX(-100%);
}

.sidebar-mobile.open[b-7mhs7g97z7] {
    transform: translateX(0);
}

/* 主内容区域 */
.main-content[b-7mhs7g97z7] {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.top-navbar[b-7mhs7g97z7] {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 30;
}

/* 品牌Logo */
.brand-logo[b-7mhs7g97z7] {
    transition: transform 0.3s ease;
}

.brand-logo:hover[b-7mhs7g97z7] {
    transform: scale(1.05);
}

/* 按钮样式 */
.btn-icon[b-7mhs7g97z7] {
    @apply inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 transition-colors duration-200;
}

.btn-primary[b-7mhs7g97z7] {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200;
}

.btn-secondary[b-7mhs7g97z7] {
    @apply inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200;
}

/* 按钮样式增强 */
.btn-primary[b-7mhs7g97z7] {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border: none;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
    transition: all 0.3s ease;
}

.btn-primary:hover[b-7mhs7g97z7] {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-secondary[b-7mhs7g97z7] {
    background: white;
    border: 1px solid #d1d5db;
    color: #374151;
    transition: all 0.3s ease;
}

.btn-secondary:hover[b-7mhs7g97z7] {
    background: #f9fafb;
    border-color: #9ca3af;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 移动端菜单动画 */
.mobile-menu[b-7mhs7g97z7] {
    transition: all 0.3s ease;
    transform-origin: top;
}

.mobile-menu.show[b-7mhs7g97z7] {
    animation: slideDown-b-7mhs7g97z7 0.3s ease-out;
}

.mobile-menu.hide[b-7mhs7g97z7] {
    animation: slideUp-b-7mhs7g97z7 0.3s ease-in;
}

@keyframes slideDown-b-7mhs7g97z7 {
    from {
        opacity: 0;
        transform: scaleY(0);
    }
    to {
        opacity: 1;
        transform: scaleY(1);
    }
}

@keyframes slideUp-b-7mhs7g97z7 {
    from {
        opacity: 1;
        transform: scaleY(1);
    }
    to {
        opacity: 0;
        transform: scaleY(0);
    }
}

/* 主内容区域 */
main[b-7mhs7g97z7] {
    flex: 1;
    min-height: calc(100vh - 64px - 120px); /* 减去导航栏和页脚高度 */
}

/* 页脚样式 */
footer[b-7mhs7g97z7] {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border-top: 1px solid #e2e8f0;
}

/* 错误提示样式 */
#blazor-error-ui[b-7mhs7g97z7] {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    animation: slideInUp-b-7mhs7g97z7 0.3s ease-out;
}

@keyframes slideInUp-b-7mhs7g97z7 {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .navbar[b-7mhs7g97z7] {
        padding: 0.5rem 1rem;
    }
    
    .brand-logo[b-7mhs7g97z7] {
        font-size: 1.25rem;
    }
    
    .mobile-menu[b-7mhs7g97z7] {
        margin-top: 0.5rem;
        padding-top: 0.5rem;
        border-top: 1px solid #e5e7eb;
    }
}

/* 平滑滚动 */
html[b-7mhs7g97z7] {
    scroll-behavior: smooth;
}

/* 焦点样式 */
button:focus[b-7mhs7g97z7],
a:focus[b-7mhs7g97z7] {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* 加载状态 */
.loading[b-7mhs7g97z7] {
    opacity: 0.6;
    pointer-events: none;
}

/* 工具提示 */
[data-tooltip][b-7mhs7g97z7] {
    position: relative;
}

[data-tooltip][b-7mhs7g97z7]::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #1f2937;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s;
    z-index: 1000;
}

[data-tooltip]:hover[b-7mhs7g97z7]::before {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-4px);
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .navbar[b-7mhs7g97z7] {
        background-color: rgba(17, 24, 39, 0.95);
        border-bottom-color: #374151;
    }
    
    .nav-link[b-7mhs7g97z7] {
        color: #d1d5db;
    }
    
    .nav-link:hover[b-7mhs7g97z7] {
        color: #f9fafb;
    }
    
    footer[b-7mhs7g97z7] {
        background: linear-gradient(135deg, #1f2937, #111827);
        border-top-color: #374151;
    }
}

/* 打印样式 */
@media print {
    .navbar[b-7mhs7g97z7],
    footer[b-7mhs7g97z7],
    #blazor-error-ui[b-7mhs7g97z7] {
        display: none !important;
    }
    
    main[b-7mhs7g97z7] {
        min-height: auto;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .btn-primary[b-7mhs7g97z7] {
        background: #000;
        color: #fff;
        border: 2px solid #000;
    }
    
    .btn-secondary[b-7mhs7g97z7] {
        background: #fff;
        color: #000;
        border: 2px solid #000;
    }
    
    .nav-link[b-7mhs7g97z7] {
        color: #000;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    *[b-7mhs7g97z7],
    *[b-7mhs7g97z7]::before,
    *[b-7mhs7g97z7]::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
