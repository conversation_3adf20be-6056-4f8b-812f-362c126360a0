using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MolySite.Core.Interfaces;
using MolySite.Identity.Data;
using MolySite.Identity.Models;

namespace MolySite.Core.Validation
{
    /// <summary>
    /// 简化角色体系验证脚本
    /// </summary>
    public class SimplifiedRoleSystemValidation
    {
        private readonly ApplicationDbContext _context;
        private readonly IPermissionService _permissionService;
        private readonly ILogger<SimplifiedRoleSystemValidation> _logger;

        public SimplifiedRoleSystemValidation(
            ApplicationDbContext context,
            IPermissionService permissionService,
            ILogger<SimplifiedRoleSystemValidation> logger)
        {
            _context = context;
            _permissionService = permissionService;
            _logger = logger;
        }

        /// <summary>
        /// 执行完整验证
        /// </summary>
        public async Task<ValidationResult> ValidateAsync()
        {
            var result = new ValidationResult();

            try
            {
                _logger.LogInformation("开始验证简化角色体系...");

                // 1. 验证数据模型
                await ValidateDataModelAsync(result);

                // 2. 验证角色体系
                await ValidateRoleSystemAsync(result);

                // 3. 验证权限系统
                await ValidatePermissionSystemAsync(result);

                // 4. 验证数据完整性
                await ValidateDataIntegrityAsync(result);

                // 5. 验证业务逻辑
                await ValidateBusinessLogicAsync(result);

                _logger.LogInformation("验证完成，成功: {Success}, 警告: {Warnings}, 错误: {Errors}",
                    result.SuccessCount, result.WarningCount, result.ErrorCount);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证过程中发生错误");
                result.AddError("验证过程异常", ex.Message);
                return result;
            }
        }

        /// <summary>
        /// 验证数据模型
        /// </summary>
        private async Task ValidateDataModelAsync(ValidationResult result)
        {
            _logger.LogInformation("验证数据模型...");

            // 检查 SiteRoleType 枚举只包含 Owner
            var roleTypes = Enum.GetValues<SiteRoleType>();
            if (roleTypes.Length == 1 && roleTypes[0] == SiteRoleType.Owner)
            {
                result.AddSuccess("SiteRoleType 枚举", "只包含 Owner 角色");
            }
            else
            {
                result.AddError("SiteRoleType 枚举", $"包含了 {roleTypes.Length} 个角色，应该只有 Owner");
            }

            // 检查数据库中是否还有非 Owner 角色
            var nonOwnerRoles = await _context.SiteUserRoles
                .Where(sur => sur.Role != SiteRoleType.Owner)
                .CountAsync();

            if (nonOwnerRoles == 0)
            {
                result.AddSuccess("数据库角色", "没有非 Owner 角色记录");
            }
            else
            {
                result.AddError("数据库角色", $"发现 {nonOwnerRoles} 条非 Owner 角色记录");
            }
        }

        /// <summary>
        /// 验证角色体系
        /// </summary>
        private async Task ValidateRoleSystemAsync(ValidationResult result)
        {
            _logger.LogInformation("验证角色体系...");

            // 检查平台角色
            var superAdminCount = await _context.Users.CountAsync(u => u.PlatformRole == PlatformRole.SuperAdmin);
            var userCount = await _context.Users.CountAsync(u => u.PlatformRole == PlatformRole.User);
            var totalUsers = await _context.Users.CountAsync();

            result.AddSuccess("平台角色统计", $"SuperAdmin: {superAdminCount}, User: {userCount}, 总计: {totalUsers}");

            // 检查是否所有网站都有 Owner
            var sitesWithoutOwner = await _context.Sites
                .Where(s => !_context.SiteUserRoles.Any(sur => 
                    sur.SiteId == s.Id && 
                    sur.Role == SiteRoleType.Owner && 
                    sur.IsActive))
                .CountAsync();

            if (sitesWithoutOwner == 0)
            {
                result.AddSuccess("网站 Owner", "所有网站都有 Owner");
            }
            else
            {
                result.AddError("网站 Owner", $"{sitesWithoutOwner} 个网站没有 Owner");
            }
        }

        /// <summary>
        /// 验证权限系统
        /// </summary>
        private async Task ValidatePermissionSystemAsync(ValidationResult result)
        {
            _logger.LogInformation("验证权限系统...");

            // 创建测试用户和网站
            var testUser = await _context.Users.FirstOrDefaultAsync(u => u.PlatformRole == PlatformRole.User);
            var testSite = await _context.Sites.FirstOrDefaultAsync();

            if (testUser != null && testSite != null)
            {
                // 测试网站权限
                var hasViewPermission = await _permissionService.HasSitePermissionAsync(
                    testUser.Id, testSite.Id, "Site.ViewSettings");

                if (hasViewPermission.IsSuccess)
                {
                    result.AddSuccess("权限检查", "网站权限检查正常");
                }
                else
                {
                    result.AddWarning("权限检查", "网站权限检查失败");
                }

                // 测试角色获取
                var userRole = await _permissionService.GetUserSiteRoleAsync(testUser.Id, testSite.Id);
                if (userRole.IsSuccess)
                {
                    result.AddSuccess("角色获取", "用户角色获取正常");
                }
                else
                {
                    result.AddWarning("角色获取", "用户角色获取失败");
                }
            }
            else
            {
                result.AddWarning("权限测试", "没有找到测试用户或网站");
            }
        }

        /// <summary>
        /// 验证数据完整性
        /// </summary>
        private async Task ValidateDataIntegrityAsync(ValidationResult result)
        {
            _logger.LogInformation("验证数据完整性...");

            // 检查孤立的角色记录
            var orphanedRoles = await _context.SiteUserRoles
                .Where(sur => !_context.Users.Any(u => u.Id == sur.UserId) ||
                             !_context.Sites.Any(s => s.Id == sur.SiteId))
                .CountAsync();

            if (orphanedRoles == 0)
            {
                result.AddSuccess("数据完整性", "没有孤立的角色记录");
            }
            else
            {
                result.AddError("数据完整性", $"发现 {orphanedRoles} 条孤立的角色记录");
            }

            // 检查重复的 Owner 角色
            var duplicateOwners = await _context.SiteUserRoles
                .Where(sur => sur.Role == SiteRoleType.Owner && sur.IsActive)
                .GroupBy(sur => sur.SiteId)
                .Where(g => g.Count() > 1)
                .CountAsync();

            if (duplicateOwners == 0)
            {
                result.AddSuccess("Owner 唯一性", "每个网站只有一个 Owner");
            }
            else
            {
                result.AddWarning("Owner 唯一性", $"{duplicateOwners} 个网站有多个 Owner");
            }
        }

        /// <summary>
        /// 验证业务逻辑
        /// </summary>
        private async Task ValidateBusinessLogicAsync(ValidationResult result)
        {
            _logger.LogInformation("验证业务逻辑...");

            // 检查网站创建者是否都是 Owner
            var sitesWithoutCreatorAsOwner = await _context.Sites
                .Where(s => !_context.SiteUserRoles.Any(sur => 
                    sur.SiteId == s.Id && 
                    sur.UserId == s.CreatedBy && 
                    sur.Role == SiteRoleType.Owner && 
                    sur.IsActive))
                .CountAsync();

            if (sitesWithoutCreatorAsOwner == 0)
            {
                result.AddSuccess("创建者权限", "所有网站创建者都是 Owner");
            }
            else
            {
                result.AddWarning("创建者权限", $"{sitesWithoutCreatorAsOwner} 个网站的创建者不是 Owner");
            }

            // 统计信息
            var totalSites = await _context.Sites.CountAsync();
            var activeSites = await _context.Sites.CountAsync(s => s.IsActive);
            var publishedSites = await _context.Sites.CountAsync(s => s.IsPublished);

            result.AddSuccess("网站统计", $"总计: {totalSites}, 活跃: {activeSites}, 已发布: {publishedSites}");
        }
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        public List<ValidationItem> Items { get; } = new();
        public int SuccessCount => Items.Count(i => i.Type == ValidationType.Success);
        public int WarningCount => Items.Count(i => i.Type == ValidationType.Warning);
        public int ErrorCount => Items.Count(i => i.Type == ValidationType.Error);
        public bool IsValid => ErrorCount == 0;

        public void AddSuccess(string category, string message)
        {
            Items.Add(new ValidationItem(ValidationType.Success, category, message));
        }

        public void AddWarning(string category, string message)
        {
            Items.Add(new ValidationItem(ValidationType.Warning, category, message));
        }

        public void AddError(string category, string message)
        {
            Items.Add(new ValidationItem(ValidationType.Error, category, message));
        }
    }

    public record ValidationItem(ValidationType Type, string Category, string Message);

    public enum ValidationType
    {
        Success,
        Warning,
        Error
    }
}
