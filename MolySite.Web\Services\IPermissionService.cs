using System.Security.Claims;

namespace MolySite.Web.Services
{
    /// <summary>
    /// 前端权限检查服务接口
    /// </summary>
    public interface IPermissionService
    {
        /// <summary>
        /// 检查用户是否有指定的平台权限
        /// </summary>
        /// <param name="user">用户声明主体</param>
        /// <param name="permission">权限名称</param>
        /// <returns>是否有权限</returns>
        bool HasPlatformPermission(ClaimsPrincipal user, string permission);

        /// <summary>
        /// 检查用户是否有指定站点的权限
        /// </summary>
        /// <param name="user">用户声明主体</param>
        /// <param name="siteId">站点ID</param>
        /// <param name="permission">权限名称</param>
        /// <returns>是否有权限</returns>
        Task<bool> HasSitePermissionAsync(ClaimsPrincipal user, Guid siteId, string permission);

        /// <summary>
        /// 获取用户角色
        /// </summary>
        /// <param name="user">用户声明主体</param>
        /// <returns>用户角色</returns>
        string GetUserRole(ClaimsPrincipal user);

        /// <summary>
        /// 检查用户是否为SuperAdmin
        /// </summary>
        /// <param name="user">用户声明主体</param>
        /// <returns>是否为SuperAdmin</returns>
        bool IsSuperAdmin(ClaimsPrincipal user);

        /// <summary>
        /// 检查用户是否为SiteOwner
        /// </summary>
        /// <param name="user">用户声明主体</param>
        /// <returns>是否为SiteOwner</returns>
        bool IsSiteOwner(ClaimsPrincipal user);

        /// <summary>
        /// 检查用户是否为普通用户
        /// </summary>
        /// <param name="user">用户声明主体</param>
        /// <returns>是否为普通用户</returns>
        bool IsUser(ClaimsPrincipal user);

        /// <summary>
        /// 检查用户是否为SiteEditor
        /// </summary>
        /// <param name="user">用户声明主体</param>
        /// <returns>是否为SiteEditor</returns>
        bool IsSiteEditor(ClaimsPrincipal user);

        /// <summary>
        /// 检查用户是否为SiteFollower
        /// </summary>
        /// <param name="user">用户声明主体</param>
        /// <returns>是否为SiteFollower</returns>
        bool IsSiteFollower(ClaimsPrincipal user);

        /// <summary>
        /// 获取用户可访问的站点列表
        /// </summary>
        /// <param name="user">用户声明主体</param>
        /// <returns>站点ID列表</returns>
        Task<List<Guid>> GetUserAccessibleSitesAsync(ClaimsPrincipal user);
    }

    /// <summary>
    /// 平台权限常量
    /// </summary>
    public static class PlatformPermissions
    {
        public const string FullAccess = "Platform.FullAccess";
        public const string ViewSettings = "Platform.ViewSettings";
        public const string ManageSettings = "Platform.ManageSettings";
        public const string ViewAllUsers = "Platform.ViewAllUsers";
        public const string ManageAllUsers = "Platform.ManageAllUsers";
        public const string ViewAllSites = "Platform.ViewAllSites";
        public const string ManageAllSites = "Platform.ManageAllSites";
        public const string ViewStatistics = "Platform.ViewStatistics";
        public const string ManageSubscriptions = "Platform.ManageSubscriptions";
    }

    /// <summary>
    /// 站点权限常量
    /// </summary>
    public static class SitePermissions
    {
        public const string ViewSettings = "Site.ViewSettings";
        public const string EditSettings = "Site.EditSettings";
        public const string ManageUsers = "Site.ManageUsers";
        public const string ViewContent = "Site.ViewContent";
        public const string EditContent = "Site.EditContent";
        public const string Publish = "Site.Publish";
        public const string ViewStatistics = "Site.ViewStatistics";
        public const string ManageMedia = "Site.ManageMedia";
        public const string Follow = "Site.Follow";
        public const string ReceiveNotifications = "Site.ReceiveNotifications";
        public const string Contact = "Site.Contact";
        public const string ViewFollowerContent = "Site.ViewFollowerContent";
    }

    /// <summary>
    /// 用户角色常量
    /// </summary>
    public static class UserRoles
    {
        public const string SuperAdmin = "SuperAdmin";
        public const string User = "User";
        public const string SiteOwner = "SiteOwner"; // 向后兼容，实际上是User
    }
}
