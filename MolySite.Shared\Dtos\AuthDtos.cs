using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace MolySite.Shared.Dtos
{
    /// <summary>
    /// 用户注册数据传输对象
    /// </summary>
    public class RegisterDto
    {
        /// <summary>
        /// 用户名
        /// </summary>
        [Required(ErrorMessage = "用户名是必填项")]
        [StringLength(50, MinimumLength = 3, ErrorMessage = "用户名长度必须在3-50个字符之间")]
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 电子邮件
        /// </summary>
        [Required(ErrorMessage = "电子邮件是必填项")]
        [EmailAddress(ErrorMessage = "请输入有效的电子邮件地址")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 密码
        /// </summary>
        [Required(ErrorMessage = "密码是必填项")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// 确认密码
        /// </summary>
        [Required(ErrorMessage = "确认密码是必填项")]
        [Compare("Password", ErrorMessage = "密码和确认密码不匹配")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// 用户登录数据传输对象
    /// </summary>
    public class LoginDto
    {
        /// <summary>
        /// 用户名或电子邮件
        /// </summary>
        [Required(ErrorMessage = "用户名或电子邮件是必填项")]
        public string UserNameOrEmail { get; set; } = string.Empty;

        /// <summary>
        /// 密码
        /// </summary>
        [Required(ErrorMessage = "密码是必填项")]
        public string Password { get; set; } = string.Empty;
    }

    /// <summary>
    /// 登录响应数据传输对象
    /// </summary>
    public class LoginResponseDto
    {
        /// <summary>
        /// JWT令牌
        /// </summary>
        public string Token { get; set; } = string.Empty;

        /// <summary>
        /// 刷新令牌
        /// </summary>
        public string RefreshToken { get; set; } = string.Empty;

        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 电子邮件
        /// </summary>
        public string Email { get; set; } = string.Empty;
        
        /// <summary>
        /// 用户角色列表
        /// </summary>
        public List<string> Roles { get; set; } = new List<string>();

        /// <summary>
        /// 用户权限列表
        /// </summary>
        public List<string> Permissions { get; set; } = new List<string>();
    }

    /// <summary>
    /// 更新用户信息DTO
    /// </summary>
    public class UpdateUserDto
    {
        /// <summary>
        /// 显示名称
        /// </summary>
        [StringLength(100, ErrorMessage = "显示名称不能超过100个字符")]
        public string? DisplayName { get; set; }

        /// <summary>
        /// 用户简介
        /// </summary>
        [StringLength(500, ErrorMessage = "用户简介不能超过500个字符")]
        public string? Bio { get; set; }

        /// <summary>
        /// 头像URL
        /// </summary>
        [StringLength(500, ErrorMessage = "头像URL不能超过500个字符")]
        public string? AvatarUrl { get; set; }

        /// <summary>
        /// 时区
        /// </summary>
        [StringLength(50, ErrorMessage = "时区不能超过50个字符")]
        public string? TimeZone { get; set; }

        /// <summary>
        /// 语言偏好
        /// </summary>
        [StringLength(10, ErrorMessage = "语言偏好不能超过10个字符")]
        public string? Language { get; set; }

        /// <summary>
        /// 是否接收邮件通知
        /// </summary>
        public bool? EmailNotifications { get; set; }
    }

    /// <summary>
    /// 用户信息DTO
    /// </summary>
    public class UserDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 电子邮件
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        public string? DisplayName { get; set; }

        /// <summary>
        /// 用户简介
        /// </summary>
        public string? Bio { get; set; }

        /// <summary>
        /// 头像URL
        /// </summary>
        public string? AvatarUrl { get; set; }

        /// <summary>
        /// 时区
        /// </summary>
        public string TimeZone { get; set; } = "UTC";

        /// <summary>
        /// 语言偏好
        /// </summary>
        public string Language { get; set; } = "zh-CN";

        /// <summary>
        /// 是否接收邮件通知
        /// </summary>
        public bool EmailNotifications { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime? LastLoginAt { get; set; }

        /// <summary>
        /// 平台角色
        /// </summary>
        public List<string> PlatformRoles { get; set; } = new List<string>();

        /// <summary>
        /// 平台权限
        /// </summary>
        public List<string> Permissions { get; set; } = new List<string>();
    }

    /// <summary>
    /// 刷新令牌请求DTO
    /// </summary>
    public class RefreshTokenDto
    {
        /// <summary>
        /// 刷新令牌
        /// </summary>
        [Required(ErrorMessage = "刷新令牌是必填项")]
        public string RefreshToken { get; set; } = string.Empty;
    }

    /// <summary>
    /// 更改密码DTO
    /// </summary>
    public class ChangePasswordDto
    {
        /// <summary>
        /// 当前密码
        /// </summary>
        [Required(ErrorMessage = "当前密码是必填项")]
        public string CurrentPassword { get; set; } = string.Empty;

        /// <summary>
        /// 新密码
        /// </summary>
        [Required(ErrorMessage = "新密码是必填项")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
        public string NewPassword { get; set; } = string.Empty;

        /// <summary>
        /// 确认新密码
        /// </summary>
        [Required(ErrorMessage = "确认新密码是必填项")]
        [Compare("NewPassword", ErrorMessage = "新密码和确认密码不匹配")]
        public string ConfirmNewPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// 重置密码DTO
    /// </summary>
    public class ResetPasswordDto
    {
        /// <summary>
        /// 电子邮件
        /// </summary>
        [Required(ErrorMessage = "电子邮件是必填项")]
        [EmailAddress(ErrorMessage = "请输入有效的电子邮件地址")]
        public string Email { get; set; } = string.Empty;
    }

    /// <summary>
    /// 确认重置密码DTO
    /// </summary>
    public class ConfirmResetPasswordDto
    {
        /// <summary>
        /// 电子邮件
        /// </summary>
        [Required(ErrorMessage = "电子邮件是必填项")]
        [EmailAddress(ErrorMessage = "请输入有效的电子邮件地址")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 重置令牌
        /// </summary>
        [Required(ErrorMessage = "重置令牌是必填项")]
        public string Token { get; set; } = string.Empty;

        /// <summary>
        /// 新密码
        /// </summary>
        [Required(ErrorMessage = "新密码是必填项")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
        public string NewPassword { get; set; } = string.Empty;

        /// <summary>
        /// 确认新密码
        /// </summary>
        [Required(ErrorMessage = "确认新密码是必填项")]
        [Compare("NewPassword", ErrorMessage = "新密码和确认密码不匹配")]
        public string ConfirmNewPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// 网站用户角色DTO
    /// </summary>
    public class SiteUserRoleDto
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 用户邮箱
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 网站ID
        /// </summary>
        public Guid SiteId { get; set; }

        /// <summary>
        /// 角色类型
        /// </summary>
        public SiteRoleType Role { get; set; }

        /// <summary>
        /// 授权时间
        /// </summary>
        public DateTime GrantedAt { get; set; }

        /// <summary>
        /// 授权者ID
        /// </summary>
        public Guid GrantedBy { get; set; }

        /// <summary>
        /// 是否活跃
        /// </summary>
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// 用户统计DTO
    /// </summary>
    public class UserStatisticsDto
    {
        /// <summary>
        /// 总用户数
        /// </summary>
        public int TotalUsers { get; set; }

        /// <summary>
        /// 活跃用户数
        /// </summary>
        public int ActiveUsers { get; set; }

        /// <summary>
        /// 今日新增用户数
        /// </summary>
        public int TodayNewUsers { get; set; }

        /// <summary>
        /// 本月新增用户数
        /// </summary>
        public int MonthlyNewUsers { get; set; }

        /// <summary>
        /// 最近活跃用户数
        /// </summary>
        public int RecentActiveUsers { get; set; }
    }
}
