using MolySite.Shared.Dtos;

namespace MolySite.Web.Services
{
    /// <summary>
    /// 域名管理服务接口
    /// </summary>
    public interface IDomainService
    {
        /// <summary>
        /// 检查域名是否可用
        /// </summary>
        /// <param name="domain">域名（不包含.molysite.com后缀）</param>
        /// <returns>是否可用</returns>
        Task<bool> IsDomainAvailableAsync(string domain);

        /// <summary>
        /// 生成域名建议
        /// </summary>
        /// <param name="siteName">网站名称</param>
        /// <param name="siteType">网站类型</param>
        /// <param name="count">建议数量</param>
        /// <returns>域名建议列表</returns>
        Task<List<DomainSuggestionDto>> GenerateDomainSuggestionsAsync(string siteName, string? siteType = null, int count = 6);

        /// <summary>
        /// 预留域名
        /// </summary>
        /// <param name="domain">域名</param>
        /// <param name="userId">用户ID</param>
        /// <returns>预留是否成功</returns>
        Task<bool> ReserveDomainAsync(string domain, Guid userId);

        /// <summary>
        /// 分配域名给网站
        /// </summary>
        /// <param name="domain">域名</param>
        /// <param name="siteId">网站ID</param>
        /// <param name="userId">用户ID</param>
        /// <returns>分配是否成功</returns>
        Task<bool> AllocateDomainAsync(string domain, Guid siteId, Guid userId);

        /// <summary>
        /// 释放域名
        /// </summary>
        /// <param name="domain">域名</param>
        /// <returns>释放是否成功</returns>
        Task<bool> ReleaseDomainAsync(string domain);

        /// <summary>
        /// 验证域名格式
        /// </summary>
        /// <param name="domain">域名</param>
        /// <returns>验证结果</returns>
        DomainValidationResult ValidateDomain(string domain);

        /// <summary>
        /// 计算域名SEO评分
        /// </summary>
        /// <param name="domain">域名</param>
        /// <returns>SEO评分（0-100）</returns>
        int CalculateSeoScore(string domain);

        /// <summary>
        /// 清理域名字符串
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>清理后的域名</returns>
        string SanitizeDomainName(string input);
    }

    /// <summary>
    /// 域名建议DTO
    /// </summary>
    public class DomainSuggestionDto
    {
        /// <summary>
        /// 域名（不包含后缀）
        /// </summary>
        public string Domain { get; set; } = string.Empty;

        /// <summary>
        /// SEO评分
        /// </summary>
        public int SeoScore { get; set; }

        /// <summary>
        /// 是否推荐
        /// </summary>
        public bool IsRecommended { get; set; }

        /// <summary>
        /// 建议类型
        /// </summary>
        public string SuggestionType { get; set; } = string.Empty;

        /// <summary>
        /// 是否可用
        /// </summary>
        public bool IsAvailable { get; set; } = true;
    }

    /// <summary>
    /// 域名验证结果
    /// </summary>
    public class DomainValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 建议
        /// </summary>
        public string Suggestion { get; set; } = string.Empty;
    }
}
