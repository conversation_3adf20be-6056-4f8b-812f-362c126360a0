using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MolySite.Core.Interfaces;
using MolySite.Core.Models;
using MolySite.Identity.Data;
using MolySite.Identity.Models;
using MolySite.Shared.Dtos;

namespace MolySite.Core.Services
{
    /// <summary>
    /// 网站服务实现
    /// </summary>
    public class SiteService : ISiteService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<SiteService> _logger;

        public SiteService(
            ApplicationDbContext context,
            ILogger<SiteService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<Result<SiteDto>> CreateSiteAsync(CreateSiteDto createSiteDto, Guid ownerId)
        {
            try
            {
                // 检查域名是否可用
                var domainCheckResult = await IsDomainAvailableAsync(createSiteDto.Domain);
                if (!domainCheckResult.IsSuccess || !domainCheckResult.Data)
                {
                    return Result<SiteDto>.Failure($"域名 {createSiteDto.Domain} 已被使用", "DOMAIN_NOT_AVAILABLE");
                }

                var site = new Site
                {
                    Id = Guid.NewGuid(),
                    Name = createSiteDto.Name,
                    Domain = createSiteDto.Domain,
                    Description = createSiteDto.Description,
                    Template = createSiteDto.Template ?? "Default",
                    OwnerId = ownerId,
                    IsActive = true,
                    IsPublished = false,
                    CreatedAt = DateTime.UtcNow,
                    LastModifiedAt = DateTime.UtcNow,
                    SubscriptionPlan = "Free",
                    MaxStorageMB = 100,
                    UsedStorageMB = 0
                };

                _context.Sites.Add(site);

                // 为网站所有者创建角色记录
                var ownerRole = new SiteUserRole
                {
                    Id = Guid.NewGuid(),
                    UserId = ownerId,
                    SiteId = site.Id,
                    Role = SiteRoleType.Owner,
                    GrantedAt = DateTime.UtcNow,
                    GrantedBy = ownerId,
                    IsActive = true
                };

                _context.SiteUserRoles.Add(ownerRole);
                await _context.SaveChangesAsync();

                _logger.LogInformation("网站创建成功: {SiteId}, 所有者: {OwnerId}", site.Id, ownerId);
                
                var siteDto = MapToSiteDto(site);
                return Result<SiteDto>.Success(siteDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建网站时发生错误: {SiteName}, 所有者: {OwnerId}", createSiteDto.Name, ownerId);
                return Result<SiteDto>.Failure("创建网站失败", "CREATE_SITE_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<SiteDto>> GetSiteByIdAsync(Guid siteId)
        {
            try
            {
                var site = await _context.Sites
                    .Include(s => s.Owner)
                    .FirstOrDefaultAsync(s => s.Id == siteId);

                if (site == null)
                {
                    return Result<SiteDto>.Failure("网站不存在", "SITE_NOT_FOUND");
                }

                var siteDto = MapToSiteDto(site);
                return Result<SiteDto>.Success(siteDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取网站信息时发生错误: {SiteId}", siteId);
                return Result<SiteDto>.Failure("获取网站信息失败", "GET_SITE_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<SiteDto>> GetSiteByDomainAsync(string domain)
        {
            try
            {
                var site = await _context.Sites
                    .Include(s => s.Owner)
                    .FirstOrDefaultAsync(s => s.Domain == domain && s.IsActive);

                if (site == null)
                {
                    return Result<SiteDto>.Failure("网站不存在", "SITE_NOT_FOUND");
                }

                var siteDto = MapToSiteDto(site);
                return Result<SiteDto>.Success(siteDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据域名获取网站时发生错误: {Domain}", domain);
                return Result<SiteDto>.Failure("获取网站信息失败", "GET_SITE_BY_DOMAIN_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<SiteDto>> UpdateSiteAsync(Guid siteId, UpdateSiteDto updateSiteDto, Guid userId)
        {
            try
            {
                var site = await _context.Sites.FindAsync(siteId);
                if (site == null || !site.IsActive)
                {
                    return Result<SiteDto>.Failure("网站不存在", "SITE_NOT_FOUND");
                }

                // 检查用户是否有权限更新网站
                var hasPermission = await _context.SiteUserRoles
                    .AnyAsync(sur => sur.UserId == userId && sur.SiteId == siteId && 
                                   sur.Role == SiteRoleType.Owner && sur.IsActive);

                if (!hasPermission)
                {
                    return Result<SiteDto>.Failure("您没有权限更新此网站", "INSUFFICIENT_PERMISSIONS");
                }

                // 检查域名是否可用（排除当前网站）
                if (site.Domain != updateSiteDto.Domain)
                {
                    var domainCheckResult = await IsDomainAvailableAsync(updateSiteDto.Domain, siteId);
                    if (!domainCheckResult.IsSuccess || !domainCheckResult.Data)
                    {
                        return Result<SiteDto>.Failure($"域名 {updateSiteDto.Domain} 已被使用", "DOMAIN_NOT_AVAILABLE");
                    }
                }

                // 更新网站信息
                site.Name = updateSiteDto.Name;
                site.Domain = updateSiteDto.Domain;
                site.Description = updateSiteDto.Description;
                site.Template = updateSiteDto.Template ?? site.Template;
                site.CustomCss = updateSiteDto.CustomCss;
                site.CustomJavaScript = updateSiteDto.CustomJavaScript;
                site.FaviconUrl = updateSiteDto.FaviconUrl;
                site.LogoUrl = updateSiteDto.LogoUrl;
                site.PrimaryColor = updateSiteDto.PrimaryColor;
                site.SecondaryColor = updateSiteDto.SecondaryColor;
                site.FooterText = updateSiteDto.FooterText;
                site.SocialMediaLinks = updateSiteDto.SocialMediaLinks;
                site.NavigationMenu = updateSiteDto.NavigationMenu;
                site.AnalyticsCode = updateSiteDto.AnalyticsCode;
                site.EnableComments = updateSiteDto.EnableComments;
                site.EnableSEO = updateSiteDto.EnableSEO;
                site.SiteKeywords = updateSiteDto.SiteKeywords;
                site.LastModifiedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("网站更新成功: {SiteId}", siteId);
                
                var siteDto = MapToSiteDto(site);
                return Result<SiteDto>.Success(siteDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新网站时发生错误: {SiteId}", siteId);
                return Result<SiteDto>.Failure("更新网站失败", "UPDATE_SITE_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result> DeleteSiteAsync(Guid siteId, Guid userId)
        {
            try
            {
                var site = await _context.Sites.FindAsync(siteId);
                if (site == null || !site.IsActive)
                {
                    return Result.Failure("网站不存在", "SITE_NOT_FOUND");
                }

                // 检查用户是否有权限删除网站
                var hasPermission = await _context.SiteUserRoles
                    .AnyAsync(sur => sur.UserId == userId && sur.SiteId == siteId && 
                                   sur.Role == SiteRoleType.Owner && sur.IsActive);

                if (!hasPermission)
                {
                    return Result.Failure("您没有权限删除此网站", "INSUFFICIENT_PERMISSIONS");
                }

                // 软删除：设置为非活跃状态
                site.IsActive = false;
                site.LastModifiedAt = DateTime.UtcNow;

                // 同时禁用所有相关的用户角色
                var siteRoles = await _context.SiteUserRoles
                    .Where(sr => sr.SiteId == siteId)
                    .ToListAsync();

                foreach (var role in siteRoles)
                {
                    role.IsActive = false;
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("网站删除成功: {SiteId}", siteId);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除网站时发生错误: {SiteId}", siteId);
                return Result.Failure("删除网站失败", "DELETE_SITE_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<List<SiteDto>>> GetUserSitesAsync(Guid userId)
        {
            try
            {
                // 获取用户拥有的网站
                var userSiteIds = await _context.SiteUserRoles
                    .Where(sur => sur.UserId == userId && sur.IsActive)
                    .Select(sur => sur.SiteId)
                    .ToListAsync();

                var sites = await _context.Sites
                    .Include(s => s.Owner)
                    .Where(s => userSiteIds.Contains(s.Id) && s.IsActive)
                    .OrderByDescending(s => s.CreatedAt)
                    .ToListAsync();

                var siteDtos = sites.Select(MapToSiteDto).ToList();
                return Result<List<SiteDto>>.Success(siteDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户网站时发生错误: {UserId}", userId);
                return Result<List<SiteDto>>.Failure("获取用户网站失败", "GET_USER_SITES_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<List<SiteDto>>> GetUserAccessibleSitesAsync(Guid userId)
        {
            try
            {
                // 获取用户拥有的网站
                var userSiteIds = await _context.SiteUserRoles
                    .Where(sur => sur.UserId == userId && sur.IsActive)
                    .Select(sur => sur.SiteId)
                    .ToListAsync();

                var sites = await _context.Sites
                    .Include(s => s.Owner)
                    .Where(s => userSiteIds.Contains(s.Id) && s.IsActive)
                    .OrderByDescending(s => s.CreatedAt)
                    .ToListAsync();

                var siteDtos = sites.Select(MapToSiteDto).ToList();
                return Result<List<SiteDto>>.Success(siteDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户可访问网站列表时发生错误: {UserId}", userId);
                return Result<List<SiteDto>>.Failure("获取可访问网站列表失败", "GET_ACCESSIBLE_SITES_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<PagedResult<SiteDto>>> GetAllSitesAsync(Guid userId, int pageNumber = 1, int pageSize = 20)
        {
            try
            {
                var totalCount = await _context.Sites.CountAsync(s => s.IsActive);

                var sites = await _context.Sites
                    .Include(s => s.Owner)
                    .Where(s => s.IsActive)
                    .OrderByDescending(s => s.CreatedAt)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var siteDtos = sites.Select(MapToSiteDto).ToList();
                var pagedResult = PagedResult<SiteDto>.Success(siteDtos, pageNumber, pageSize, totalCount);

                return Result<PagedResult<SiteDto>>.Success(pagedResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有网站列表时发生错误");
                return Result<PagedResult<SiteDto>>.Failure("获取网站列表失败", "GET_ALL_SITES_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<bool>> IsDomainAvailableAsync(string domain, Guid? excludeSiteId = null)
        {
            try
            {
                var query = _context.Sites.Where(s => s.Domain == domain && s.IsActive);
                
                if (excludeSiteId.HasValue)
                {
                    query = query.Where(s => s.Id != excludeSiteId.Value);
                }

                var isAvailable = !await query.AnyAsync();
                return Result<bool>.Success(isAvailable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查域名可用性时发生错误: {Domain}", domain);
                return Result<bool>.Failure("检查域名可用性失败", "CHECK_DOMAIN_AVAILABILITY_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result> PublishSiteAsync(Guid siteId, Guid userId)
        {
            try
            {
                var site = await _context.Sites.FindAsync(siteId);
                if (site == null || !site.IsActive)
                {
                    return Result.Failure("网站不存在", "SITE_NOT_FOUND");
                }

                // 检查用户是否有权限发布网站
                var hasPermission = await _context.SiteUserRoles
                    .AnyAsync(sur => sur.UserId == userId && sur.SiteId == siteId &&
                                   sur.Role == SiteRoleType.Owner && sur.IsActive);

                if (!hasPermission)
                {
                    return Result.Failure("您没有权限发布此网站", "INSUFFICIENT_PERMISSIONS");
                }

                site.IsPublished = true;
                site.LastModifiedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("网站发布成功: {SiteId}", siteId);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发布网站时发生错误: {SiteId}", siteId);
                return Result.Failure("发布网站失败", "PUBLISH_SITE_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result> UnpublishSiteAsync(Guid siteId, Guid userId)
        {
            try
            {
                var site = await _context.Sites.FindAsync(siteId);
                if (site == null || !site.IsActive)
                {
                    return Result.Failure("网站不存在", "SITE_NOT_FOUND");
                }

                // 检查用户是否有权限取消发布网站
                var hasPermission = await _context.SiteUserRoles
                    .AnyAsync(sur => sur.UserId == userId && sur.SiteId == siteId &&
                                   sur.Role == SiteRoleType.Owner && sur.IsActive);

                if (!hasPermission)
                {
                    return Result.Failure("您没有权限取消发布此网站", "INSUFFICIENT_PERMISSIONS");
                }

                site.IsPublished = false;
                site.LastModifiedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("网站取消发布成功: {SiteId}", siteId);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消发布网站时发生错误: {SiteId}", siteId);
                return Result.Failure("取消发布网站失败", "UNPUBLISH_SITE_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result> UpdateSiteSubscriptionAsync(Guid siteId, string planName, DateTime? expiryDate, Guid userId)
        {
            try
            {
                var site = await _context.Sites.FindAsync(siteId);
                if (site == null || !site.IsActive)
                {
                    return Result.Failure("网站不存在", "SITE_NOT_FOUND");
                }

                // 检查用户是否为SuperAdmin或网站Owner
                var user = await _context.Users.FindAsync(userId);
                var isOwner = await _context.SiteUserRoles
                    .AnyAsync(sur => sur.UserId == userId && sur.SiteId == siteId &&
                                   sur.Role == SiteRoleType.Owner && sur.IsActive);

                if (user?.PlatformRole != PlatformRole.SuperAdmin && !isOwner)
                {
                    return Result.Failure("您没有权限更新此网站的订阅", "INSUFFICIENT_PERMISSIONS");
                }

                site.SubscriptionPlan = planName;
                site.SubscriptionExpiresAt = expiryDate;
                site.LastModifiedAt = DateTime.UtcNow;

                // 根据订阅计划更新存储限制
                site.MaxStorageMB = planName switch
                {
                    "Free" => 100,
                    "Basic" => 1024,
                    "Premium" => 10240,
                    "Enterprise" => int.MaxValue,
                    _ => 100
                };

                await _context.SaveChangesAsync();

                _logger.LogInformation("网站订阅更新成功: {SiteId}, {PlanName}", siteId, planName);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新网站订阅时发生错误: {SiteId}", siteId);
                return Result.Failure("更新网站订阅失败", "UPDATE_SUBSCRIPTION_ERROR");
            }
        }

        /// <summary>
        /// 将Site实体映射为SiteDto
        /// </summary>
        private static SiteDto MapToSiteDto(Site site)
        {
            return new SiteDto
            {
                Id = site.Id,
                Name = site.Name,
                Domain = site.Domain,
                Description = site.Description,
                Template = site.Template,
                CustomCss = site.CustomCss,
                CustomJavaScript = site.CustomJavaScript,
                SiteConfig = site.SiteConfig,
                FaviconUrl = site.FaviconUrl,
                LogoUrl = site.LogoUrl,
                PrimaryColor = site.PrimaryColor,
                SecondaryColor = site.SecondaryColor,
                IsPublished = site.IsPublished,
                IsActive = site.IsActive,
                CreatedAt = site.CreatedAt,
                LastModifiedAt = site.LastModifiedAt,
                SubscriptionPlan = site.SubscriptionPlan,
                SubscriptionExpiresAt = site.SubscriptionExpiresAt,
                MaxStorageMB = site.MaxStorageMB,
                UsedStorageMB = site.UsedStorageMB,
                FooterText = site.FooterText,
                SocialMediaLinks = site.SocialMediaLinks,
                NavigationMenu = site.NavigationMenu,
                AnalyticsCode = site.AnalyticsCode,
                EnableComments = site.EnableComments,
                EnableSEO = site.EnableSEO,
                SiteKeywords = site.SiteKeywords
            };
        }
    }
}
