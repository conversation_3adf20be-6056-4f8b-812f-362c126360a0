is_global = true
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = MolySite.Web
build_property.RootNamespace = MolySite.Web
build_property.ProjectDir = E:\Project-SaaS-PostgreSQL\MolySite\MolySite.Web\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = E:\Project-SaaS-PostgreSQL\MolySite\MolySite.Web
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/App.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xBcHAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Layout/DashboardLayout.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcRGFzaGJvYXJkTGF5b3V0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/AccessDenied.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xBY2Nlc3NEZW5pZWQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/ComponentsShowcase.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xDb21wb25lbnRzU2hvd2Nhc2UucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Dashboard/Components/DashboardCard.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xEYXNoYm9hcmRcQ29tcG9uZW50c1xEYXNoYm9hcmRDYXJkLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Dashboard/Components/DashboardNav.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xEYXNoYm9hcmRcQ29tcG9uZW50c1xEYXNoYm9hcmROYXYucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Dashboard/Components/StatisticsPanel.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xEYXNoYm9hcmRcQ29tcG9uZW50c1xTdGF0aXN0aWNzUGFuZWwucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Dashboard/Index.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xEYXNoYm9hcmRcSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Dashboard/LegacyRedirect.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xEYXNoYm9hcmRcTGVnYWN5UmVkaXJlY3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Dashboard/MySites/Index.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xEYXNoYm9hcmRcTXlTaXRlc1xJbmRleC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Dashboard/Sites/Create.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xEYXNoYm9hcmRcU2l0ZXNcQ3JlYXRlLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Dashboard/Sites/Index.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xEYXNoYm9hcmRcU2l0ZXNcSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Dashboard/SubscriptionPlans/Index.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xEYXNoYm9hcmRcU3Vic2NyaXB0aW9uUGxhbnNcSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Dashboard/Users/<USER>
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xEYXNoYm9hcmRcVXNlcnNcQ3JlYXRlLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Dashboard/Users/<USER>
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xEYXNoYm9hcmRcVXNlcnNcSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Error.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xFcnJvci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Home/Components/CTASection.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xIb21lXENvbXBvbmVudHNcQ1RBU2VjdGlvbi5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Home/Components/FAQSection.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xIb21lXENvbXBvbmVudHNcRkFRU2VjdGlvbi5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Home/Components/FeaturesSection.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xIb21lXENvbXBvbmVudHNcRmVhdHVyZXNTZWN0aW9uLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Home/Components/HeroSection.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xIb21lXENvbXBvbmVudHNcSGVyb1NlY3Rpb24ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Home/Components/PricingSection.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xIb21lXENvbXBvbmVudHNcUHJpY2luZ1NlY3Rpb24ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Home/Components/ShowcaseSection.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xIb21lXENvbXBvbmVudHNcU2hvd2Nhc2VTZWN0aW9uLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Index.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xJbmRleC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Login.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xMb2dpbi5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Register.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xSZWdpc3Rlci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/TailwindTest.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xUYWlsd2luZFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/RedirectToLogin.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xSZWRpcmVjdFRvTG9naW4ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Routes.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xSb3V0ZXMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Shared/AuthorizingComponent.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xTaGFyZWRcQXV0aG9yaXppbmdDb21wb25lbnQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Shared/ConfirmDialog.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xTaGFyZWRcQ29uZmlybURpYWxvZy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Shared/HeroIcon.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xTaGFyZWRcSGVyb0ljb24ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Shared/Pagination.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xTaGFyZWRcUGFnaW5hdGlvbi5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Sites/Components/SiteNotFound.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xTaXRlc1xDb21wb25lbnRzXFNpdGVOb3RGb3VuZC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Sites/Components/SitePageRenderer.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xTaXRlc1xDb21wb25lbnRzXFNpdGVQYWdlUmVuZGVyZXIucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Sites/Layouts/SiteLayout.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xTaXRlc1xMYXlvdXRzXFNpdGVMYXlvdXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Sites/SiteRouteHandler.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xTaXRlc1xTaXRlUm91dGVIYW5kbGVyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xfSW1wb3J0cy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = X0ltcG9ydHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Layout/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcTWFpbkxheW91dC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = b-7mhs7g97z7

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Layout/NavMenu.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcTmF2TWVudS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = b-rkm042n77k

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Layout/PublicLayout.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcUHVibGljTGF5b3V0LnJhem9y
build_metadata.AdditionalFiles.CssScope = b-zljedqn1vf

[E:/Project-SaaS-PostgreSQL/MolySite/MolySite.Web/Components/Pages/Dashboard/Components/DashboardLayoutComponent.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xEYXNoYm9hcmRcQ29tcG9uZW50c1xEYXNoYm9hcmRMYXlvdXRDb21wb25lbnQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = b-a02jn9xbct
