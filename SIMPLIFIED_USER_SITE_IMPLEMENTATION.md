# 🎯 简化版用户网站前端实施方案

## 📋 方案概述

**目标**: 平台前端 + 用户标准展示型网站前端
**特点**: 简化设计、快速实现、标准展示
**开发周期**: 4-6周
**核心功能**: 企业/个人展示型网站

## 🏗️ 简化架构设计

### 1. 整体架构

```
MolySite.Web (统一应用)
├── 智能路由层
│   ├── Platform Route (molysite.com)
│   └── Site Route (*.molysite.com)
├── Platform Frontend (现有)
│   ├── PublicLayout
│   ├── DashboardLayout  
│   └── MainLayout
└── Sites Frontend (新增)
    ├── StandardSiteLayout (标准网站布局)
    ├── SiteComponents (网站组件)
    └── SiteServices (网站服务)
```

### 2. 路由策略

```csharp
// 简化的路由判断
public class SiteRouter
{
    public bool IsSiteRequest(HttpContext context)
    {
        var host = context.Request.Host.Host;
        var subdomain = GetSubdomain(host);
        
        // 平台域名
        if (subdomain == null || subdomain == "www" || subdomain == "api")
            return false;
            
        // 用户网站域名
        return true;
    }
}

// 路由示例:
// molysite.com         -> Platform Frontend
// www.molysite.com     -> Platform Frontend
// mysite.molysite.com  -> Sites Frontend
// company.molysite.com -> Sites Frontend
```

## 🎨 标准展示型网站设计

### 1. 网站结构

```
标准展示型网站包含:
├── 首页 (Home)           # 网站主页
├── 关于我们 (About)       # 公司/个人介绍
├── 服务/产品 (Services)   # 服务或产品展示
├── 联系我们 (Contact)     # 联系方式
└── 自定义页面 (Custom)    # 用户自定义页面
```

### 2. 页面类型定义

```csharp
// Models/SitePage.cs
public class SitePage
{
    public Guid Id { get; set; }
    public Guid SiteId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Slug { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public PageType Type { get; set; }
    public bool IsPublished { get; set; }
    public int SortOrder { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public enum PageType
{
    Home,       // 首页
    About,      // 关于
    Services,   // 服务
    Contact,    // 联系
    Custom      // 自定义
}
```

## 🔧 核心技术实现

### 1. 智能路由中间件

```csharp
// Middleware/SiteRoutingMiddleware.cs
public class SiteRoutingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ISiteService _siteService;

    public async Task InvokeAsync(HttpContext context)
    {
        var siteRouter = new SiteRouter();
        
        if (siteRouter.IsSiteRequest(context))
        {
            // 用户网站请求
            var subdomain = siteRouter.GetSubdomain(context.Request.Host.Host);
            var site = await _siteService.GetSiteByDomainAsync(subdomain);
            
            if (site != null && site.IsPublished)
            {
                // 设置网站上下文
                context.Items["IsSiteRequest"] = true;
                context.Items["CurrentSite"] = site;
                context.Items["SiteDomain"] = subdomain;
            }
            else
            {
                // 网站不存在或未发布，显示404
                context.Response.StatusCode = 404;
                await context.Response.WriteAsync("网站不存在或未发布");
                return;
            }
        }
        else
        {
            // 平台请求
            context.Items["IsSiteRequest"] = false;
        }

        await _next(context);
    }
}
```

### 2. 动态路由组件

```razor
@* Components/Routes.razor (修改现有) *@
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Authorization
@using MolySite.Web.Components.Layout
@using MolySite.Web.Components.Sites
@inject IHttpContextAccessor HttpContextAccessor
@inject ILogger<Routes> Logger

<Router AppAssembly="@typeof(Program).Assembly">
    <Found Context="routeData">
        @{
            var httpContext = HttpContextAccessor.HttpContext;
            var isSiteRequest = httpContext?.Items["IsSiteRequest"] as bool? ?? false;
        }

        @if (isSiteRequest)
        {
            <!-- 用户网站路由 -->
            <SiteRouteHandler RouteData="@routeData" />
        }
        else
        {
            <!-- 平台路由 (现有逻辑) -->
            @{
                var pageName = routeData.PageType.Name;
                var hasAllowAnonymous = routeData.PageType.GetCustomAttributes(typeof(AllowAnonymousAttribute), false).Any();
                var isPublicPage = PublicPageConfiguration.IsPublicPage(pageName) || hasAllowAnonymous;
            }

            @if (isPublicPage)
            {
                <RouteView RouteData="@routeData" DefaultLayout="@typeof(PublicLayout)" />
            }
            else
            {
                <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(PublicLayout)">
                    <NotAuthorized>
                        @if (context.User.Identity?.IsAuthenticated != true)
                        {
                            <RedirectToLogin ReturnUrl="@NavigationManager.ToBaseRelativePath(NavigationManager.Uri)" />
                        }
                        else
                        {
                            <AccessDenied />
                        }
                    </NotAuthorized>
                </AuthorizeRouteView>
            }
        }
    </Found>
    <NotFound>
        @{
            var httpContext = HttpContextAccessor.HttpContext;
            var isSiteRequest = httpContext?.Items["IsSiteRequest"] as bool? ?? false;
        }

        @if (isSiteRequest)
        {
            <!-- 用户网站404页面 -->
            <SiteNotFound />
        }
        else
        {
            <!-- 平台404页面 (现有) -->
            <PageTitle>Not found</PageTitle>
            <LayoutView Layout="@typeof(PublicLayout)">
                <div class="container-responsive mt-12">
                    <div class="bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-lg">
                        <h3 class="text-xl font-semibold mb-2">404 - 页面未找到</h3>
                        <p class="mb-4">抱歉，您请求的页面不存在。</p>
                        <a href="/" class="btn-primary">返回首页</a>
                    </div>
                </div>
            </LayoutView>
        }
    </NotFound>
</Router>
```

### 3. 网站路由处理器

```razor
@* Components/Sites/SiteRouteHandler.razor *@
@inject IHttpContextAccessor HttpContextAccessor
@inject ISitePageService SitePageService
@inject ILogger<SiteRouteHandler> Logger

@if (_currentSite != null)
{
    <LayoutView Layout="@typeof(StandardSiteLayout)">
        <SitePageRenderer Page="@_currentPage" Site="@_currentSite" />
    </LayoutView>
}

@code {
    [Parameter] public RouteData RouteData { get; set; } = default!;
    
    private SiteDto? _currentSite;
    private SitePageDto? _currentPage;

    protected override async Task OnInitializedAsync()
    {
        var httpContext = HttpContextAccessor.HttpContext;
        _currentSite = httpContext?.Items["CurrentSite"] as SiteDto;
        
        if (_currentSite != null)
        {
            // 获取当前页面
            var path = httpContext?.Request.Path.Value?.TrimStart('/') ?? "";
            _currentPage = await GetPageByPathAsync(path);
        }
    }

    private async Task<SitePageDto?> GetPageByPathAsync(string path)
    {
        if (string.IsNullOrEmpty(path))
        {
            // 首页
            return await SitePageService.GetHomePageAsync(_currentSite!.Id);
        }
        
        // 根据slug查找页面
        return await SitePageService.GetPageBySlugAsync(_currentSite!.Id, path);
    }
}
```

## 🎨 标准网站布局

### 1. 标准网站布局组件

```razor
@* Components/Sites/Layouts/StandardSiteLayout.razor *@
@inherits LayoutComponentBase
@inject IHttpContextAccessor HttpContextAccessor
@inject IJSRuntime JSRuntime

<div class="standard-site-layout" data-site-id="@Site?.Id">
    <!-- 动态注入网站自定义CSS -->
    @if (!string.IsNullOrEmpty(Site?.CustomCss))
    {
        <style>@((MarkupString)Site.CustomCss)</style>
    }
    
    <!-- 网站头部 -->
    <SiteHeader Site="@Site" />
    
    <!-- 网站导航 -->
    <SiteNavigation Site="@Site" />
    
    <!-- 主要内容 -->
    <main class="site-main">
        @Body
    </main>
    
    <!-- 网站底部 -->
    <SiteFooter Site="@Site" />
    
    <!-- 动态注入网站自定义JavaScript -->
    @if (!string.IsNullOrEmpty(Site?.CustomJavaScript))
    {
        <script>@((MarkupString)Site.CustomJavaScript)</script>
    }
</div>

@code {
    private SiteDto? Site;

    protected override void OnInitialized()
    {
        var httpContext = HttpContextAccessor.HttpContext;
        Site = httpContext?.Items["CurrentSite"] as SiteDto;
    }
}
```

### 2. 网站头部组件

```razor
@* Components/Sites/Components/SiteHeader.razor *@
<header class="site-header">
    <div class="container mx-auto px-4">
        <div class="flex items-center justify-between h-16">
            <!-- 网站Logo和标题 -->
            <div class="flex items-center">
                @if (!string.IsNullOrEmpty(Site.LogoUrl))
                {
                    <img src="@Site.LogoUrl" alt="@Site.Name" class="h-10 w-auto mr-3" />
                }
                <h1 class="text-xl font-bold text-gray-900">@Site.Name</h1>
            </div>
            
            <!-- 网站描述 -->
            @if (!string.IsNullOrEmpty(Site.Description))
            {
                <p class="hidden md:block text-gray-600">@Site.Description</p>
            }
        </div>
    </div>
</header>

@code {
    [Parameter] public SiteDto Site { get; set; } = default!;
}
```

### 3. 网站导航组件

```razor
@* Components/Sites/Components/SiteNavigation.razor *@
@inject ISitePageService SitePageService

<nav class="site-navigation bg-white shadow-sm">
    <div class="container mx-auto px-4">
        <div class="flex space-x-8">
            @foreach (var navItem in _navigationItems)
            {
                <a href="@navItem.Url" 
                   class="py-4 px-2 text-gray-700 hover:text-blue-600 transition-colors duration-200 @GetActiveClass(navItem.Url)">
                    @navItem.Title
                </a>
            }
        </div>
    </div>
</nav>

@code {
    [Parameter] public SiteDto Site { get; set; } = default!;
    
    private List<NavigationItem> _navigationItems = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadNavigationAsync();
    }

    private async Task LoadNavigationAsync()
    {
        var pages = await SitePageService.GetPublishedPagesAsync(Site.Id);
        
        _navigationItems = pages
            .OrderBy(p => p.SortOrder)
            .Select(p => new NavigationItem
            {
                Title = p.Title,
                Url = p.Type == PageType.Home ? "/" : $"/{p.Slug}"
            })
            .ToList();
    }

    private string GetActiveClass(string url)
    {
        // TODO: 实现当前页面高亮逻辑
        return "";
    }

    private class NavigationItem
    {
        public string Title { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
    }
}
```

### 4. 页面内容渲染器

```razor
@* Components/Sites/Components/SitePageRenderer.razor *@
@inject IMarkdownService MarkdownService

<div class="site-page-renderer">
    @if (Page != null)
    {
        <!-- 页面标题 -->
        <div class="page-header py-8 bg-gray-50">
            <div class="container mx-auto px-4">
                <h1 class="text-3xl font-bold text-gray-900">@Page.Title</h1>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="page-content py-8">
            <div class="container mx-auto px-4">
                <div class="prose max-w-none">
                    @((MarkupString)ProcessContent(Page.Content))
                </div>
            </div>
        </div>
    }
    else
    {
        <!-- 页面不存在 -->
        <div class="page-not-found py-16 text-center">
            <div class="container mx-auto px-4">
                <h1 class="text-2xl font-bold text-gray-900 mb-4">页面未找到</h1>
                <p class="text-gray-600 mb-8">抱歉，您访问的页面不存在。</p>
                <a href="/" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    返回首页
                </a>
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public SitePageDto? Page { get; set; }
    [Parameter] public SiteDto Site { get; set; } = default!;

    private string ProcessContent(string content)
    {
        // 支持Markdown和HTML内容
        if (content.Contains("# ") || content.Contains("## "))
        {
            // 可能是Markdown内容
            return MarkdownService.ToHtml(content);
        }
        
        // HTML内容
        return content;
    }
}
```

## 🔧 数据服务实现

### 1. 网站页面服务接口

```csharp
// Services/ISitePageService.cs
public interface ISitePageService
{
    Task<SitePageDto?> GetHomePageAsync(Guid siteId);
    Task<SitePageDto?> GetPageBySlugAsync(Guid siteId, string slug);
    Task<List<SitePageDto>> GetPublishedPagesAsync(Guid siteId);
    Task<SitePageDto> CreatePageAsync(CreateSitePageDto createDto);
    Task<SitePageDto> UpdatePageAsync(Guid pageId, UpdateSitePageDto updateDto);
    Task<bool> DeletePageAsync(Guid pageId);
}
```

### 2. 网站页面服务实现

```csharp
// Services/SitePageService.cs
public class SitePageService : ISitePageService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<SitePageService> _logger;

    public SitePageService(HttpClient httpClient, ILogger<SitePageService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<SitePageDto?> GetHomePageAsync(Guid siteId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/sites/{siteId}/pages/home");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<ApiResponse<SitePageDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                
                return result?.Data;
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取网站首页失败: {SiteId}", siteId);
            return null;
        }
    }

    public async Task<SitePageDto?> GetPageBySlugAsync(Guid siteId, string slug)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/sites/{siteId}/pages/{slug}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<ApiResponse<SitePageDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                
                return result?.Data;
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取网站页面失败: {SiteId}, {Slug}", siteId, slug);
            return null;
        }
    }

    public async Task<List<SitePageDto>> GetPublishedPagesAsync(Guid siteId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/sites/{siteId}/pages?published=true");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<ApiResponse<List<SitePageDto>>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                
                return result?.Data ?? new List<SitePageDto>();
            }
            
            return new List<SitePageDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取网站页面列表失败: {SiteId}", siteId);
            return new List<SitePageDto>();
        }
    }
}
```

## 📱 响应式设计

### 1. 标准网站CSS

```css
/* wwwroot/css/standard-site.css */
.standard-site-layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.site-header {
    background: white;
    border-bottom: 1px solid #e5e7eb;
}

.site-navigation {
    position: sticky;
    top: 0;
    z-index: 10;
}

.site-main {
    flex: 1;
}

.site-footer {
    background: #f9fafb;
    border-top: 1px solid #e5e7eb;
    padding: 2rem 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .site-navigation .flex {
        flex-direction: column;
        space-y: 0;
    }
    
    .site-navigation a {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #e5e7eb;
    }
}

/* 内容样式 */
.prose {
    max-width: none;
}

.prose h1 {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.prose h2 {
    font-size: 1.875rem;
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.prose p {
    margin-bottom: 1rem;
    line-height: 1.7;
}

.prose img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
}
```

## 🚀 实施步骤

### 第1周: 基础架构
- ✅ 实现智能路由中间件
- ✅ 修改Routes组件支持双路由
- ✅ 创建Sites模块基础结构
- ✅ 实现SiteRouteHandler

### 第2周: 标准布局
- ✅ 创建StandardSiteLayout
- ✅ 实现SiteHeader、SiteNavigation、SiteFooter
- ✅ 实现SitePageRenderer
- ✅ 添加响应式CSS

### 第3周: 数据服务
- ✅ 实现SitePageService
- ✅ 创建页面管理API
- ✅ 实现页面CRUD功能
- ✅ 添加Markdown支持

### 第4周: 管理界面
- ✅ 在Dashboard中添加页面管理
- ✅ 实现页面编辑器
- ✅ 添加网站预览功能
- ✅ 实现网站发布功能

### 第5-6周: 优化完善
- ✅ 性能优化和缓存
- ✅ SEO基础支持
- ✅ 错误处理完善
- ✅ 测试和调试

## 🎯 预期效果

实现后，用户可以：
1. ✅ 在Dashboard创建网站
2. ✅ 添加和编辑网站页面（首页、关于、服务、联系等）
3. ✅ 自定义网站样式和脚本
4. ✅ 发布网站到子域名
5. ✅ 访问 `mysite.molysite.com` 查看网站
6. ✅ 网站具有标准的企业/个人展示功能

这个简化方案专注于核心功能，避免了复杂的博客系统，但提供了完整的标准展示型网站功能。🚀
