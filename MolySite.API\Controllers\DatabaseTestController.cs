using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MolySite.Identity.Data;
using MolySite.Identity.Models;

namespace MolySite.API.Controllers
{
    /// <summary>
    /// 数据库连接测试控制器
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class DatabaseTestController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<DatabaseTestController> _logger;

        public DatabaseTestController(
            ApplicationDbContext context,
            ILogger<DatabaseTestController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        [HttpGet("connection")]
        public async Task<IActionResult> TestConnection()
        {
            try
        {
                _logger.LogInformation("开始测试数据库连接...");

                // 测试数据库连接
                var canConnect = await _context.Database.CanConnectAsync();
                
                if (!canConnect)
                {
                    return StatusCode(500, new { 
                        success = false, 
                        message = "无法连接到数据库" 
                    });
                }

                // 获取数据库信息
                var connectionString = _context.Database.GetConnectionString();
                var databaseName = _context.Database.GetDbConnection().Database;
                
                _logger.LogInformation("数据库连接测试成功");

                return Ok(new
                {
                    success = true,
                    message = "数据库连接成功",
                    database = databaseName,
                    provider = "PostgreSQL",
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据库连接测试失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "数据库连接失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 测试数据库表结构
        /// </summary>
        [HttpGet("tables")]
        public async Task<IActionResult> TestTables()
        {
            try
            {
                _logger.LogInformation("开始测试数据库表结构...");

                var tables = new List<object>();

                // 测试用户表
                var userCount = await _context.Users.CountAsync();
                tables.Add(new { table = "AspNetUsers", count = userCount });

                // 测试网站表
                var siteCount = await _context.Sites.CountAsync();
                tables.Add(new { table = "Sites", count = siteCount });

                // 测试角色表
                var roleCount = await _context.Roles.CountAsync();
                tables.Add(new { table = "AspNetRoles", count = roleCount });

                // 测试订阅计划表
                var planCount = await _context.SubscriptionPlans.CountAsync();
                tables.Add(new { table = "SubscriptionPlans", count = planCount });

                _logger.LogInformation("数据库表结构测试成功");

                return Ok(new
                {
                    success = true,
                    message = "数据库表结构正常",
                    tables = tables,
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据库表结构测试失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "数据库表结构测试失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 测试基本CRUD操作
        /// </summary>
        [HttpPost("crud")]
        public async Task<IActionResult> TestCrud()
        {
            try
            {
                _logger.LogInformation("开始测试基本CRUD操作...");

                // 创建测试用户
                var testUser = new ApplicationUser
                {
                    Id = Guid.NewGuid(),
                    UserName = $"test_user_{DateTime.UtcNow.Ticks}",
                    Email = $"test_{DateTime.UtcNow.Ticks}@test.com",
                    PlatformRole = PlatformRole.User,
                    PlatformRoles = new List<string> { "SiteOwner" },
                    Permissions = new List<string>(),
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    TimeZone = "UTC",
                    Language = "zh-CN",
                    EmailNotifications = true
                };

                // Create
                _context.Users.Add(testUser);
                await _context.SaveChangesAsync();

                // Read
                var retrievedUser = await _context.Users.FindAsync(testUser.Id);
                if (retrievedUser == null)
                {
                    throw new Exception("无法读取创建的测试用户");
                }

                // Update
                retrievedUser.DisplayName = "测试用户";
                await _context.SaveChangesAsync();

                // Delete
                _context.Users.Remove(retrievedUser);
                await _context.SaveChangesAsync();

                _logger.LogInformation("基本CRUD操作测试成功");

                return Ok(new
                {
                    success = true,
                    message = "基本CRUD操作测试成功",
                    operations = new[] { "Create", "Read", "Update", "Delete" },
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "基本CRUD操作测试失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "基本CRUD操作测试失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 获取数据库状态信息
        /// </summary>
        [HttpGet("status")]
        public async Task<IActionResult> GetDatabaseStatus()
        {
            try
            {
                var status = new
                {
                    connected = await _context.Database.CanConnectAsync(),
                    database = _context.Database.GetDbConnection().Database,
                    provider = _context.Database.ProviderName,
                    migrations = await _context.Database.GetAppliedMigrationsAsync(),
                    pendingMigrations = await _context.Database.GetPendingMigrationsAsync(),
                    timestamp = DateTime.UtcNow
                };

                return Ok(new
                {
                    success = true,
                    status = status
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取数据库状态失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取数据库状态失败",
                    error = ex.Message
                });
            }
        }
    }
}
