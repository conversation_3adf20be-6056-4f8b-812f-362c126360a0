using System.Security.Claims;

namespace MolySite.Web.Services
{
    /// <summary>
    /// 前端权限检查服务实现
    /// </summary>
    public class PermissionService : IPermissionService
    {
        private readonly ILogger<PermissionService> _logger;
        private readonly ISiteService _siteService;

        public PermissionService(ILogger<PermissionService> logger, ISiteService siteService)
        {
            _logger = logger;
            _siteService = siteService;
        }

        /// <inheritdoc/>
        public bool HasPlatformPermission(ClaimsPrincipal user, string permission)
        {
            if (user?.Identity?.IsAuthenticated != true)
                return false;

            // SuperAdmin拥有所有平台权限
            if (IsSuperAdmin(user))
                return true;

            // 根据权限类型检查
            return permission switch
            {
                PlatformPermissions.FullAccess => IsSuperAdmin(user),
                PlatformPermissions.ViewSettings => IsSuperAdmin(user),
                PlatformPermissions.ManageSettings => IsSuperAdmin(user),
                PlatformPermissions.ViewAllUsers => IsSuperAdmin(user),
                PlatformPermissions.ManageAllUsers => IsSuperAdmin(user),
                PlatformPermissions.ViewAllSites => IsSuperAdmin(user),
                PlatformPermissions.ManageAllSites => IsSuperAdmin(user),
                PlatformPermissions.ViewStatistics => IsSuperAdmin(user),
                PlatformPermissions.ManageSubscriptions => IsSuperAdmin(user),
                _ => false
            };
        }

        /// <inheritdoc/>
        public async Task<bool> HasSitePermissionAsync(ClaimsPrincipal user, Guid siteId, string permission)
        {
            if (user?.Identity?.IsAuthenticated != true)
                return false;

            // SuperAdmin只有有限的网站权限（不能访问网站内容和隐私信息）
            if (IsSuperAdmin(user))
            {
                return permission switch
                {
                    SitePermissions.ViewSettings => true,  // 查看基本设置
                    SitePermissions.ManageUsers => true,   // 管理用户（平台管理需要）
                    _ => false  // 其他权限都不允许
                };
            }

            // TODO: 实现基于SiteUserRole表的权限检查
            // 这里需要调用API来检查用户在特定站点的权限
            var userRole = GetUserRole(user);

            return permission switch
            {
                SitePermissions.ViewSettings => userRole == UserRoles.SiteOwner,
                SitePermissions.EditSettings => userRole == UserRoles.SiteOwner,
                SitePermissions.ManageUsers => userRole == UserRoles.SiteOwner,
                SitePermissions.ViewContent => userRole == UserRoles.SiteOwner || userRole == UserRoles.SiteEditor,
                SitePermissions.EditContent => userRole == UserRoles.SiteOwner || userRole == UserRoles.SiteEditor,
                SitePermissions.Publish => userRole == UserRoles.SiteOwner,
                SitePermissions.ViewStatistics => userRole == UserRoles.SiteOwner,
                SitePermissions.ManageMedia => userRole == UserRoles.SiteOwner || userRole == UserRoles.SiteEditor,
                SitePermissions.Follow => userRole == UserRoles.User || userRole == UserRoles.SiteOwner || userRole == UserRoles.SiteEditor,
                SitePermissions.ReceiveNotifications => userRole == UserRoles.SiteFollower || userRole == UserRoles.SiteOwner || userRole == UserRoles.SiteEditor,
                SitePermissions.Contact => userRole == UserRoles.SiteFollower || userRole == UserRoles.SiteOwner || userRole == UserRoles.SiteEditor,
                SitePermissions.ViewFollowerContent => userRole == UserRoles.SiteFollower || userRole == UserRoles.SiteOwner || userRole == UserRoles.SiteEditor,
                _ => false
            };
        }

        /// <inheritdoc/>
        public string GetUserRole(ClaimsPrincipal user)
        {
            if (user?.Identity?.IsAuthenticated != true)
                return "";

            return user.FindFirst(ClaimTypes.Role)?.Value ?? "";
        }

        /// <inheritdoc/>
        public bool IsSuperAdmin(ClaimsPrincipal user)
        {
            return user?.IsInRole(UserRoles.SuperAdmin) == true;
        }

        /// <inheritdoc/>
        public bool IsUser(ClaimsPrincipal user)
        {
            return user?.IsInRole(UserRoles.User) == true;
        }

        /// <inheritdoc/>
        public bool IsSiteOwner(ClaimsPrincipal user)
        {
            return user?.IsInRole(UserRoles.SiteOwner) == true;
        }

        /// <inheritdoc/>
        public bool IsSiteEditor(ClaimsPrincipal user)
        {
            return user?.IsInRole(UserRoles.SiteEditor) == true;
        }

        /// <inheritdoc/>
        public bool IsSiteFollower(ClaimsPrincipal user)
        {
            return user?.IsInRole(UserRoles.SiteFollower) == true;
        }

        /// <inheritdoc/>
        public async Task<List<Guid>> GetUserAccessibleSitesAsync(ClaimsPrincipal user)
        {
            if (user?.Identity?.IsAuthenticated != true)
                return new List<Guid>();

            try
            {
                // SuperAdmin可以查看所有站点的基本信息，但不能访问内容
                if (IsSuperAdmin(user))
                {
                    var allSites = await _siteService.GetAllSitesAsync();
                    return allSites.Select(s => s.Id).ToList();
                }

                // 普通用户只能访问自己有权限的站点
                // TODO: 实现基于SiteUserRole表的站点查询
                var mySites = await _siteService.GetMySitesAsync();
                return mySites.Select(s => s.Id).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户可访问站点列表时发生错误");
                return new List<Guid>();
            }
        }
    }
}
