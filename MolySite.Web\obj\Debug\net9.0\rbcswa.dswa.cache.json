{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["WDXu9FveOEvyuHpKjSWJGJapZ5jICfWs+Ko698QGuek=", "h1wV+aMdqDvvK1imLovDcltr5avRQ4PeO4k6TZl8mcs=", "FeoN4CsgXj4NXGSYRF3OR6l7gPf4oQbX1XeL0YygQO0=", "v4kDMx5uF3ggH3ICkXGU/5bZcdsZP4KZIUWMT19iz0I=", "e2jEdy295N9BI1c7omLgh7gce2UkoIAIKIWN6T7jWX4=", "IPWN2JMSbzA89BWBzNfrgfJdsbxd9c2889bhNck8izA=", "Lc6j7XSOmLz1lUpEdOBc6VeOL+dETMqp9p/gVvhp9K8=", "E721hxADVKvxhre5tzJA0oSF0qcToRe3Yk80BdVf25c="], "CachedAssets": {"FeoN4CsgXj4NXGSYRF3OR6l7gPf4oQbX1XeL0YygQO0=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\y2fooq3isi-frz2k1ad57.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "css/tailwind#[.{fingerprint=frz2k1ad57}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j1p9my5d34", "Integrity": "+eojRBy5eYG7gLIJBwvt3+BJm9RY2TPoWTw/1/b/ef8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\tailwind.css", "FileLength": 11947, "LastWriteTime": "2025-06-24T10:21:11.8011167+00:00"}, "e2jEdy295N9BI1c7omLgh7gce2UkoIAIKIWN6T7jWX4=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\x7wqaayaif-1wi6zlpeou.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "js/performance-monitor#[.{fingerprint=1wi6zlpeou}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\performance-monitor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bwlmk2cci1", "Integrity": "W7Qr0WZF8BwwinZMll66JpF0UpCKmSQ99BfgNIjdYBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\performance-monitor.js", "FileLength": 1941, "LastWriteTime": "2025-06-24T10:21:11.7971016+00:00"}, "Lc6j7XSOmLz1lUpEdOBc6VeOL+dETMqp9p/gVvhp9K8=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\ll5cbn73iy-56vxggxec6.gz", "SourceId": "MolySite.Web", "SourceType": "Computed", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "MolySite.Web#[.{fingerprint=56vxggxec6}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\MolySite.Web.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xwa4cwue1o", "Integrity": "QXwHO2MGAzteDsxybLlquFOGIAgRMn3CntpAROkEm9w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\MolySite.Web.styles.css", "FileLength": 4213, "LastWriteTime": "2025-06-24T10:21:11.7991097+00:00"}, "h1wV+aMdqDvvK1imLovDcltr5avRQ4PeO4k6TZl8mcs=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\qftk4dc618-5ipweew5fc.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "css/dashboard#[.{fingerprint=5ipweew5fc}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\dashboard.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\dashboard.css", "FileLength": 0, "LastWriteTime": "2025-06-24T10:21:11.7991097+00:00"}, "E721hxADVKvxhre5tzJA0oSF0qcToRe3Yk80BdVf25c=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\if8keuzty8-56vxggxec6.gz", "SourceId": "MolySite.Web", "SourceType": "Computed", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "MolySite.Web#[.{fingerprint=56vxggxec6}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\MolySite.Web.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xwa4cwue1o", "Integrity": "QXwHO2MGAzteDsxybLlquFOGIAgRMn3CntpAROkEm9w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\MolySite.Web.bundle.scp.css", "FileLength": 4213, "LastWriteTime": "2025-06-24T10:21:11.8127672+00:00"}, "v4kDMx5uF3ggH3ICkXGU/5bZcdsZP4KZIUWMT19iz0I=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\yygdecoiyq-ab62h7jd1e.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "js/app#[.{fingerprint=ab62h7jd1e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ep95u4nrfa", "Integrity": "Yl6RfmWHmQsMOvoTFMndXStjt6M6jFMLzvnqHNL0G5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\app.js", "FileLength": 1246, "LastWriteTime": "2025-06-24T10:21:11.7950951+00:00"}, "WDXu9FveOEvyuHpKjSWJGJapZ5jICfWs+Ko698QGuek=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\4yau8vpo2c-2zmnmq58fl.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "app#[.{fingerprint=2zmnmq58fl}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j2subczbn7", "Integrity": "m3TOaMa/dbFUHAbraOyLHPIUciPPJibKnA0F3VeZ458=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\app.css", "FileLength": 2021, "LastWriteTime": "2025-06-24T10:21:11.7813114+00:00"}, "IPWN2JMSbzA89BWBzNfrgfJdsbxd9c2889bhNck8izA=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\9qndxilbbr-26f9b7qkas.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap-icons/font/bootstrap-icons.min#[.{fingerprint=26f9b7qkas}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap-icons\\font\\bootstrap-icons.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o6cudrsz94", "Integrity": "0G38FUtwEm3D6mNy0IJsswxRAeP1u8XxqJsRyIEItlg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap-icons\\font\\bootstrap-icons.min.css", "FileLength": 13662, "LastWriteTime": "2025-06-24T10:21:11.8091512+00:00"}}, "CachedCopyCandidates": {}}