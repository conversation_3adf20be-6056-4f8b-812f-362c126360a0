using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MolySite.Core.Controllers;
using MolySite.Core.Interfaces;
using MolySite.Shared.Dtos;

namespace MolySite.API.Controllers
{
    /// <summary>
    /// 用户管理控制器
    /// </summary>
    [Route("api/[controller]")]
    [Authorize]
    public class UsersController : BaseApiController
    {
        private readonly IUserService _userService;
        private readonly IFollowService _followService;
        private readonly ILogger<UsersController> _logger;

        public UsersController(
            IUserService userService,
            IFollowService followService,
            ILogger<UsersController> logger)
        {
            _userService = userService;
            _followService = followService;
            _logger = logger;
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        /// <returns>用户信息</returns>
        [HttpGet("me")]
        public async Task<IActionResult> GetCurrentUser()
        {
            var userId = GetCurrentUserId();
            var result = await _userService.GetUserByIdAsync(userId);

            return HandleResult(result);
        }

        /// <summary>
        /// 更新当前用户信息
        /// </summary>
        /// <param name="updateUserDto">更新用户信息</param>
        /// <returns>更新结果</returns>
        [HttpPut("me")]
        public async Task<IActionResult> UpdateCurrentUser([FromBody] UpdateUserDto updateUserDto)
        {
            var userId = GetCurrentUserId();
            var result = await _userService.UpdateUserAsync(userId, updateUserDto);

            return HandleResult(result);
        }

        /// <summary>
        /// 获取用户关注的网站列表
        /// </summary>
        /// <returns>关注的网站列表</returns>
        [HttpGet("me/followed-sites")]
        public async Task<IActionResult> GetMyFollowedSites()
        {
            var userId = GetCurrentUserId();
            var result = await _followService.GetFollowedSitesAsync(userId);

            if (result.IsSuccess)
            {
                // 转换为DTO
                var followedSiteDtos = result.Data!.Select(site => new FollowedSiteDto
                {
                    SiteId = site.Id,
                    SiteName = site.Name,
                    Domain = site.Domain,
                    Description = site.Description,
                    LogoUrl = site.LogoUrl,
                    FollowedAt = DateTime.UtcNow, // TODO: 从SiteUserRole获取实际关注时间
                    LastUpdated = site.LastModifiedAt
                }).ToList();

                return Ok(followedSiteDtos);
            }

            return HandleResult(result);
        }

        /// <summary>
        /// 获取用户的网站列表（包括拥有的和参与编辑的）
        /// </summary>
        /// <returns>用户网站列表</returns>
        [HttpGet("me/sites")]
        public async Task<IActionResult> GetMySites()
        {
            var userId = GetCurrentUserId();
            var result = await _userService.GetUserSitesAsync(userId);

            return HandleResult(result);
        }

        /// <summary>
        /// 获取用户统计信息
        /// </summary>
        /// <returns>用户统计信息</returns>
        [HttpGet("me/stats")]
        public async Task<IActionResult> GetMyStats()
        {
            var userId = GetCurrentUserId();
            
            // 获取用户拥有的网站数量
            var sitesResult = await _userService.GetUserSitesAsync(userId);
            var ownedSitesCount = sitesResult.IsSuccess ? sitesResult.Data!.Count : 0;

            // 获取用户关注的网站数量
            var followedSitesResult = await _followService.GetFollowedSitesAsync(userId);
            var followedSitesCount = followedSitesResult.IsSuccess ? followedSitesResult.Data!.Count : 0;

            var stats = new UserStatsDto
            {
                OwnedSitesCount = ownedSitesCount,
                FollowedSitesCount = followedSitesCount,
                // TODO: 添加更多统计信息，如总访问量、总关注者数等
            };

            return Ok(stats);
        }

        /// <summary>
        /// 搜索用户（仅SuperAdmin可访问）
        /// </summary>
        /// <param name="query">搜索关键词</param>
        /// <param name="pageNumber">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>用户列表</returns>
        [HttpGet("search")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> SearchUsers(
            [FromQuery] string? query = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            var result = await _userService.SearchUsersAsync(query, pageNumber, pageSize);

            return HandleResult(result);
        }

        /// <summary>
        /// 获取指定用户信息（仅SuperAdmin可访问）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息</returns>
        [HttpGet("{userId}")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> GetUser(Guid userId)
        {
            var result = await _userService.GetUserByIdAsync(userId);

            return HandleResult(result);
        }

        /// <summary>
        /// 暂停/恢复用户账户（仅SuperAdmin可访问）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="suspend">是否暂停</param>
        /// <returns>操作结果</returns>
        [HttpPost("{userId}/suspend")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> SuspendUser(Guid userId, [FromBody] bool suspend)
        {
            var result = await _userService.SuspendUserAsync(userId, suspend);

            return HandleResult(result);
        }
    }

    /// <summary>
    /// 用户统计信息DTO
    /// </summary>
    public class UserStatsDto
    {
        /// <summary>
        /// 拥有的网站数量
        /// </summary>
        public int OwnedSitesCount { get; set; }

        /// <summary>
        /// 关注的网站数量
        /// </summary>
        public int FollowedSitesCount { get; set; }

        /// <summary>
        /// 总访问量
        /// </summary>
        public long TotalViews { get; set; }

        /// <summary>
        /// 总关注者数
        /// </summary>
        public int TotalFollowers { get; set; }
    }

    /// <summary>
    /// 更新用户信息DTO
    /// </summary>
    public class UpdateUserDto
    {
        /// <summary>
        /// 显示名称
        /// </summary>
        public string? DisplayName { get; set; }

        /// <summary>
        /// 头像URL
        /// </summary>
        public string? AvatarUrl { get; set; }

        /// <summary>
        /// 个人简介
        /// </summary>
        public string? Bio { get; set; }

        /// <summary>
        /// 网站URL
        /// </summary>
        public string? WebsiteUrl { get; set; }

        /// <summary>
        /// 位置
        /// </summary>
        public string? Location { get; set; }
    }
}
