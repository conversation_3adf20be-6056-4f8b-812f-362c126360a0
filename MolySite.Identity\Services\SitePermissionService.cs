using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MolySite.Identity.Authorization;
using MolySite.Identity.Data;
using MolySite.Identity.Models;

namespace MolySite.Identity.Services
{
    /// <summary>
    /// 网站权限服务实现
    /// </summary>
    public class SitePermissionService : ISitePermissionService
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<SitePermissionService> _logger;

        public SitePermissionService(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            ILogger<SitePermissionService> logger)
        {
            _context = context;
            _userManager = userManager;
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<bool> HasSitePermissionAsync(Guid userId, Guid siteId, string permission)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.SiteRoles)
                    .FirstOrDefaultAsync(u => u.Id == userId);

                if (user == null)
                {
                    _logger.LogWarning("用户不存在: {UserId}", userId);
                    return false;
                }

                // SuperAdmin拥有所有权限
                if (user.PlatformRole == PlatformRole.SuperAdmin)
                {
                    return true;
                }

                // 获取用户在网站中的角色
                var siteRole = user.SiteRoles.FirstOrDefault(sr => sr.SiteId == siteId && sr.IsActive);
                if (siteRole == null)
                {
                    return false;
                }

                // 根据角色检查权限
                var allowedPermissions = GetPermissionsForRole(siteRole.Role);
                return allowedPermissions.Contains(permission);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查网站权限时发生错误: UserId={UserId}, SiteId={SiteId}, Permission={Permission}", 
                    userId, siteId, permission);
                return false;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> HasPlatformPermissionAsync(Guid userId, string permission)
        {
            try
            {
                var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
                if (user == null)
                {
                    return false;
                }

                var allowedPermissions = GetPlatformPermissionsForRole(user.PlatformRole);
                return allowedPermissions.Contains(permission);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查平台权限时发生错误: UserId={UserId}, Permission={Permission}", 
                    userId, permission);
                return false;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> HasSitePermissionAsync(ClaimsPrincipal user, Guid siteId, string permission)
        {
            var userIdClaim = user.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
            {
                return false;
            }

            return await HasSitePermissionAsync(userId, siteId, permission);
        }

        /// <inheritdoc/>
        public async Task<SiteRoleType?> GetUserRoleInSiteAsync(Guid userId, Guid siteId)
        {
            try
            {
                var siteRole = await _context.SiteUserRoles
                    .FirstOrDefaultAsync(sr => sr.UserId == userId && sr.SiteId == siteId && sr.IsActive);

                return siteRole?.Role;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户网站角色时发生错误: UserId={UserId}, SiteId={SiteId}", userId, siteId);
                return null;
            }
        }

        /// <inheritdoc/>
        public async Task<List<Site>> GetUserOwnedSitesAsync(Guid userId)
        {
            try
            {
                return await _context.Sites
                    .Where(s => s.OwnerId == userId && s.IsActive)
                    .OrderByDescending(s => s.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户拥有的网站时发生错误: UserId={UserId}", userId);
                return new List<Site>();
            }
        }

        /// <inheritdoc/>
        public async Task<List<Site>> GetUserEditableSitesAsync(Guid userId)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.SiteRoles)
                    .ThenInclude(sr => sr.Site)
                    .FirstOrDefaultAsync(u => u.Id == userId);

                if (user == null)
                {
                    return new List<Site>();
                }

                // SuperAdmin可以编辑所有网站
                if (user.PlatformRole == PlatformRole.SuperAdmin)
                {
                    return await _context.Sites
                        .Where(s => s.IsActive)
                        .OrderByDescending(s => s.CreatedAt)
                        .ToListAsync();
                }

                // 获取用户可以编辑的网站（Owner或Editor）
                var editableSites = user.SiteRoles
                    .Where(sr => sr.IsActive && sr.Site.IsActive)
                    .Select(sr => sr.Site)
                    .OrderByDescending(s => s.CreatedAt)
                    .ToList();

                return editableSites;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户可编辑的网站时发生错误: UserId={UserId}", userId);
                return new List<Site>();
            }
        }

        /// <inheritdoc/>
        public async Task<List<Site>> GetUserAccessibleSitesAsync(Guid userId)
        {
            // 对于当前的三层架构，可编辑的网站就是可访问的网站
            return await GetUserEditableSitesAsync(userId);
        }

        /// <inheritdoc/>
        public async Task<bool> CanUserAccessSiteAsync(Guid userId, Guid siteId)
        {
            try
            {
                var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
                if (user == null)
                {
                    return false;
                }

                // SuperAdmin可以访问所有网站
                if (user.PlatformRole == PlatformRole.SuperAdmin)
                {
                    return true;
                }

                // 检查用户是否在网站中有角色
                return await _context.SiteUserRoles
                    .AnyAsync(sr => sr.UserId == userId && sr.SiteId == siteId && sr.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查用户网站访问权限时发生错误: UserId={UserId}, SiteId={SiteId}", userId, siteId);
                return false;
            }
        }

        /// <inheritdoc/>
        public async Task<List<string>> GetUserSitePermissionsAsync(Guid userId, Guid siteId)
        {
            try
            {
                var role = await GetUserRoleInSiteAsync(userId, siteId);
                if (role == null)
                {
                    return new List<string>();
                }

                return GetPermissionsForRole(role.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户网站权限时发生错误: UserId={UserId}, SiteId={SiteId}", userId, siteId);
                return new List<string>();
            }
        }

        /// <inheritdoc/>
        public async Task<List<string>> GetUserPlatformPermissionsAsync(Guid userId)
        {
            try
            {
                var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
                if (user == null)
                {
                    return new List<string>();
                }

                return GetPlatformPermissionsForRole(user.PlatformRole);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户平台权限时发生错误: UserId={UserId}", userId);
                return new List<string>();
            }
        }

        /// <inheritdoc/>
        public async Task<bool> AssignSiteRoleAsync(Guid userId, Guid siteId, SiteRoleType role, Guid grantedBy)
        {
            try
            {
                // 检查是否已存在角色
                var existingRole = await _context.SiteUserRoles
                    .FirstOrDefaultAsync(sr => sr.UserId == userId && sr.SiteId == siteId);

                if (existingRole != null)
                {
                    // 更新现有角色
                    existingRole.Role = role;
                    existingRole.IsActive = true;
                    existingRole.GrantedBy = grantedBy;
                    existingRole.GrantedAt = DateTime.UtcNow;
                }
                else
                {
                    // 创建新角色
                    var siteUserRole = new SiteUserRole
                    {
                        Id = Guid.NewGuid(),
                        UserId = userId,
                        SiteId = siteId,
                        Role = role,
                        GrantedBy = grantedBy,
                        GrantedAt = DateTime.UtcNow,
                        IsActive = true,

                    };

                    _context.SiteUserRoles.Add(siteUserRole);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分配网站角色时发生错误: UserId={UserId}, SiteId={SiteId}, Role={Role}", 
                    userId, siteId, role);
                return false;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> RemoveSiteRoleAsync(Guid userId, Guid siteId)
        {
            try
            {
                var siteRole = await _context.SiteUserRoles
                    .FirstOrDefaultAsync(sr => sr.UserId == userId && sr.SiteId == siteId);

                if (siteRole != null)
                {
                    siteRole.IsActive = false;
                    await _context.SaveChangesAsync();
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移除网站角色时发生错误: UserId={UserId}, SiteId={SiteId}", userId, siteId);
                return false;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> IsSuperAdminAsync(Guid userId)
        {
            try
            {
                var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
                return user?.PlatformRole == PlatformRole.SuperAdmin;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查SuperAdmin权限时发生错误: UserId={UserId}", userId);
                return false;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> IsSiteOwnerAsync(Guid userId, Guid siteId)
        {
            try
            {
                var role = await GetUserRoleInSiteAsync(userId, siteId);
                return role == SiteRoleType.Owner;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查网站所有者权限时发生错误: UserId={UserId}, SiteId={SiteId}", userId, siteId);
                return false;
            }
        }

        /// <summary>
        /// 根据网站角色获取权限列表
        /// </summary>
        private List<string> GetPermissionsForRole(SiteRoleType role)
        {
            return role switch
            {
                SiteRoleType.Owner => SitePermissions.GetSiteOwnerSitePermissions(),
                SiteRoleType.Editor => SitePermissions.GetSiteEditorPermissions(),
                SiteRoleType.Follower => SitePermissions.GetSiteFollowerPermissions(),
                _ => new List<string>()
            };
        }

        /// <summary>
        /// 根据平台角色获取权限列表
        /// </summary>
        private List<string> GetPlatformPermissionsForRole(PlatformRole role)
        {
            return role switch
            {
                PlatformRole.SuperAdmin => SitePermissions.GetSuperAdminPermissions(),
                PlatformRole.User => SitePermissions.GetUserPermissions(),
                _ => new List<string>()
            };
        }
    }
}
