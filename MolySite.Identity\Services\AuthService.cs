using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using MolySite.Identity.Authorization;
using MolySite.Identity.Data;
using MolySite.Identity.Dtos;
using MolySite.Identity.Models;

namespace MolySite.Identity.Services
{
    /// <summary>
    /// 多租户认证服务实现
    /// </summary>
    public class AuthService : IAuthService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly ApplicationDbContext _context;
        private readonly ITokenService _tokenService;
        private readonly ILogger<AuthService> _logger;

        public AuthService(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            ApplicationDbContext context,
            ITokenService tokenService,
            ILogger<AuthService> logger)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _context = context;
            _tokenService = tokenService;
            _logger = logger;
        }

        /// <summary>
        /// 用户注册（新架构：默认为SiteOwner角色）
        /// </summary>
        public async Task<AuthResult> RegisterAsync(RegisterDto registerDto)
        {
            try
            {
                _logger.LogInformation("处理注册请求: {UserName}, {Email}",
                    registerDto.UserName, registerDto.Email);

                // 检查用户是否已存在
                var existingUser = await _userManager.FindByEmailAsync(registerDto.Email);
                if (existingUser != null)
                {
                    return new AuthResult
                    {
                        Success = false,
                        ErrorMessage = "用户已存在"
                    };
                }

                // 创建用户（新架构：默认为User角色）
                var user = new ApplicationUser
                {
                    UserName = registerDto.UserName,
                    Email = registerDto.Email,
                    IsActive = true,
                    PlatformRole = PlatformRole.User, // 默认平台角色
                    PlatformRoles = new List<string> { "User" },
                    Permissions = SitePermissions.GetUserPermissions(), // 默认权限
                    CreatedAt = DateTime.UtcNow,
                    DisplayName = registerDto.UserName,
                    TimeZone = "UTC",
                    Language = "zh-CN",
                    EmailNotifications = true
                };

                // 尝试创建用户
                var result = await _userManager.CreateAsync(user, registerDto.Password);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    _logger.LogWarning("用户创建失败: {Errors}", errors);

                    return new AuthResult
                    {
                        Success = false,
                        ErrorMessage = errors
                    };
                }

                // 添加默认角色到ASP.NET Core Identity
                await _userManager.AddToRoleAsync(user, "User");

                _logger.LogInformation("用户注册成功: {UserId}, {UserName}, 角色: User",
                    user.Id, user.UserName);

                return new AuthResult
                {
                    Success = true,
                    UserId = user.Id,
                    Message = "注册成功，您现在可以创建自己的网站了！"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "注册过程中发生异常: {UserName}, {Email}",
                    registerDto.UserName, registerDto.Email);

                return new AuthResult
                {
                    Success = false,
                    ErrorMessage = $"注册过程中发生错误: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        public async Task<AuthResult> LoginAsync(LoginDto loginDto)
        {
            // 查找用户（支持用户名或邮箱登录）
            var user = await _userManager.FindByNameAsync(loginDto.UserNameOrEmail) ??
                       await _userManager.FindByEmailAsync(loginDto.UserNameOrEmail);

            if (user == null)
            {
                return new AuthResult
                {
                    Success = false,
                    ErrorMessage = "用户不存在"
                };
            }

            // 检查密码
            var signInResult = await _signInManager.CheckPasswordSignInAsync(user, loginDto.Password, false);
            if (!signInResult.Succeeded)
            {
                return new AuthResult
                {
                    Success = false,
                    ErrorMessage = "密码错误"
                };
            }

            // 获取用户角色
            var roles = await _userManager.GetRolesAsync(user);

            // 验证SuperAdmin角色的有效性
            if (roles.Contains("SuperAdmin"))
            {
                // 额外验证SuperAdmin用户的有效性
                var isSuperAdminValid = ValidateSuperAdminAccess(user);
                if (!isSuperAdminValid)
                {
                    _logger.LogWarning("SuperAdmin角色验证失败，用户: {UserId}", user.Id);
                    roles = roles.Where(r => r != "SuperAdmin").ToList();
                }
            }

            // 确保用户的 PlatformRoles 属性也包含这些角色
            user.PlatformRoles = roles.ToList();
            await _userManager.UpdateAsync(user);

            // 生成令牌
            var accessToken = _tokenService.GenerateAccessToken(user);
            var refreshToken = _tokenService.GenerateRefreshToken();

            // 更新用户最后登录时间
            user.LastLoginAt = DateTime.UtcNow;
            await _userManager.UpdateAsync(user);

            return new AuthResult
            {
                Success = true,
                UserId = user.Id,
                LoginResponse = new LoginResponseDto
                {
                    Token = accessToken,
                    RefreshToken = refreshToken,
                    UserId = user.Id,
                    UserName = user.UserName,
                    Email = user.Email ?? string.Empty,
                    Roles = roles.ToList() // 使用从 UserManager 获取的角色
                }
            };
        }

        /// <summary>
        /// 刷新令牌
        /// </summary>
        public async Task<AuthResult> RefreshTokenAsync(string refreshToken)
        {
            // 验证刷新令牌的逻辑（这里是简化版本）
            try
            {
                // 在实际应用中，你应该将刷新令牌存储在数据库中并进行验证
                var principal = _tokenService.GetPrincipalFromToken(refreshToken);
                var userId = principal.FindFirstValue(ClaimTypes.NameIdentifier);

                if (string.IsNullOrEmpty(userId))
                {
                    return new AuthResult
                    {
                        Success = false,
                        ErrorMessage = "无效的刷新令牌"
                    };
                }

                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    return new AuthResult
                    {
                        Success = false,
                        ErrorMessage = "用户不存在"
                    };
                }

                // 生成新的访问令牌
                var newAccessToken = _tokenService.GenerateAccessToken(user);
                var newRefreshToken = _tokenService.GenerateRefreshToken();
                
                // 获取用户角色
                var roles = await _userManager.GetRolesAsync(user);

                // 验证SuperAdmin角色的有效性
                if (roles.Contains("SuperAdmin"))
                {
                    var isSuperAdminValid = ValidateSuperAdminAccess(user);
                    if (!isSuperAdminValid)
                    {
                        _logger.LogWarning("刷新令牌时SuperAdmin角色验证失败，用户: {UserId}", user.Id);
                        roles = roles.Where(r => r != "SuperAdmin").ToList();
                    }
                }

                return new AuthResult
                {
                    Success = true,
                    LoginResponse = new LoginResponseDto
                    {
                        Token = newAccessToken,
                        RefreshToken = newRefreshToken,
                        UserId = user.Id,
                        UserName = user.UserName,
                        Email = user.Email ?? string.Empty,
                        Roles = roles.ToList() // 添加用户角色信息
                    }
                };
            }
            catch
            {
                return new AuthResult
                {
                    Success = false,
                    ErrorMessage = "令牌刷新失败"
                };
            }
        }

        /// <summary>
        /// 验证令牌
        /// </summary>
        public async Task<bool> ValidateTokenAsync(string token)
        {
            try
            {
                var principal = _tokenService.GetPrincipalFromToken(token);
                var userId = principal.FindFirstValue(ClaimTypes.NameIdentifier);

                if (string.IsNullOrEmpty(userId))
                {
                    return false;
                }

                var user = await _userManager.FindByIdAsync(userId);
                return user != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 注销
        /// </summary>
        public async Task LogoutAsync(Guid userId)
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user != null)
            {
                // 可以在这里添加注销逻辑，如使令牌失效
                await _signInManager.SignOutAsync();
            }
        }

        /// <summary>
        /// 验证SuperAdmin访问权限
        /// </summary>
        private bool ValidateSuperAdminAccess(ApplicationUser user)
        {
            try
            {
                // 验证用户是否活跃
                if (!user.IsActive)
                {
                    _logger.LogWarning("SuperAdmin用户未激活: {UserId}", user.Id);
                    return false;
                }

                // 新架构：SuperAdmin不再依赖租户，直接验证平台角色
                if (user.PlatformRole != PlatformRole.SuperAdmin)
                {
                    _logger.LogWarning("用户不是SuperAdmin角色: {UserId}, 角色: {PlatformRole}",
                        user.Id, user.PlatformRole);
                    return false;
                }

                // 验证用户权限
                if (user.Permissions == null || !user.Permissions.Contains("System.FullAccess"))
                {
                    _logger.LogWarning("SuperAdmin用户缺少必要权限: {UserId}", user.Id);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证SuperAdmin访问权限时发生异常: {UserId}", user.Id);
                return false;
            }
        }


    }
} 