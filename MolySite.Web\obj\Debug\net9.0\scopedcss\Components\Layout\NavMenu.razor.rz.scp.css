/* NavMenu 样式 - 开发导航菜单 */

.nav-menu[b-rkm042n77k] {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: white;
    border-right: 1px solid #e2e8f0;
    width: 250px;
}

.nav-header[b-rkm042n77k] {
    padding: 1rem;
    border-bottom: 1px solid #f1f5f9;
}

.nav-brand[b-rkm042n77k] {
    display: flex;
    align-items: center;
    font-size: 1.125rem;
    font-weight: 600;
}

.nav-items[b-rkm042n77k] {
    flex: 1;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    overflow-y: auto;
}

.nav-section[b-rkm042n77k] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.nav-section-title[b-rkm042n77k] {
    font-size: 0.75rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.nav-links[b-rkm042n77k] {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.nav-link[b-rkm042n77k] {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #4b5563;
    border-radius: 0.5rem;
    transition: all 0.2s;
    text-decoration: none;
}

.nav-link:hover[b-rkm042n77k] {
    background: #f9fafb;
    color: #111827;
}

.nav-link.active[b-rkm042n77k] {
    background: #eff6ff;
    color: #1d4ed8;
    border-right: 2px solid #2563eb;
}

.nav-info[b-rkm042n77k] {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-item[b-rkm042n77k] {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
}

.info-label[b-rkm042n77k] {
    color: #6b7280;
}

.info-value[b-rkm042n77k] {
    color: #111827;
    font-weight: 500;
}

.nav-footer[b-rkm042n77k] {
    padding: 1rem;
    border-top: 1px solid #f1f5f9;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .nav-menu[b-rkm042n77k] {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        z-index: 50;
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
    }

    .nav-menu.open[b-rkm042n77k] {
        transform: translateX(0);
    }
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
    .nav-menu[b-rkm042n77k] {
        background: #111827;
        border-right-color: #374151;
    }

    .nav-brand[b-rkm042n77k] {
        color: white;
    }

    .nav-section-title[b-rkm042n77k] {
        color: #9ca3af;
    }

    .nav-link[b-rkm042n77k] {
        color: #d1d5db;
    }

    .nav-link:hover[b-rkm042n77k] {
        background: #1f2937;
        color: white;
    }

    .nav-link.active[b-rkm042n77k] {
        background: #1e3a8a;
        color: #93c5fd;
        border-right-color: #3b82f6;
    }

    .info-label[b-rkm042n77k] {
        color: #9ca3af;
    }

    .info-value[b-rkm042n77k] {
        color: #e5e7eb;
    }
}

/* 滚动条样式 */
.nav-items[b-rkm042n77k]::-webkit-scrollbar {
    width: 4px;
}

.nav-items[b-rkm042n77k]::-webkit-scrollbar-track {
    background: transparent;
}

.nav-items[b-rkm042n77k]::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 2px;
}

.nav-items[b-rkm042n77k]::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* 焦点样式 */
.nav-link:focus[b-rkm042n77k] {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* 动画效果 */
.nav-link[b-rkm042n77k] {
    position: relative;
    overflow: hidden;
}

.nav-link[b-rkm042n77k]::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.nav-link:hover[b-rkm042n77k]::before {
    left: 100%;
}

/* 图标样式 */
.nav-link i[b-rkm042n77k] {
    margin-right: 0.5rem;
    width: 1rem;
    text-align: center;
}

/* 徽章样式 */
.nav-badge[b-rkm042n77k] {
    background: #ef4444;
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 9999px;
    margin-left: auto;
}

/* 分隔线 */
.nav-divider[b-rkm042n77k] {
    height: 1px;
    background: #e5e7eb;
    margin: 0.5rem 0;
}

/* 工具提示 */
.nav-tooltip[b-rkm042n77k] {
    position: relative;
}

.nav-tooltip[b-rkm042n77k]::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: #1f2937;
    color: white;
    padding: 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s;
    z-index: 1000;
    margin-left: 0.5rem;
}

.nav-tooltip:hover[b-rkm042n77k]::after {
    opacity: 1;
    visibility: visible;
}

/* 加载状态 */
.nav-loading[b-rkm042n77k] {
    opacity: 0.6;
    pointer-events: none;
}

.nav-loading[b-rkm042n77k]::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1rem;
    height: 1rem;
    border: 2px solid #e5e7eb;
    border-top-color: #3b82f6;
    border-radius: 50%;
    animation: spin-b-rkm042n77k 1s linear infinite;
    transform: translate(-50%, -50%);
}

@keyframes spin-b-rkm042n77k {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}
