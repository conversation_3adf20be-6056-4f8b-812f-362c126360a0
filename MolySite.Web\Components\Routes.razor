﻿@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Authorization
@using MolySite.Web.Components.Layout
@using MolySite.Web.Components.Sites
@using MolySite.Web.Services
@using MolySite.Web.Components.Pages
@using MolySite.Web.Configuration
@inject ILogger<Routes> Logger
@inject NavigationManager NavigationManager
@inject IHttpContextAccessor HttpContextAccessor
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<Router AppAssembly="@typeof(Program).Assembly">
    <Found Context="routeData">
        @{
            var httpContext = HttpContextAccessor.HttpContext;
            var isSiteRequest = httpContext?.Items["IsSiteRequest"] as bool? ?? false;

            Logger.LogDebug("🔍 路由处理: IsSiteRequest={IsSiteRequest}, PageType={PageType}",
                isSiteRequest, routeData.PageType.Name);

            // 平台路由变量
            var pageName = routeData.PageType.Name;
            var hasAllowAnonymous = routeData.PageType.GetCustomAttributes(typeof(AllowAnonymousAttribute), false).Any();
            var isPublicPage = PublicPageConfiguration.IsPublicPage(pageName) || hasAllowAnonymous;

            if (!isSiteRequest)
            {
                Logger.LogInformation("🔍 平台路由检查: 页面={PageName}, 是否公共页面={IsPublic}, 有AllowAnonymous={HasAllowAnonymous}",
                    pageName, isPublicPage, hasAllowAnonymous);
            }
        }

        @if (isSiteRequest)
        {
            <!-- 用户网站路由 -->
            <SiteRouteHandler RouteData="@routeData" />
        }
        else
        {

            @if (isPublicPage)
            {
                <!-- 公共页面直接渲染，不进行认证检查 -->
                <RouteView RouteData="@routeData" DefaultLayout="@typeof(PublicLayout)" />
            }
            else
            {
                <!-- 需要认证的页面使用AuthorizeRouteView -->
                <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(PublicLayout)">
                    <NotAuthorized>
                        @if (context.User.Identity?.IsAuthenticated != true)
                        {
                            <RedirectToLogin ReturnUrl="@NavigationManager.ToBaseRelativePath(NavigationManager.Uri)" />
                        }
                        else
                        {
                            <AccessDenied />
                        }
                    </NotAuthorized>
                    <Authorizing>
                        <MolySite.Web.Components.Shared.AuthorizingComponent />
                    </Authorizing>
                </AuthorizeRouteView>
            }
        }

        <FocusOnNavigate RouteData="@routeData" Selector="h1" />
    </Found>
    <NotFound>
        @{
            var httpContext = HttpContextAccessor.HttpContext;
            var isSiteRequest = httpContext?.Items["IsSiteRequest"] as bool? ?? false;
        }

        @if (isSiteRequest)
        {
            <!-- 用户网站404页面 -->
            <SiteNotFound />
        }
        else
        {
            <!-- 平台404页面 (现有) -->
            <PageTitle>Not found</PageTitle>
            <LayoutView Layout="@typeof(PublicLayout)">
                <div class="container-responsive mt-12">
                    <div class="bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-lg">
                        <h3 class="text-xl font-semibold mb-2">404 - 页面未找到</h3>
                        <p class="mb-4">抱歉，您请求的页面不存在。</p>
                        <a href="/" class="btn-primary">返回首页</a>
                    </div>
                </div>
            </LayoutView>
        }
    </NotFound>
</Router>

@code {
    protected override void OnInitialized()
    {
        Logger.LogInformation("Routes组件初始化");
    }
}
