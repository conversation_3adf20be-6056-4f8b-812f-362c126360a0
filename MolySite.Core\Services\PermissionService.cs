using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MolySite.Core.Interfaces;
using MolySite.Core.Models;
using MolySite.Core.Constants;
using MolySite.Identity.Data;
using MolySite.Identity.Models;

namespace MolySite.Core.Services
{
    /// <summary>
    /// Core层权限服务实现
    /// </summary>
    public class PermissionService : IPermissionService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<PermissionService> _logger;

        public PermissionService(
            ApplicationDbContext context,
            ILogger<PermissionService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<Result<bool>> HasPlatformPermissionAsync(Guid userId, string permission)
        {
            try
            {
                var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
                if (user == null)
                {
                    return Result<bool>.Failure("用户不存在", "USER_NOT_FOUND");
                }

                // SuperAdmin拥有所有权限
                if (user.PlatformRole == PlatformRole.SuperAdmin)
                {
                    return Result<bool>.Success(true);
                }

                // 检查用户权限列表
                var userPermissions = user.Permissions ?? new List<string>();
                var hasPermission = userPermissions.Contains(permission);

                return Result<bool>.Success(hasPermission);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查平台权限时发生错误: UserId={UserId}, Permission={Permission}", userId, permission);
                return Result<bool>.Failure("检查权限失败", "CHECK_PLATFORM_PERMISSION_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<bool>> HasSitePermissionAsync(Guid userId, Guid siteId, string permission)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.SiteRoles)
                    .FirstOrDefaultAsync(u => u.Id == userId);

                if (user == null)
                {
                    return Result<bool>.Failure("用户不存在", "USER_NOT_FOUND");
                }

                // SuperAdmin只有有限的网站权限（不能访问网站内容和隐私信息）
                if (user.PlatformRole == PlatformRole.SuperAdmin)
                {
                    var allowedSuperAdminSitePermissions = GetSuperAdminSitePermissions();
                    return Result<bool>.Success(allowedSuperAdminSitePermissions.Contains(permission));
                }

                // 获取用户在网站中的角色
                var siteRole = user.SiteRoles.FirstOrDefault(sr => sr.SiteId == siteId && sr.IsActive);
                if (siteRole == null)
                {
                    return Result<bool>.Success(false);
                }

                // 根据角色检查权限
                var allowedPermissions = GetPermissionsForRole(siteRole.Role);
                var hasPermission = allowedPermissions.Contains(permission);

                return Result<bool>.Success(hasPermission);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查网站权限时发生错误: UserId={UserId}, SiteId={SiteId}, Permission={Permission}", 
                    userId, siteId, permission);
                return Result<bool>.Failure("检查权限失败", "CHECK_SITE_PERMISSION_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<List<string>>> GetUserPlatformPermissionsAsync(Guid userId)
        {
            try
            {
                var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
                if (user == null)
                {
                    return Result<List<string>>.Failure("用户不存在", "USER_NOT_FOUND");
                }

                // SuperAdmin拥有所有权限
                if (user.PlatformRole == PlatformRole.SuperAdmin)
                {
                    return Result<List<string>>.Success(PlatformPermissions.GetAllPermissions());
                }

                var permissions = user.Permissions ?? new List<string>();
                return Result<List<string>>.Success(permissions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户平台权限时发生错误: UserId={UserId}", userId);
                return Result<List<string>>.Failure("获取权限失败", "GET_PLATFORM_PERMISSIONS_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<List<string>>> GetUserSitePermissionsAsync(Guid userId, Guid siteId)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.SiteRoles)
                    .FirstOrDefaultAsync(u => u.Id == userId);

                if (user == null)
                {
                    return Result<List<string>>.Failure("用户不存在", "USER_NOT_FOUND");
                }

                // SuperAdmin拥有所有权限
                if (user.PlatformRole == PlatformRole.SuperAdmin)
                {
                    return Result<List<string>>.Success(SitePermissions.GetAllPermissions());
                }

                // 获取用户在网站中的角色
                var siteRole = user.SiteRoles.FirstOrDefault(sr => sr.SiteId == siteId && sr.IsActive);
                if (siteRole == null)
                {
                    return Result<List<string>>.Success(new List<string>());
                }

                var permissions = GetPermissionsForRole(siteRole.Role);
                return Result<List<string>>.Success(permissions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户网站权限时发生错误: UserId={UserId}, SiteId={SiteId}", userId, siteId);
                return Result<List<string>>.Failure("获取权限失败", "GET_SITE_PERMISSIONS_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<bool>> CanUserAccessSiteAsync(Guid userId, Guid siteId)
        {
            try
            {
                var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
                if (user == null)
                {
                    return Result<bool>.Failure("用户不存在", "USER_NOT_FOUND");
                }

                // SuperAdmin可以访问所有网站
                if (user.PlatformRole == PlatformRole.SuperAdmin)
                {
                    return Result<bool>.Success(true);
                }

                // 检查用户是否在网站中有角色
                var hasRole = await _context.SiteUserRoles
                    .AnyAsync(sr => sr.UserId == userId && sr.SiteId == siteId && sr.IsActive);

                return Result<bool>.Success(hasRole);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查用户网站访问权限时发生错误: UserId={UserId}, SiteId={SiteId}", userId, siteId);
                return Result<bool>.Failure("检查访问权限失败", "CHECK_SITE_ACCESS_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<List<Guid>>> GetUserAccessibleSiteIdsAsync(Guid userId)
        {
            try
            {
                var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
                if (user == null)
                {
                    return Result<List<Guid>>.Failure("用户不存在", "USER_NOT_FOUND");
                }

                // SuperAdmin可以访问所有网站
                if (user.PlatformRole == PlatformRole.SuperAdmin)
                {
                    var allSiteIds = await _context.Sites
                        .Where(s => s.IsActive)
                        .Select(s => s.Id)
                        .ToListAsync();
                    return Result<List<Guid>>.Success(allSiteIds);
                }

                // 获取用户有角色的网站
                var accessibleSiteIds = await _context.SiteUserRoles
                    .Where(sr => sr.UserId == userId && sr.IsActive)
                    .Select(sr => sr.SiteId)
                    .ToListAsync();

                return Result<List<Guid>>.Success(accessibleSiteIds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户可访问网站ID列表时发生错误: UserId={UserId}", userId);
                return Result<List<Guid>>.Failure("获取可访问网站失败", "GET_ACCESSIBLE_SITES_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<List<Guid>>> GetUserEditableSiteIdsAsync(Guid userId)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.SiteRoles)
                    .FirstOrDefaultAsync(u => u.Id == userId);

                if (user == null)
                {
                    return Result<List<Guid>>.Failure("用户不存在", "USER_NOT_FOUND");
                }

                // SuperAdmin不能编辑网站内容，只能进行平台管理
                if (user.PlatformRole == PlatformRole.SuperAdmin)
                {
                    // SuperAdmin不能编辑任何网站的内容
                    return Result<List<Guid>>.Success(new List<Guid>());
                }

                // 获取用户可以编辑的网站（Owner或Editor）
                var editableSiteIds = user.SiteRoles
                    .Where(sr => sr.IsActive && (sr.Role == SiteRoleType.Owner || sr.Role == SiteRoleType.Editor))
                    .Select(sr => sr.SiteId)
                    .ToList();

                return Result<List<Guid>>.Success(editableSiteIds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户可编辑网站ID列表时发生错误: UserId={UserId}", userId);
                return Result<List<Guid>>.Failure("获取可编辑网站失败", "GET_EDITABLE_SITES_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result> AssignSiteRoleAsync(Guid userId, Guid siteId, string role, Guid grantedBy)
        {
            try
            {
                // 检查授权者是否有权限分配角色
                var hasPermission = await HasSitePermissionAsync(grantedBy, siteId, SitePermissions.ManageUsers);
                if (!hasPermission.IsSuccess || !hasPermission.Data)
                {
                    return Result.Failure("您没有权限分配角色", "INSUFFICIENT_PERMISSIONS");
                }

                // 检查用户是否已有角色
                var existingRole = await _context.SiteUserRoles
                    .FirstOrDefaultAsync(sr => sr.UserId == userId && sr.SiteId == siteId);

                if (existingRole != null)
                {
                    // 更新现有角色
                    existingRole.Role = Enum.Parse<SiteRoleType>(role);
                    existingRole.IsActive = true;
                    existingRole.GrantedAt = DateTime.UtcNow;
                    existingRole.GrantedBy = grantedBy;
                }
                else
                {
                    // 创建新角色
                    var newRole = new SiteUserRole
                    {
                        Id = Guid.NewGuid(),
                        UserId = userId,
                        SiteId = siteId,
                        Role = Enum.Parse<SiteRoleType>(role),
                        GrantedAt = DateTime.UtcNow,
                        GrantedBy = grantedBy,
                        IsActive = true,
                        InvitationStatus = InvitationStatus.Accepted
                    };

                    _context.SiteUserRoles.Add(newRole);
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("网站角色分配成功: UserId={UserId}, SiteId={SiteId}, Role={Role}", userId, siteId, role);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分配网站角色时发生错误: UserId={UserId}, SiteId={SiteId}, Role={Role}", userId, siteId, role);
                return Result.Failure("分配角色失败", "ASSIGN_SITE_ROLE_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result> RemoveSiteRoleAsync(Guid userId, Guid siteId, Guid removedBy)
        {
            try
            {
                // 检查移除者是否有权限移除角色
                var hasPermission = await HasSitePermissionAsync(removedBy, siteId, SitePermissions.ManageUsers);
                if (!hasPermission.IsSuccess || !hasPermission.Data)
                {
                    return Result.Failure("您没有权限移除角色", "INSUFFICIENT_PERMISSIONS");
                }

                var siteRole = await _context.SiteUserRoles
                    .FirstOrDefaultAsync(sr => sr.UserId == userId && sr.SiteId == siteId && sr.IsActive);

                if (siteRole == null)
                {
                    return Result.Failure("用户在此网站中没有角色", "ROLE_NOT_FOUND");
                }

                // 不能移除网站所有者的角色
                if (siteRole.Role == SiteRoleType.Owner)
                {
                    return Result.Failure("不能移除网站所有者的角色", "CANNOT_REMOVE_OWNER_ROLE");
                }

                siteRole.IsActive = false;
                await _context.SaveChangesAsync();

                _logger.LogInformation("网站角色移除成功: UserId={UserId}, SiteId={SiteId}", userId, siteId);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移除网站角色时发生错误: UserId={UserId}, SiteId={SiteId}", userId, siteId);
                return Result.Failure("移除角色失败", "REMOVE_SITE_ROLE_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<List<SiteUserRoleDto>>> GetSiteUserRolesAsync(Guid siteId, Guid requestUserId)
        {
            try
            {
                // 检查请求者是否有权限查看网站用户
                var hasPermission = await HasSitePermissionAsync(requestUserId, siteId, SitePermissions.ViewSettings);
                if (!hasPermission.IsSuccess || !hasPermission.Data)
                {
                    return Result<List<SiteUserRoleDto>>.Failure("您没有权限查看网站用户", "INSUFFICIENT_PERMISSIONS");
                }

                var siteUserRoles = await _context.SiteUserRoles
                    .Include(sur => sur.User)
                    .Where(sur => sur.SiteId == siteId && sur.IsActive)
                    .Select(sur => new SiteUserRoleDto
                    {
                        UserId = sur.UserId,
                        UserName = sur.User.UserName!,
                        Email = sur.User.Email!,
                        Role = sur.Role.ToString(),
                        IsActive = sur.IsActive,
                        GrantedAt = sur.GrantedAt,
                        GrantedBy = sur.GrantedBy,
                        InvitationStatus = sur.InvitationStatus.ToString()
                    })
                    .ToListAsync();

                return Result<List<SiteUserRoleDto>>.Success(siteUserRoles);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取网站用户角色列表时发生错误: SiteId={SiteId}", siteId);
                return Result<List<SiteUserRoleDto>>.Failure("获取用户角色列表失败", "GET_SITE_USER_ROLES_ERROR");
            }
        }

        /// <summary>
        /// 根据角色获取权限列表
        /// </summary>
        private static List<string> GetPermissionsForRole(SiteRoleType role)
        {
            return role switch
            {
                SiteRoleType.Owner => SitePermissions.GetOwnerPermissions(),
                SiteRoleType.Editor => SitePermissions.GetEditorPermissions(),
                SiteRoleType.Follower => SitePermissions.GetFollowerPermissions(),
                _ => new List<string>()
            };
        }

        /// <summary>
        /// 获取SuperAdmin在网站中的有限权限（不包含内容访问和隐私信息）
        /// </summary>
        private static List<string> GetSuperAdminSitePermissions()
        {
            return new List<string>
            {
                // SuperAdmin只能进行网站管理操作，不能访问网站内容
                SitePermissions.ViewSettings,  // 查看基本设置（非隐私）
                SitePermissions.ManageSubscription,  // 管理订阅状态
                // 注意：不包含 EditSettings, ViewContent, EditContent, Publish 等涉及隐私的权限
            };
        }
    }
}
