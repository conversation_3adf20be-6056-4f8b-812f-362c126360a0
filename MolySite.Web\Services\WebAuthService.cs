using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MolySite.Shared.Dtos;
using MolySite.Web.Dtos;
using MolySite.Web.Models;
using SharedLoginResponseDto = MolySite.Shared.Dtos.LoginResponseDto;

namespace MolySite.Web.Services
{
    /// <summary>
    /// Web层认证服务适配器，将Core层服务适配为Web层接口
    /// </summary>
    public class WebAuthService : IAuthService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<WebAuthService> _logger;
        private readonly ILocalStorageService _localStorage;
        private readonly AuthenticationStateProvider _authStateProvider;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IJSInteropService _jsInteropService;

        public WebAuthService(
            HttpClient httpClient,
            ILogger<WebAuthService> logger,
            ILocalStorageService localStorage,
            AuthenticationStateProvider authStateProvider,
            IHttpContextAccessor httpContextAccessor,
            IJSInteropService jsInteropService)
        {
            _httpClient = httpClient;
            _logger = logger;
            _localStorage = localStorage;
            _authStateProvider = authStateProvider;
            _httpContextAccessor = httpContextAccessor;
            _jsInteropService = jsInteropService;
        }

        /// <inheritdoc/>
        public async Task<AuthResult> RegisterAsync(MolySite.Shared.Dtos.RegisterDto registerDto)
        {
            try
            {
                _logger.LogInformation("发送注册请求: {UserName}, {Email}", registerDto.UserName, registerDto.Email);

                var coreRegisterDto = new MolySite.Shared.Dtos.RegisterDto
                {
                    UserName = registerDto.UserName,
                    Email = registerDto.Email,
                    Password = registerDto.Password,
                    ConfirmPassword = registerDto.Password
                };

                var response = await _httpClient.PostAsJsonAsync("api/auth/register", coreRegisterDto);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var apiResponse = JsonSerializer.Deserialize<JsonElement>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (apiResponse.TryGetProperty("success", out var successElement) && successElement.GetBoolean())
                    {
                        // 检查是否有登录响应数据（注册后自动登录）
                        if (apiResponse.TryGetProperty("data", out var dataElement))
                        {
                            try
                            {
                                var loginResponse = JsonSerializer.Deserialize<SharedLoginResponseDto>(dataElement.GetRawText(), new JsonSerializerOptions
                                {
                                    PropertyNameCaseInsensitive = true
                                });

                                if (loginResponse != null)
                                {
                                    // 保存认证令牌（自动登录）
                                    await SaveAuthTokens(loginResponse);
                                    return new AuthResult { Success = true, Message = "注册成功，已自动登录！" };
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, "注册成功但自动登录失败");
                                return new AuthResult { Success = true, Message = "注册成功，请手动登录" };
                            }
                        }

                        return new AuthResult { Success = true, Message = "注册成功" };
                    }
                }

                // 处理错误响应
                var errorMessage = ExtractErrorMessage(responseContent, response.StatusCode);
                return new AuthResult { Success = false, ErrorMessage = errorMessage };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "注册过程中发生异常");
                return new AuthResult { Success = false, ErrorMessage = $"注册过程中发生错误: {ex.Message}" };
            }
        }

        /// <inheritdoc/>
        public async Task<LoginResult> LoginAsync(MolySite.Shared.Dtos.LoginDto loginDto)
        {
            try
            {
                _logger.LogInformation("发送登录请求: {Username}", loginDto.UserNameOrEmail);

                var coreLoginDto = new MolySite.Shared.Dtos.LoginDto
                {
                    UserNameOrEmail = loginDto.UserNameOrEmail,
                    Password = loginDto.Password
                };

                var response = await _httpClient.PostAsJsonAsync("api/auth/login", coreLoginDto);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var apiResponse = JsonSerializer.Deserialize<JsonElement>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (apiResponse.TryGetProperty("success", out var successElement) && successElement.GetBoolean() &&
                        apiResponse.TryGetProperty("data", out var dataElement))
                    {
                        var loginResponse = JsonSerializer.Deserialize<SharedLoginResponseDto>(dataElement.GetRawText(), new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });

                        if (loginResponse != null)
                        {
                            return new LoginResult
                            {
                                Success = true,
                                LoginResponse = loginResponse
                            };
                        }
                    }
                }

                var errorMessage = ExtractErrorMessage(responseContent, response.StatusCode);
                return new LoginResult { Success = false, ErrorMessage = errorMessage };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "登录过程中发生异常");
                return new LoginResult { Success = false, ErrorMessage = $"登录过程中发生异常: {ex.Message}" };
            }
        }

        /// <inheritdoc/>
        public async Task SaveAuthTokens(SharedLoginResponseDto loginResponse)
        {
            try
            {
                _logger.LogInformation("保存认证令牌: {UserId}, {UserName}", loginResponse.UserId, loginResponse.UserName);

                // 设置Cookie认证
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext != null && !httpContext.Response.HasStarted)
                {
                    var claims = new List<Claim>
                    {
                        new Claim(ClaimTypes.NameIdentifier, loginResponse.UserId.ToString()),
                        new Claim(ClaimTypes.Name, loginResponse.UserName),
                        new Claim(ClaimTypes.Email, loginResponse.Email)
                    };

                    foreach (var role in loginResponse.Roles)
                    {
                        claims.Add(new Claim(ClaimTypes.Role, role));
                    }

                    var identity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                    var principal = new ClaimsPrincipal(identity);

                    var authProperties = new AuthenticationProperties
                    {
                        IsPersistent = true,
                        ExpiresUtc = DateTimeOffset.UtcNow.AddDays(7)
                    };

                    await httpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, principal, authProperties);
                    httpContext.User = principal;
                }

                // 保存到本地存储
                await _jsInteropService.SetLocalStorage("authToken", loginResponse.Token);
                await _jsInteropService.SetLocalStorage("refreshToken", loginResponse.RefreshToken);
                await _jsInteropService.SetLocalStorage("currentUser", JsonSerializer.Serialize(loginResponse));
                await _jsInteropService.SetLocalStorage("auth_timestamp", DateTime.UtcNow.ToString("o"));

                // 更新认证状态
                if (_authStateProvider is CustomAuthStateProvider customProvider)
                {
                    customProvider.MarkUserAsAuthenticated(loginResponse);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存认证令牌时发生异常");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task LogoutAsync()
        {
            try
            {
                _logger.LogInformation("开始用户注销流程");

                // 清除本地存储
                await _jsInteropService.RemoveLocalStorage("authToken");
                await _jsInteropService.RemoveLocalStorage("refreshToken");
                await _jsInteropService.RemoveLocalStorage("currentUser");
                await _jsInteropService.RemoveLocalStorage("user"); // 也清除旧的user键
                await _jsInteropService.RemoveLocalStorage("auth_timestamp");
                _logger.LogInformation("已清除本地存储");

                // 清除自定义Cookie - 这是关键！
                try
                {
                    await _jsInteropService.EraseCookie("MolySite.AuthUser");
                    await _jsInteropService.EraseCookie("MolySite.AuthRole");
                    _logger.LogInformation("已清除自定义Cookie");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "清除自定义Cookie时发生异常");
                }

                // 清除Cookie认证 - 只在响应未开始时执行
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext != null && !httpContext.Response.HasStarted)
                {
                    try
                    {
                        await httpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
                        _logger.LogInformation("已清除Cookie认证票据");
                    }
                    catch (InvalidOperationException ex) when (ex.Message.Contains("Headers are read-only"))
                    {
                        _logger.LogWarning("无法清除Cookie认证票据，响应已开始: {Message}", ex.Message);
                        // 继续执行，不抛出异常
                    }
                }

                // 更新认证状态
                if (_authStateProvider is CustomAuthStateProvider customProvider)
                {
                    customProvider.MarkUserAsLoggedOut();
                    _logger.LogInformation("已更新认证状态为注销");
                }

                _logger.LogInformation("用户注销流程完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "注销时发生异常");
                // 不重新抛出异常，避免影响用户体验
            }
        }

        /// <inheritdoc/>
        public async Task<SharedLoginResponseDto?> GetCurrentUserAsync()
        {
            try
            {
                var userJson = await _jsInteropService.GetLocalStorage("currentUser");
                if (!string.IsNullOrEmpty(userJson))
                {
                    return JsonSerializer.Deserialize<SharedLoginResponseDto>(userJson, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取当前用户信息时发生异常");
                return null;
            }
        }

        /// <inheritdoc/>
        public async Task<AuthDetails> GetAuthDetails()
        {
            var authState = await _authStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;

            return new AuthDetails
            {
                IsAuthenticated = user.Identity?.IsAuthenticated ?? false,
                UserName = user.Identity?.Name ?? string.Empty,
                Roles = user.Claims.Where(c => c.Type == ClaimTypes.Role).Select(c => c.Value).ToList()
            };
        }

        /// <inheritdoc/>
        public async Task<bool> RefreshTokenAsync()
        {
            try
            {
                var refreshToken = await _jsInteropService.GetLocalStorage("refreshToken");
                if (string.IsNullOrEmpty(refreshToken))
                {
                    return false;
                }

                var response = await _httpClient.PostAsJsonAsync("api/auth/refresh-token", refreshToken);
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var apiResponse = JsonSerializer.Deserialize<JsonElement>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (apiResponse.TryGetProperty("success", out var successElement) && successElement.GetBoolean() &&
                        apiResponse.TryGetProperty("data", out var dataElement))
                    {
                        var loginResponse = JsonSerializer.Deserialize<SharedLoginResponseDto>(dataElement.GetRawText(), new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });

                        if (loginResponse != null)
                        {
                            await SaveAuthTokens(loginResponse);
                            return true;
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新令牌时发生异常");
                return false;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> IsAuthenticatedAsync()
        {
            var authState = await _authStateProvider.GetAuthenticationStateAsync();
            return authState.User.Identity?.IsAuthenticated ?? false;
        }

        /// <inheritdoc/>
        public async Task<bool> IsInRoleAsync(string role)
        {
            var authState = await _authStateProvider.GetAuthenticationStateAsync();
            return authState.User.IsInRole(role);
        }

        /// <summary>
        /// 从响应中提取错误消息
        /// </summary>
        private string ExtractErrorMessage(string responseContent, System.Net.HttpStatusCode statusCode)
        {
            try
            {
                var errorObj = JsonSerializer.Deserialize<JsonElement>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (errorObj.TryGetProperty("message", out var messageElement))
                {
                    return messageElement.GetString() ?? "未知错误";
                }

                if (errorObj.TryGetProperty("errorMessage", out var errorMessageElement))
                {
                    return errorMessageElement.GetString() ?? "未知错误";
                }
            }
            catch
            {
                // 忽略解析错误
            }

            return statusCode switch
            {
                System.Net.HttpStatusCode.BadRequest => "请求参数无效",
                System.Net.HttpStatusCode.Unauthorized => "用户名或密码错误",
                System.Net.HttpStatusCode.Forbidden => "访问被拒绝",
                System.Net.HttpStatusCode.NotFound => "资源不存在",
                System.Net.HttpStatusCode.Conflict => "资源冲突",
                _ => $"请求失败: {statusCode}"
            };
        }
    }
}
