﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MolySite.Identity.Migrations
{
    /// <inheritdoc />
    public partial class SimplifiedRoleSystem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_SiteUserRoles_InvitationToken",
                table: "SiteUserRoles");

            migrationBuilder.DropColumn(
                name: "InvitationExpiresAt",
                table: "SiteUserRoles");

            migrationBuilder.DropColumn(
                name: "InvitationStatus",
                table: "SiteUserRoles");

            migrationBuilder.DropColumn(
                name: "InvitationToken",
                table: "SiteUserRoles");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "InvitationExpiresAt",
                table: "SiteUserRoles",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "InvitationStatus",
                table: "SiteUserRoles",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "InvitationToken",
                table: "SiteUserRoles",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_SiteUserRoles_InvitationToken",
                table: "SiteUserRoles",
                column: "InvitationToken");
        }
    }
}
