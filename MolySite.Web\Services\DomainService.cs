using Microsoft.EntityFrameworkCore;
using MolySite.Identity.Data;
using MolySite.Shared.Dtos;
using System.Text.RegularExpressions;

namespace MolySite.Web.Services
{
    /// <summary>
    /// 域名管理服务实现
    /// </summary>
    public class DomainService : IDomainService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<DomainService> _logger;

        // 保留域名列表
        private static readonly HashSet<string> ReservedDomains = new(StringComparer.OrdinalIgnoreCase)
        {
            "www", "api", "admin", "mail", "ftp", "blog", "shop", "app", "mobile",
            "support", "help", "docs", "cdn", "static", "assets", "images", "files",
            "download", "upload", "secure", "ssl", "vpn", "proxy", "cache", "backup",
            "test", "demo", "staging", "dev", "development", "prod", "production"
        };

        // 禁用词汇列表
        private static readonly HashSet<string> BannedWords = new(StringComparer.OrdinalIgnoreCase)
        {
            "porn", "sex", "adult", "casino", "gambling", "drugs", "violence",
            "hate", "spam", "phishing", "scam", "fraud", "illegal", "piracy"
        };

        public DomainService(ApplicationDbContext context, ILogger<DomainService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<bool> IsDomainAvailableAsync(string domain)
        {
            try
            {
                if (string.IsNullOrEmpty(domain))
                    return false;

                domain = domain.ToLowerInvariant();

                // 检查域名格式
                var validation = ValidateDomain(domain);
                if (!validation.IsValid)
                    return false;

                // 检查保留域名
                if (ReservedDomains.Contains(domain))
                    return false;

                // 检查禁用词汇
                if (BannedWords.Any(word => domain.Contains(word)))
                    return false;

                // 检查数据库中是否已存在
                var exists = await _context.Sites
                    .AnyAsync(s => s.Domain == domain && s.IsActive);

                return !exists;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查域名可用性时发生错误: {Domain}", domain);
                return false;
            }
        }

        public async Task<List<DomainSuggestionDto>> GenerateDomainSuggestionsAsync(string siteName, string? siteType = null, int count = 6)
        {
            try
            {
                var suggestions = new List<DomainSuggestionDto>();
                var cleanName = SanitizeDomainName(siteName);

                if (string.IsNullOrEmpty(cleanName))
                    return suggestions;

                // 1. 直接使用网站名
                await AddSuggestionIfAvailable(suggestions, cleanName, "direct", true);

                // 2. 添加类型前缀
                if (!string.IsNullOrEmpty(siteType))
                {
                    var typePrefix = GetSiteTypePrefix(siteType);
                    if (!string.IsNullOrEmpty(typePrefix))
                    {
                        await AddSuggestionIfAvailable(suggestions, $"{typePrefix}-{cleanName}", "type-prefix");
                        await AddSuggestionIfAvailable(suggestions, $"{cleanName}-{typePrefix}", "type-suffix");
                    }
                }

                // 3. 添加常用后缀
                var commonSuffixes = new[] { "site", "web", "online", "hub", "zone" };
                foreach (var suffix in commonSuffixes.Take(2))
                {
                    await AddSuggestionIfAvailable(suggestions, $"{cleanName}-{suffix}", "common-suffix");
                }

                // 4. 添加数字后缀
                for (int i = 1; i <= 5 && suggestions.Count < count; i++)
                {
                    await AddSuggestionIfAvailable(suggestions, $"{cleanName}{i}", "number-suffix");
                }

                // 5. 添加年份
                var year = DateTime.Now.Year;
                await AddSuggestionIfAvailable(suggestions, $"{cleanName}{year}", "year-suffix");

                // 6. 缩短版本（如果原名太长）
                if (cleanName.Length > 10)
                {
                    var shortName = cleanName.Substring(0, Math.Min(8, cleanName.Length));
                    await AddSuggestionIfAvailable(suggestions, shortName, "shortened");
                }

                return suggestions.Take(count).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成域名建议时发生错误: {SiteName}", siteName);
                return new List<DomainSuggestionDto>();
            }
        }

        public async Task<bool> ReserveDomainAsync(string domain, Guid userId)
        {
            try
            {
                // TODO: 实现域名预留逻辑
                // 在实际实现中，这里应该在domain_reservations表中创建记录
                _logger.LogInformation("预留域名: {Domain} for user: {UserId}", domain, userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "预留域名时发生错误: {Domain}", domain);
                return false;
            }
        }

        public async Task<bool> AllocateDomainAsync(string domain, Guid siteId, Guid userId)
        {
            try
            {
                // TODO: 实现域名分配逻辑
                // 在实际实现中，这里应该在domain_allocations表中创建记录
                _logger.LogInformation("分配域名: {Domain} to site: {SiteId}", domain, siteId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分配域名时发生错误: {Domain}", domain);
                return false;
            }
        }

        public async Task<bool> ReleaseDomainAsync(string domain)
        {
            try
            {
                // TODO: 实现域名释放逻辑
                _logger.LogInformation("释放域名: {Domain}", domain);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放域名时发生错误: {Domain}", domain);
                return false;
            }
        }

        public DomainValidationResult ValidateDomain(string domain)
        {
            if (string.IsNullOrEmpty(domain))
            {
                return new DomainValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "域名不能为空"
                };
            }

            domain = domain.ToLowerInvariant();

            // 长度检查
            if (domain.Length < 3)
            {
                return new DomainValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "域名长度不能少于3个字符"
                };
            }

            if (domain.Length > 63)
            {
                return new DomainValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "域名长度不能超过63个字符"
                };
            }

            // 字符检查
            if (!Regex.IsMatch(domain, @"^[a-z0-9-]+$"))
            {
                return new DomainValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "域名只能包含字母、数字和连字符"
                };
            }

            // 连字符位置检查
            if (domain.StartsWith("-") || domain.EndsWith("-"))
            {
                return new DomainValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "域名不能以连字符开头或结尾"
                };
            }

            // 连续连字符检查
            if (domain.Contains("--"))
            {
                return new DomainValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "域名不能包含连续的连字符"
                };
            }

            return new DomainValidationResult { IsValid = true };
        }

        public int CalculateSeoScore(string domain)
        {
            if (string.IsNullOrEmpty(domain))
                return 0;

            int score = 100;

            // 长度评分
            if (domain.Length > 15) score -= 5;
            if (domain.Length > 25) score -= 10;
            if (domain.Length > 35) score -= 15;

            // 连字符评分
            var hyphens = domain.Count(c => c == '-');
            if (hyphens > 0) score -= 2;
            if (hyphens > 2) score -= hyphens * 3;

            // 数字评分
            var numbers = domain.Count(char.IsDigit);
            if (numbers > 0) score -= 3;
            if (numbers > 2) score -= numbers * 2;

            // 可读性评分
            if (IsReadableDomain(domain)) score += 5;

            // 关键词评分
            if (ContainsCommonKeywords(domain)) score += 3;

            return Math.Max(0, Math.Min(100, score));
        }

        public string SanitizeDomainName(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            // 转换为小写
            var result = input.ToLowerInvariant();

            // 移除特殊字符，只保留字母、数字和空格
            result = Regex.Replace(result, @"[^a-z0-9\s]", "");

            // 将空格替换为连字符
            result = Regex.Replace(result, @"\s+", "-");

            // 移除连续的连字符
            result = Regex.Replace(result, @"-+", "-");

            // 移除开头和结尾的连字符
            result = result.Trim('-');

            // 限制长度
            if (result.Length > 30)
                result = result.Substring(0, 30).TrimEnd('-');

            return result;
        }

        private async Task AddSuggestionIfAvailable(List<DomainSuggestionDto> suggestions, string domain, string type, bool isRecommended = false)
        {
            if (string.IsNullOrEmpty(domain) || suggestions.Any(s => s.Domain == domain))
                return;

            var validation = ValidateDomain(domain);
            if (!validation.IsValid)
                return;

            var isAvailable = await IsDomainAvailableAsync(domain);
            if (isAvailable)
            {
                suggestions.Add(new DomainSuggestionDto
                {
                    Domain = domain,
                    SeoScore = CalculateSeoScore(domain),
                    IsRecommended = isRecommended,
                    SuggestionType = type,
                    IsAvailable = true
                });
            }
        }

        private string GetSiteTypePrefix(string siteType)
        {
            return siteType?.ToLowerInvariant() switch
            {
                "blog" => "blog",
                "portfolio" => "portfolio",
                "business" => "biz",
                "shop" => "shop",
                "personal" => "me",
                "news" => "news",
                "photo" => "photo",
                "music" => "music",
                "art" => "art",
                _ => string.Empty
            };
        }

        private bool IsReadableDomain(string domain)
        {
            // 简单的可读性检查：避免过多的数字和连字符
            var digitRatio = (double)domain.Count(char.IsDigit) / domain.Length;
            var hyphenRatio = (double)domain.Count(c => c == '-') / domain.Length;

            return digitRatio < 0.3 && hyphenRatio < 0.2;
        }

        private bool ContainsCommonKeywords(string domain)
        {
            var keywords = new[] { "blog", "site", "web", "app", "shop", "store", "news", "info", "tech", "art", "photo", "music" };
            return keywords.Any(keyword => domain.Contains(keyword));
        }
    }
}
