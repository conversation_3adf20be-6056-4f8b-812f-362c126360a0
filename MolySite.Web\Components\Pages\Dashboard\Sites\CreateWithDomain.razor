@* 创建站点页面 - 增强版域名分配 *@
@page "/dashboard/sites/create-enhanced"
@using Microsoft.AspNetCore.Authorization
@using MolySite.Shared.Models
@using MolySite.Shared.Dtos
@using MolySite.Web.Services
@using MolySite.Web.Components.Pages.Dashboard.Components
@using System.ComponentModel.DataAnnotations
@using System.Text.RegularExpressions
@inject ISiteService SiteService
@inject NavigationManager NavigationManager
@inject ILogger<CreateWithDomain> Logger
@attribute [Authorize]

<PageTitle>创建站点 - MolySite</PageTitle>

<DashboardLayout UserRole="@_userRole">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-gray-900">创建新站点</h1>
                <button @onclick="GoBack" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">
                    返回
                </button>
            </div>

            @if (!string.IsNullOrEmpty(_errorMessage))
            {
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                    @_errorMessage
                </div>
            }

            @if (!string.IsNullOrEmpty(_successMessage))
            {
                <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4">
                    @_successMessage
                </div>
            }

            <EditForm Model="@_createSiteModel" OnValidSubmit="@HandleValidSubmit">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-red-600 mb-4" />

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- 左侧：基本信息 -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">站点名称 *</label>
                            <InputText @bind-Value="_createSiteModel.Name" @oninput="OnSiteNameChanged" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                      placeholder="我的个人博客" />
                            <ValidationMessage For="@(() => _createSiteModel.Name)" class="text-red-600 text-sm" />
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">站点描述</label>
                            <InputTextArea @bind-Value="_createSiteModel.Description" rows="3" 
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                          placeholder="描述您的网站内容和用途" />
                            <ValidationMessage For="@(() => _createSiteModel.Description)" class="text-red-600 text-sm" />
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">网站类型</label>
                            <InputSelect @bind-Value="_createSiteModel.SiteType" @onchange="OnSiteTypeChanged" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择网站类型</option>
                                <option value="blog">个人博客</option>
                                <option value="portfolio">作品集</option>
                                <option value="business">企业官网</option>
                                <option value="shop">在线商店</option>
                                <option value="personal">个人主页</option>
                                <option value="other">其他</option>
                            </InputSelect>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">主题模板</label>
                            <InputSelect @bind-Value="_createSiteModel.Theme" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择主题</option>
                                <option value="default">默认主题</option>
                                <option value="blog">博客主题</option>
                                <option value="business">商务主题</option>
                                <option value="portfolio">作品集主题</option>
                                <option value="minimal">极简主题</option>
                            </InputSelect>
                        </div>

                        <div class="flex items-center">
                            <InputCheckbox @bind-Value="_createSiteModel.IsPublic" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
                            <label class="ml-2 block text-sm text-gray-700">
                                公开站点（允许搜索引擎索引）
                            </label>
                        </div>
                    </div>

                    <!-- 右侧：域名配置 -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">域名配置</h3>
                        
                        <!-- 域名输入 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">选择您的域名</label>
                            <div class="flex">
                                <InputText @bind-Value="_domainInput" @oninput="OnDomainInputChanged" 
                                          class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                          placeholder="myblog" />
                                <span class="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm rounded-r-md">
                                    .molysite.com
                                </span>
                            </div>
                            <div class="flex items-center mt-1">
                                @if (_checkingAvailability)
                                {
                                    <span class="text-sm text-gray-500">
                                        <i class="fas fa-spinner fa-spin mr-1"></i>检查可用性...
                                    </span>
                                }
                                else if (!string.IsNullOrEmpty(_domainInput))
                                {
                                    @if (_isDomainAvailable)
                                    {
                                        <span class="text-sm text-green-600">
                                            <i class="fas fa-check mr-1"></i>@_domainInput.molysite.com 可用
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="text-sm text-red-600">
                                            <i class="fas fa-times mr-1"></i>@_domainInput.molysite.com 不可用
                                        </span>
                                    }
                                }
                            </div>
                        </div>

                        <!-- 域名建议 -->
                        @if (_domainSuggestions.Any())
                        {
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">推荐域名</label>
                                <div class="space-y-2 max-h-48 overflow-y-auto">
                                    @foreach (var suggestion in _domainSuggestions)
                                    {
                                        <label class="flex items-center p-2 border rounded hover:bg-gray-50 cursor-pointer">
                                            <input type="radio" name="domainSuggestion" value="@suggestion.Domain" 
                                                   @onchange="() => SelectSuggestion(suggestion.Domain)"
                                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300" />
                                            <div class="ml-3 flex-1">
                                                <div class="text-sm font-medium text-gray-900">
                                                    @suggestion.Domain.molysite.com
                                                </div>
                                                <div class="text-xs text-gray-500 flex items-center">
                                                    <span class="mr-2">SEO评分: @suggestion.SeoScore/100</span>
                                                    @if (suggestion.IsRecommended)
                                                    {
                                                        <span class="bg-blue-100 text-blue-800 px-2 py-0.5 rounded text-xs">推荐</span>
                                                    }
                                                </div>
                                            </div>
                                        </label>
                                    }
                                </div>
                            </div>
                        }

                        <!-- 域名规则说明 -->
                        <div class="bg-blue-50 border border-blue-200 rounded p-3">
                            <h4 class="text-sm font-medium text-blue-900 mb-2">域名规则</h4>
                            <ul class="text-xs text-blue-700 space-y-1">
                                <li>• 长度：3-63个字符</li>
                                <li>• 只能包含字母、数字和连字符</li>
                                <li>• 不能以连字符开头或结尾</li>
                                <li>• 建议使用有意义的词汇，有利于SEO</li>
                            </ul>
                        </div>

                        <!-- 最终域名预览 -->
                        @if (!string.IsNullOrEmpty(_selectedDomain))
                        {
                            <div class="bg-green-50 border border-green-200 rounded p-3">
                                <h4 class="text-sm font-medium text-green-900 mb-1">您的网站地址</h4>
                                <div class="text-sm text-green-700 font-mono">
                                    https://@_selectedDomain.molysite.com
                                </div>
                            </div>
                        }
                    </div>
                </div>

                <div class="flex justify-end space-x-3 mt-8 pt-6 border-t">
                    <button type="button" @onclick="GoBack" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded">
                        取消
                    </button>
                    <button type="submit" disabled="@(_isSubmitting || !_isDomainValid)" 
                            class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded disabled:opacity-50 disabled:cursor-not-allowed">
                        @if (_isSubmitting)
                        {
                            <span><i class="fas fa-spinner fa-spin mr-2"></i>创建中...</span>
                        }
                        else
                        {
                            <span>创建站点</span>
                        }
                    </button>
                </div>
            </EditForm>
        </div>
    </div>
</DashboardLayout>

@code {
    private CreateSiteModel _createSiteModel = new();
    private string _userRole = "User";
    private string _errorMessage = string.Empty;
    private string _successMessage = string.Empty;
    private bool _isSubmitting = false;

    // 域名相关
    private string _domainInput = string.Empty;
    private string _selectedDomain = string.Empty;
    private bool _checkingAvailability = false;
    private bool _isDomainAvailable = false;
    private bool _isDomainValid = false;
    private List<DomainSuggestion> _domainSuggestions = new();

    protected override async Task OnInitializedAsync()
    {
        // 初始化
    }

    private async Task OnSiteNameChanged(ChangeEventArgs e)
    {
        var siteName = e.Value?.ToString() ?? string.Empty;
        _createSiteModel.Name = siteName;
        
        if (!string.IsNullOrEmpty(siteName))
        {
            await GenerateDomainSuggestions(siteName);
        }
    }

    private async Task OnSiteTypeChanged(ChangeEventArgs e)
    {
        var siteType = e.Value?.ToString() ?? string.Empty;
        _createSiteModel.SiteType = siteType;
        
        if (!string.IsNullOrEmpty(_createSiteModel.Name))
        {
            await GenerateDomainSuggestions(_createSiteModel.Name);
        }
    }

    private async Task OnDomainInputChanged(ChangeEventArgs e)
    {
        var domain = e.Value?.ToString() ?? string.Empty;
        _domainInput = domain;
        
        if (!string.IsNullOrEmpty(domain))
        {
            await CheckDomainAvailability(domain);
        }
        else
        {
            _isDomainAvailable = false;
            _isDomainValid = false;
        }
    }

    private async Task GenerateDomainSuggestions(string siteName)
    {
        try
        {
            var suggestions = new List<DomainSuggestion>();
            var cleanName = SanitizeDomainName(siteName);
            
            if (!string.IsNullOrEmpty(cleanName))
            {
                // 1. 直接使用网站名
                suggestions.Add(new DomainSuggestion 
                { 
                    Domain = cleanName, 
                    SeoScore = CalculateSeoScore(cleanName),
                    IsRecommended = true 
                });
                
                // 2. 添加类型前缀
                if (!string.IsNullOrEmpty(_createSiteModel.SiteType))
                {
                    var typePrefix = GetSiteTypePrefix(_createSiteModel.SiteType);
                    if (!string.IsNullOrEmpty(typePrefix))
                    {
                        suggestions.Add(new DomainSuggestion 
                        { 
                            Domain = $"{typePrefix}-{cleanName}", 
                            SeoScore = CalculateSeoScore($"{typePrefix}-{cleanName}") 
                        });
                    }
                }
                
                // 3. 添加数字后缀
                for (int i = 1; i <= 3; i++)
                {
                    suggestions.Add(new DomainSuggestion 
                    { 
                        Domain = $"{cleanName}{i}", 
                        SeoScore = CalculateSeoScore($"{cleanName}{i}") 
                    });
                }
                
                // 4. 添加年份
                var year = DateTime.Now.Year;
                suggestions.Add(new DomainSuggestion 
                { 
                    Domain = $"{cleanName}{year}", 
                    SeoScore = CalculateSeoScore($"{cleanName}{year}") 
                });
            }
            
            _domainSuggestions = suggestions.Take(6).ToList();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "生成域名建议时发生错误");
        }
    }

    private async Task CheckDomainAvailability(string domain)
    {
        try
        {
            _checkingAvailability = true;
            StateHasChanged();
            
            // 模拟检查延迟
            await Task.Delay(500);
            
            // 验证域名格式
            if (!IsValidDomainName(domain))
            {
                _isDomainAvailable = false;
                _isDomainValid = false;
                return;
            }
            
            // 检查是否可用（这里使用模拟逻辑）
            _isDomainAvailable = await IsDomainAvailable(domain);
            _isDomainValid = _isDomainAvailable;
            _selectedDomain = _isDomainAvailable ? domain : string.Empty;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "检查域名可用性时发生错误");
            _isDomainAvailable = false;
            _isDomainValid = false;
        }
        finally
        {
            _checkingAvailability = false;
            StateHasChanged();
        }
    }

    private void SelectSuggestion(string domain)
    {
        _domainInput = domain;
        _selectedDomain = domain;
        _isDomainAvailable = true;
        _isDomainValid = true;
        _createSiteModel.Domain = domain;
        StateHasChanged();
    }

    private string SanitizeDomainName(string input)
    {
        if (string.IsNullOrEmpty(input)) return string.Empty;
        
        // 转换为小写
        var result = input.ToLowerInvariant();
        
        // 移除特殊字符，只保留字母、数字和空格
        result = Regex.Replace(result, @"[^a-z0-9\s]", "");
        
        // 将空格替换为连字符
        result = Regex.Replace(result, @"\s+", "-");
        
        // 移除开头和结尾的连字符
        result = result.Trim('-');
        
        // 限制长度
        if (result.Length > 30) result = result.Substring(0, 30).TrimEnd('-');
        
        return result;
    }

    private string GetSiteTypePrefix(string siteType)
    {
        return siteType switch
        {
            "blog" => "blog",
            "portfolio" => "portfolio",
            "business" => "biz",
            "shop" => "shop",
            "personal" => "me",
            _ => string.Empty
        };
    }

    private int CalculateSeoScore(string domain)
    {
        if (string.IsNullOrEmpty(domain)) return 0;
        
        int score = 100;
        
        // 长度评分
        if (domain.Length > 20) score -= 10;
        if (domain.Length > 30) score -= 20;
        
        // 连字符评分
        var hyphens = domain.Count(c => c == '-');
        if (hyphens > 2) score -= hyphens * 5;
        
        // 数字评分
        var numbers = domain.Count(char.IsDigit);
        if (numbers > 0) score -= numbers * 3;
        
        return Math.Max(0, score);
    }

    private bool IsValidDomainName(string domain)
    {
        if (string.IsNullOrEmpty(domain)) return false;
        if (domain.Length < 3 || domain.Length > 63) return false;
        if (domain.StartsWith("-") || domain.EndsWith("-")) return false;
        
        return Regex.IsMatch(domain, @"^[a-z0-9-]+$");
    }

    private async Task<bool> IsDomainAvailable(string domain)
    {
        // 模拟域名可用性检查
        // 在实际实现中，这里应该调用SiteService.IsDomainAvailableAsync
        var unavailableDomains = new[] { "admin", "api", "www", "mail", "blog", "shop", "test", "demo" };
        return !unavailableDomains.Contains(domain.ToLowerInvariant());
    }

    private async Task HandleValidSubmit()
    {
        try
        {
            _isSubmitting = true;
            _errorMessage = string.Empty;
            
            // 设置域名
            _createSiteModel.Domain = _selectedDomain;
            
            // TODO: 调用SiteService创建网站
            // var result = await SiteService.CreateSiteAsync(_createSiteModel);
            
            _successMessage = $"网站创建成功！您的网站地址是：https://{_selectedDomain}.molysite.com";
            
            // 延迟跳转
            await Task.Delay(2000);
            NavigationManager.NavigateTo("/dashboard/my-sites");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "创建网站时发生错误");
            _errorMessage = "创建网站失败，请重试。";
        }
        finally
        {
            _isSubmitting = false;
        }
    }

    private void GoBack()
    {
        NavigationManager.NavigateTo("/dashboard/my-sites");
    }

    // 域名建议模型
    public class DomainSuggestion
    {
        public string Domain { get; set; } = string.Empty;
        public int SeoScore { get; set; }
        public bool IsRecommended { get; set; }
    }

    // 创建网站模型
    public class CreateSiteModel
    {
        [Required(ErrorMessage = "网站名称是必填的")]
        [StringLength(100, ErrorMessage = "网站名称不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "描述不能超过500个字符")]
        public string Description { get; set; } = string.Empty;

        public string Domain { get; set; } = string.Empty;

        public string SiteType { get; set; } = string.Empty;

        public string Theme { get; set; } = "default";

        public bool IsPublic { get; set; } = true;
    }
}
