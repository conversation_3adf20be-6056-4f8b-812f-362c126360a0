using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MolySite.Identity.Data;
using MolySite.Identity.Models;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace MolySite
{
    /// <summary>
    /// 数据库验证程序
    /// </summary>
    public class VerifyDatabase
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("🔍 开始验证数据库状态...");

            var host = CreateHostBuilder(args).Build();

            using (var scope = host.Services.CreateScope())
            {
                var services = scope.ServiceProvider;
                var logger = services.GetRequiredService<ILogger<VerifyDatabase>>();

                try
                {
                    var context = services.GetRequiredService<ApplicationDbContext>();
                    var userManager = services.GetRequiredService<UserManager<ApplicationUser>>();
                    var roleManager = services.GetRequiredService<RoleManager<IdentityRole<Guid>>>();

                    await VerifyDataAsync(context, userManager, roleManager, logger);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "❌ 验证数据库时发生错误");
                    Console.WriteLine($"❌ 错误: {ex.Message}");
                }
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureServices((context, services) =>
                {
                    // 添加数据库上下文
                    services.AddDbContext<ApplicationDbContext>(options =>
                        options.UseNpgsql("Host=localhost;Port=5432;Database=molysite;Username=postgres;Password=******"));

                    // 添加Identity服务
                    services.AddIdentity<ApplicationUser, IdentityRole<Guid>>(options =>
                    {
                        options.Password.RequireDigit = true;
                        options.Password.RequireLowercase = true;
                        options.Password.RequireUppercase = true;
                        options.Password.RequireNonAlphanumeric = true;
                        options.Password.RequiredLength = 8;
                    })
                    .AddEntityFrameworkStores<ApplicationDbContext>()
                    .AddDefaultTokenProviders();
                });

        private static async Task VerifyDataAsync(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole<Guid>> roleManager,
            ILogger logger)
        {
            Console.WriteLine("\n📊 数据库验证结果:");
            Console.WriteLine(new string('=', 50));

            // 验证角色
            var roles = await roleManager.Roles.ToListAsync();
            Console.WriteLine($"\n🔐 角色数量: {roles.Count}");
            foreach (var role in roles)
            {
                Console.WriteLine($"  ✓ {role.Name}");
            }

            // 验证用户
            var users = await userManager.Users.ToListAsync();
            Console.WriteLine($"\n👥 用户数量: {users.Count}");
            foreach (var user in users)
            {
                var userRoles = await userManager.GetRolesAsync(user);
                Console.WriteLine($"  ✓ {user.UserName} ({user.Email})");
                Console.WriteLine($"    - 平台角色: {user.PlatformRole}");
                Console.WriteLine($"    - Identity角色: {string.Join(", ", userRoles)}");
                Console.WriteLine($"    - 状态: {(user.IsActive ? "活跃" : "禁用")}");
                Console.WriteLine($"    - 邮箱确认: {(user.EmailConfirmed ? "已确认" : "未确认")}");
            }

            // 验证网站
            var sites = await context.Sites.ToListAsync();
            Console.WriteLine($"\n🌐 网站数量: {sites.Count}");
            foreach (var site in sites)
            {
                var owner = await userManager.FindByIdAsync(site.OwnerId.ToString());
                Console.WriteLine($"  ✓ {site.Name} ({site.Domain})");
                Console.WriteLine($"    - 所有者: {owner?.UserName ?? "未知"}");
                Console.WriteLine($"    - 状态: {(site.IsActive ? "活跃" : "禁用")} | {(site.IsPublished ? "已发布" : "未发布")}");
                Console.WriteLine($"    - 订阅: {site.SubscriptionPlan}");
            }

            // 验证网站用户角色
            var siteUserRoles = await context.SiteUserRoles
                .Include(sur => sur.User)
                .ToListAsync();
            Console.WriteLine($"\n🔗 网站用户角色数量: {siteUserRoles.Count}");
            foreach (var sur in siteUserRoles)
            {
                var site = await context.Sites.FindAsync(sur.SiteId);
                Console.WriteLine($"  ✓ {sur.User.UserName} -> {sur.Role} @ {site?.Name ?? "未知网站"}");
                Console.WriteLine($"    - 状态: {(sur.IsActive ? "活跃" : "禁用")}");
                Console.WriteLine($"    - 授予时间: {sur.GrantedAt:yyyy-MM-dd HH:mm:ss}");
            }

            // 验证订阅计划
            var subscriptionPlans = await context.SubscriptionPlans.ToListAsync();
            Console.WriteLine($"\n💳 订阅计划数量: {subscriptionPlans.Count}");
            foreach (var plan in subscriptionPlans)
            {
                Console.WriteLine($"  ✓ {plan.Name} - ${plan.MonthlyPrice}/月");
                Console.WriteLine($"    - 状态: {(plan.IsActive ? "活跃" : "禁用")} | {(plan.IsDefault ? "默认" : "可选")}");
            }

            Console.WriteLine("\n" + new string('=', 50));
            Console.WriteLine("✅ 数据库验证完成！");

            // 检查是否符合简化要求
            Console.WriteLine("\n🎯 简化要求检查:");
            
            var expectedRoles = new[] { "SuperAdmin", "User", "Owner" };
            var actualRoleNames = roles.Select(r => r.Name).ToArray();
            var hasCorrectRoles = expectedRoles.All(r => actualRoleNames.Contains(r)) && 
                                 actualRoleNames.All(r => expectedRoles.Contains(r));
            
            Console.WriteLine($"角色检查: {(hasCorrectRoles ? "✅ 通过" : "❌ 失败")}");
            Console.WriteLine($"  期望: {string.Join(", ", expectedRoles)}");
            Console.WriteLine($"  实际: {string.Join(", ", actualRoleNames)}");

            var expectedUsers = new[] { "superadmin", "user" };
            var actualUserNames = users.Select(u => u.UserName).ToArray();
            var hasCorrectUsers = expectedUsers.All(u => actualUserNames.Contains(u)) && 
                                 actualUserNames.All(u => expectedUsers.Contains(u));
            
            Console.WriteLine($"用户检查: {(hasCorrectUsers ? "✅ 通过" : "❌ 失败")}");
            Console.WriteLine($"  期望: {string.Join(", ", expectedUsers)}");
            Console.WriteLine($"  实际: {string.Join(", ", actualUserNames)}");

            Console.WriteLine($"网站检查: {(sites.Count <= 1 ? "✅ 通过" : "❌ 失败")} (应该只有0-1个网站)");
        }
    }
}
