using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Logging;
using MolySite.Core.Constants;

namespace MolySite.Web.Services
{
    /// <summary>
    /// Web层权限服务适配器
    /// </summary>
    public class WebPermissionService : IPermissionService
    {
        private readonly HttpClient _httpClient;
        private readonly AuthenticationStateProvider _authStateProvider;
        private readonly ILogger<WebPermissionService> _logger;

        public WebPermissionService(
            HttpClient httpClient,
            AuthenticationStateProvider authStateProvider,
            ILogger<WebPermissionService> logger)
        {
            _httpClient = httpClient;
            _authStateProvider = authStateProvider;
            _logger = logger;
        }

        /// <inheritdoc/>
        public bool HasPlatformPermission(ClaimsPrincipal user, string permission)
        {
            try
            {
                if (!user.Identity?.IsAuthenticated ?? true)
                {
                    return false;
                }

                // 检查是否为SuperAdmin
                if (user.IsInRole(UserRoles.SuperAdmin))
                {
                    return true;
                }

                // 检查权限声明
                return user.HasClaim("Permission", permission);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查平台权限时发生异常: {Permission}", permission);
                return false;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> HasSitePermissionAsync(ClaimsPrincipal user, Guid siteId, string permission)
        {
            try
            {
                if (!user.Identity?.IsAuthenticated ?? true)
                {
                    return false;
                }

                // SuperAdmin只有有限的网站权限（不能访问网站内容和隐私信息）
                if (user.IsInRole(UserRoles.SuperAdmin))
                {
                    return permission switch
                    {
                        SitePermissions.ViewSettings => true,  // 查看基本设置
                        SitePermissions.ManageUsers => true,   // 管理用户（平台管理需要）
                        _ => false  // 其他权限都不允许
                    };
                }

                var response = await _httpClient.GetAsync($"api/permissions/site/{siteId}/check?permission={Uri.EscapeDataString(permission)}");
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var apiResponse = JsonSerializer.Deserialize<JsonElement>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (apiResponse.TryGetProperty("success", out var successElement) && successElement.GetBoolean() &&
                        apiResponse.TryGetProperty("data", out var dataElement))
                    {
                        return dataElement.GetBoolean();
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查网站权限时发生异常: {SiteId}, {Permission}", siteId, permission);
                return false;
            }
        }

        /// <inheritdoc/>
        public string GetUserRole(ClaimsPrincipal user)
        {
            try
            {
                if (!user.Identity?.IsAuthenticated ?? true)
                {
                    return string.Empty;
                }

                var roleClaims = user.Claims.Where(c => c.Type == ClaimTypes.Role).Select(c => c.Value).ToList();

                if (roleClaims.Contains(UserRoles.SuperAdmin))
                    return UserRoles.SuperAdmin;
                if (roleClaims.Contains(UserRoles.User))
                    return UserRoles.User;
                if (roleClaims.Contains(UserRoles.SiteOwner))
                    return UserRoles.User; // 向后兼容

                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户角色时发生异常");
                return string.Empty;
            }
        }

        /// <inheritdoc/>
        public bool IsSuperAdmin(ClaimsPrincipal user)
        {
            return user.IsInRole(UserRoles.SuperAdmin);
        }

        /// <inheritdoc/>
        public bool IsUser(ClaimsPrincipal user)
        {
            return user.IsInRole(UserRoles.User) || user.IsInRole(UserRoles.SiteOwner); // 向后兼容
        }

        /// <inheritdoc/>
        public bool IsSiteOwner(ClaimsPrincipal user)
        {
            return user.IsInRole(UserRoles.SiteOwner) || user.IsInRole(UserRoles.User);
        }

        /// <inheritdoc/>
        public bool IsSiteEditor(ClaimsPrincipal user)
        {
            return user.IsInRole(UserRoles.SiteEditor);
        }



        /// <inheritdoc/>
        public async Task<List<Guid>> GetUserAccessibleSitesAsync(ClaimsPrincipal user)
        {
            try
            {
                if (!user.Identity?.IsAuthenticated ?? true)
                {
                    return new List<Guid>();
                }

                var response = await _httpClient.GetAsync("api/permissions/accessible-sites");
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var apiResponse = JsonSerializer.Deserialize<JsonElement>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (apiResponse.TryGetProperty("success", out var successElement) && successElement.GetBoolean() &&
                        apiResponse.TryGetProperty("data", out var dataElement))
                    {
                        var siteIds = JsonSerializer.Deserialize<List<Guid>>(dataElement.GetRawText(), new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });

                        return siteIds ?? new List<Guid>();
                    }
                }

                return new List<Guid>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户可访问网站列表时发生异常");
                return new List<Guid>();
            }
        }


    }
}
