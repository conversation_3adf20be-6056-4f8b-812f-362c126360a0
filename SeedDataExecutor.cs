using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MolySite.Identity.Data;
using MolySite.Identity.Models;
using System;
using System.Threading.Tasks;

namespace MolySite
{
    /// <summary>
    /// 临时的seed数据执行程序
    /// </summary>
    public class SeedDataExecutor
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("🌱 开始执行Seed数据更新...");

            var host = CreateHostBuilder(args).Build();

            using (var scope = host.Services.CreateScope())
            {
                var services = scope.ServiceProvider;
                var logger = services.GetRequiredService<ILogger<SeedDataExecutor>>();

                try
                {
                    var context = services.GetRequiredService<ApplicationDbContext>();
                    var userManager = services.GetRequiredService<UserManager<ApplicationUser>>();
                    var roleManager = services.GetRequiredService<RoleManager<IdentityRole<Guid>>>();

                    logger.LogInformation("开始重置并种子数据...");

                    // 执行重置和种子数据
                    await DataSeeder.ResetAndSeedDataAsync(context, userManager, roleManager);

                    logger.LogInformation("✅ 数据重置和种子完成！");

                    // 验证数据
                    await VerifyDataAsync(context, userManager, roleManager, logger);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "❌ 执行seed数据时发生错误");
                    Console.WriteLine($"❌ 错误: {ex.Message}");
                }
            }

            Console.WriteLine("🎉 Seed数据执行完成！");
            Console.WriteLine("\n📋 测试账号信息:");
            Console.WriteLine("SuperAdmin: superadmin / SuperAdmin@2024!");
            Console.WriteLine("User: user / User@2024!");
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureServices((context, services) =>
                {
                    // 添加数据库上下文
                    services.AddDbContext<ApplicationDbContext>(options =>
                        options.UseNpgsql("Host=localhost;Port=5432;Database=molysite;Username=postgres;Password=******"));

                    // 添加Identity服务
                    services.AddIdentity<ApplicationUser, IdentityRole<Guid>>(options =>
                    {
                        options.Password.RequireDigit = true;
                        options.Password.RequireLowercase = true;
                        options.Password.RequireUppercase = true;
                        options.Password.RequireNonAlphanumeric = true;
                        options.Password.RequiredLength = 8;
                    })
                    .AddEntityFrameworkStores<ApplicationDbContext>()
                    .AddDefaultTokenProviders();
                });

        private static async Task VerifyDataAsync(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole<Guid>> roleManager,
            ILogger logger)
        {
            logger.LogInformation("🔍 验证数据...");

            // 验证角色
            var roles = await roleManager.Roles.ToListAsync();
            logger.LogInformation($"角色数量: {roles.Count}");
            foreach (var role in roles)
            {
                logger.LogInformation($"  - {role.Name}");
            }

            // 验证用户
            var users = await userManager.Users.ToListAsync();
            logger.LogInformation($"用户数量: {users.Count}");
            foreach (var user in users)
            {
                var userRoles = await userManager.GetRolesAsync(user);
                logger.LogInformation($"  - {user.UserName} ({user.Email}) - 角色: {string.Join(", ", userRoles)}");
            }

            // 验证网站
            var sites = await context.Sites.ToListAsync();
            logger.LogInformation($"网站数量: {sites.Count}");
            foreach (var site in sites)
            {
                logger.LogInformation($"  - {site.Name} ({site.Domain})");
            }

            // 验证网站用户角色
            var siteUserRoles = await context.SiteUserRoles.Include(sur => sur.User).ToListAsync();
            logger.LogInformation($"网站用户角色数量: {siteUserRoles.Count}");
            foreach (var sur in siteUserRoles)
            {
                logger.LogInformation($"  - {sur.User.UserName} -> {sur.Role}");
            }

            Console.WriteLine("\n📊 数据验证结果:");
            Console.WriteLine($"角色: {roles.Count} 个");
            Console.WriteLine($"用户: {users.Count} 个");
            Console.WriteLine($"网站: {sites.Count} 个");
            Console.WriteLine($"网站用户角色: {siteUserRoles.Count} 个");
        }
    }
}
