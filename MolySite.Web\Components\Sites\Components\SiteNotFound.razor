@using MolySite.Web.Components.Sites.Layouts

<LayoutView Layout="@typeof(SiteLayout)">
    <div class="site-not-found py-16 text-center">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if (!string.IsNullOrEmpty(Domain))
            {
                <h1 class="text-3xl font-bold text-gray-900 mb-4">网站不存在</h1>
                <p class="text-gray-600 mb-8">域名 <strong>@Domain</strong> 对应的网站不存在或尚未发布。</p>
            }
            else
            {
                <h1 class="text-3xl font-bold text-gray-900 mb-4">页面未找到</h1>
                <p class="text-gray-600 mb-8">抱歉，您访问的页面不存在。</p>
            }
            
            <div class="space-y-4">
                <a href="/" class="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    返回首页
                </a>
                <div>
                    <a href="https://molysite.com" class="text-blue-600 hover:text-blue-800 transition-colors">
                        访问 MolySite 平台
                    </a>
                </div>
            </div>
        </div>
    </div>
</LayoutView>

@code {
    [Parameter] public string? Domain { get; set; }
}
