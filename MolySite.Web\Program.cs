using MolySite.Web.Components;
using MolySite.Web.Services;
using MolySite.Web.Dtos;
using MolySite.Web.Middleware;
using System.Net.Http;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage;
using Microsoft.Extensions.Options;
using Microsoft.AspNetCore.Authentication.Cookies;

// 使用环境变量禁用浏览器自动刷新功能
Environment.SetEnvironmentVariable("ASPNETCORE_HOSTINGSTARTUPASSEMBLIES", "");

var builder = WebApplication.CreateBuilder(args);

// 添加详细日志
builder.Logging.AddConsole();
builder.Logging.AddDebug();
builder.Logging.SetMinimumLevel(LogLevel.Debug);

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents()
    .AddCircuitOptions(options =>
    {
        // 设置详细的异常信息
        options.DetailedErrors = builder.Environment.IsDevelopment();

        // 设置连接超时
        options.DisconnectedCircuitMaxRetained = 100;
        options.DisconnectedCircuitRetentionPeriod = TimeSpan.FromMinutes(3);

        // 设置JavaScript调用超时
        options.JSInteropDefaultCallTimeout = TimeSpan.FromMinutes(1);
    });

// 添加HttpContext访问器
builder.Services.AddHttpContextAccessor();

// 添加认证服务
builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options =>
    {
        options.LoginPath = "/login";
        options.LogoutPath = "/logout";
        options.AccessDeniedPath = "/access-denied";
        options.ExpireTimeSpan = TimeSpan.FromHours(12);
        options.SlidingExpiration = true;
        options.Cookie.HttpOnly = true;
        options.Cookie.SameSite = SameSiteMode.Lax;
        options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
        
        // 在开发环境中允许更宽松的Cookie策略
        if (builder.Environment.IsDevelopment())
        {
            options.Cookie.SameSite = SameSiteMode.None;
        }
    });

builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("SuperAdmin", policy => policy.RequireRole("SuperAdmin"));
    options.AddPolicy("User", policy => policy.RequireRole("User", "SuperAdmin"));
    options.AddPolicy("SiteOwner", policy => policy.RequireRole("User", "SuperAdmin")); // 向后兼容
});

builder.Services.AddCascadingAuthenticationState();
builder.Services.AddScoped<ProtectedSessionStorage>();

// 添加JS互操作服务
builder.Services.AddScoped<IJSInteropService, JSInteropService>();

// 注册 CustomAuthStateProvider
builder.Services.AddScoped<AuthenticationStateProvider, CustomAuthStateProvider>(sp => {
    var loggerFactory = sp.GetRequiredService<ILoggerFactory>();
    var jsInteropService = sp.GetRequiredService<IJSInteropService>();
    var serviceProvider = sp;
    var httpContextAccessor = sp.GetRequiredService<IHttpContextAccessor>();
    return new CustomAuthStateProvider(loggerFactory, jsInteropService, serviceProvider, httpContextAccessor);
});
builder.Services.AddAuthorizationCore();

// 添加本地存储服务
builder.Services.AddScoped<ILocalStorageService, LocalStorageService>();

// 配置 API 设置
builder.Services.Configure<ApiSettings>(builder.Configuration.GetSection("ApiSettings"));

// 配置 HttpClient
builder.Services.AddHttpClient("ApiClient", client => {
    client.BaseAddress = new Uri(builder.Configuration["ApiSettings:BaseUrl"] ?? "https://localhost:7048/");
});

// 注册 WebAuthService (适配器)
builder.Services.AddScoped<IAuthService, WebAuthService>(sp => {
    var httpClientFactory = sp.GetRequiredService<IHttpClientFactory>();
    var httpClient = httpClientFactory.CreateClient("ApiClient");
    var logger = sp.GetRequiredService<ILogger<WebAuthService>>();
    var localStorage = sp.GetRequiredService<ILocalStorageService>();
    var authStateProvider = sp.GetRequiredService<AuthenticationStateProvider>();
    var httpContextAccessor = sp.GetRequiredService<IHttpContextAccessor>();
    var jsInteropService = sp.GetRequiredService<IJSInteropService>();
    return new WebAuthService(
        httpClient,
        logger,
        localStorage,
        authStateProvider,
        httpContextAccessor,
        jsInteropService);
});



// 配置 UserService
builder.Services.AddScoped<IUserService, UserService>(sp => {
    var httpClientFactory = sp.GetRequiredService<IHttpClientFactory>();
    var httpClient = httpClientFactory.CreateClient("ApiClient");
    var logger = sp.GetRequiredService<ILogger<UserService>>();
    var options = sp.GetRequiredService<IOptions<ApiSettings>>();
    return new UserService(httpClient, options, logger);
});

// 配置 WebSiteService (适配器)
builder.Services.AddScoped<MolySite.Web.Services.ISiteService, WebSiteService>(sp => {
    var httpClientFactory = sp.GetRequiredService<IHttpClientFactory>();
    var httpClient = httpClientFactory.CreateClient("ApiClient");
    var logger = sp.GetRequiredService<ILogger<WebSiteService>>();
    return new WebSiteService(httpClient, logger);
});

// 配置 WebPermissionService (适配器)
builder.Services.AddScoped<IPermissionService, WebPermissionService>(sp => {
    var httpClientFactory = sp.GetRequiredService<IHttpClientFactory>();
    var httpClient = httpClientFactory.CreateClient("ApiClient");
    var authStateProvider = sp.GetRequiredService<AuthenticationStateProvider>();
    var logger = sp.GetRequiredService<ILogger<WebPermissionService>>();
    return new WebPermissionService(httpClient, authStateProvider, logger);
});

// 配置 SitePageService
builder.Services.AddScoped<ISitePageService, SitePageService>(sp => {
    var httpClientFactory = sp.GetRequiredService<IHttpClientFactory>();
    var httpClient = httpClientFactory.CreateClient("ApiClient");
    var logger = sp.GetRequiredService<ILogger<SitePageService>>();
    return new SitePageService(httpClient, logger);
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}
else
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseAntiforgery();

// 添加智能路由中间件 (在认证之前)
app.UseMiddleware<SiteRoutingMiddleware>();

// 添加认证中间件
app.UseAuthentication();
app.UseAuthorization();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.Run();
