using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MolySite.Identity.Data;
using MolySite.Identity.Models;
using MolySite.Core.Services;

namespace MolySite.Tests
{
    /// <summary>
    /// 重构验证脚本
    /// </summary>
    public class RefactoringValidationScript
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<RefactoringValidationScript> _logger;

        public RefactoringValidationScript(IServiceProvider serviceProvider, ILogger<RefactoringValidationScript> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        /// <summary>
        /// 执行完整的重构验证
        /// </summary>
        public async Task<ValidationResult> ValidateRefactoringAsync()
        {
            var result = new ValidationResult();

            try
            {
                _logger.LogInformation("开始执行重构验证...");

                // 1. 验证数据模型
                await ValidateDataModelsAsync(result);

                // 2. 验证权限系统
                await ValidatePermissionSystemAsync(result);

                // 3. 验证关注功能
                await ValidateFollowFunctionalityAsync(result);

                // 4. 验证角色转换
                await ValidateRoleConversionAsync(result);

                // 5. 验证API接口
                await ValidateApiInterfacesAsync(result);

                _logger.LogInformation("重构验证完成，成功: {SuccessCount}, 失败: {FailureCount}", 
                    result.SuccessCount, result.FailureCount);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行重构验证时发生错误");
                result.AddFailure("重构验证", $"执行验证时发生异常: {ex.Message}");
                return result;
            }
        }

        private async Task ValidateDataModelsAsync(ValidationResult result)
        {
            _logger.LogInformation("验证数据模型...");

            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            try
            {
                // 验证PlatformRole枚举
                var validPlatformRoles = new[] { PlatformRole.SuperAdmin, PlatformRole.User };
                var users = await context.Users.ToListAsync();
                
                foreach (var user in users)
                {
                    if (!validPlatformRoles.Contains(user.PlatformRole))
                    {
                        result.AddFailure("数据模型", $"用户 {user.Id} 的平台角色无效: {user.PlatformRole}");
                        continue;
                    }

                    if (user.PlatformRole == PlatformRole.SuperAdmin && !user.PlatformRoles.Contains("SuperAdmin"))
                    {
                        result.AddFailure("数据模型", $"SuperAdmin用户 {user.Id} 的角色字符串不正确");
                        continue;
                    }

                    if (user.PlatformRole == PlatformRole.User && !user.PlatformRoles.Contains("User"))
                    {
                        result.AddFailure("数据模型", $"User用户 {user.Id} 的角色字符串不正确");
                        continue;
                    }
                }

                // 验证SiteRoleType枚举
                var validSiteRoles = new[] { SiteRoleType.Owner, SiteRoleType.Editor, SiteRoleType.Follower };
                var siteRoles = await context.SiteUserRoles.ToListAsync();
                
                foreach (var siteRole in siteRoles)
                {
                    if (!validSiteRoles.Contains(siteRole.Role))
                    {
                        result.AddFailure("数据模型", $"网站用户角色 {siteRole.Id} 的角色类型无效: {siteRole.Role}");
                    }
                }

                result.AddSuccess("数据模型", "数据模型验证通过");
            }
            catch (Exception ex)
            {
                result.AddFailure("数据模型", $"验证数据模型时发生错误: {ex.Message}");
            }
        }

        private async Task ValidatePermissionSystemAsync(ValidationResult result)
        {
            _logger.LogInformation("验证权限系统...");

            using var scope = _serviceProvider.CreateScope();
            var permissionService = scope.ServiceProvider.GetRequiredService<IPermissionService>();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            try
            {
                var superAdmin = await context.Users.FirstOrDefaultAsync(u => u.PlatformRole == PlatformRole.SuperAdmin);
                var user = await context.Users.FirstOrDefaultAsync(u => u.PlatformRole == PlatformRole.User);
                var site = await context.Sites.FirstOrDefaultAsync();

                if (superAdmin != null && site != null)
                {
                    // 验证SuperAdmin的有限网站权限
                    var canViewSettings = await permissionService.HasSitePermissionAsync(superAdmin.Id, site.Id, "Site.ViewSettings");
                    var canEditContent = await permissionService.HasSitePermissionAsync(superAdmin.Id, site.Id, "Site.EditContent");

                    if (!canViewSettings.IsSuccess || !canViewSettings.Data)
                    {
                        result.AddFailure("权限系统", "SuperAdmin应该能够查看网站设置");
                    }

                    if (!canEditContent.IsSuccess || canEditContent.Data)
                    {
                        result.AddFailure("权限系统", "SuperAdmin不应该能够编辑网站内容");
                    }
                }

                result.AddSuccess("权限系统", "权限系统验证通过");
            }
            catch (Exception ex)
            {
                result.AddFailure("权限系统", $"验证权限系统时发生错误: {ex.Message}");
            }
        }

        private async Task ValidateFollowFunctionalityAsync(ValidationResult result)
        {
            _logger.LogInformation("验证关注功能...");

            using var scope = _serviceProvider.CreateScope();
            var followService = scope.ServiceProvider.GetRequiredService<IFollowService>();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            try
            {
                var user = await context.Users.FirstOrDefaultAsync(u => u.PlatformRole == PlatformRole.User);
                var site = await context.Sites.FirstOrDefaultAsync(s => s.OwnerId != user!.Id);

                if (user != null && site != null)
                {
                    // 测试关注功能
                    var followResult = await followService.FollowSiteAsync(user.Id, site.Id);
                    if (!followResult.IsSuccess)
                    {
                        result.AddFailure("关注功能", $"关注网站失败: {followResult.ErrorMessage}");
                        return;
                    }

                    // 测试检查关注状态
                    var isFollowingResult = await followService.IsFollowingSiteAsync(user.Id, site.Id);
                    if (!isFollowingResult.IsSuccess || !isFollowingResult.Data)
                    {
                        result.AddFailure("关注功能", "检查关注状态失败");
                        return;
                    }

                    // 测试取消关注
                    var unfollowResult = await followService.UnfollowSiteAsync(user.Id, site.Id);
                    if (!unfollowResult.IsSuccess)
                    {
                        result.AddFailure("关注功能", $"取消关注失败: {unfollowResult.ErrorMessage}");
                        return;
                    }
                }

                result.AddSuccess("关注功能", "关注功能验证通过");
            }
            catch (Exception ex)
            {
                result.AddFailure("关注功能", $"验证关注功能时发生错误: {ex.Message}");
            }
        }

        private async Task ValidateRoleConversionAsync(ValidationResult result)
        {
            _logger.LogInformation("验证角色转换...");

            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            try
            {
                // 检查是否还有旧的角色值
                var hasInvalidPlatformRoles = await context.Users
                    .AnyAsync(u => (int)u.PlatformRole > 2 || (int)u.PlatformRole < 1);

                if (hasInvalidPlatformRoles)
                {
                    result.AddFailure("角色转换", "发现无效的平台角色值");
                    return;
                }

                var hasInvalidRoleStrings = await context.Users
                    .AnyAsync(u => u.PlatformRoles.Any(r => r != "SuperAdmin" && r != "User"));

                if (hasInvalidRoleStrings)
                {
                    result.AddFailure("角色转换", "发现无效的角色字符串");
                    return;
                }

                result.AddSuccess("角色转换", "角色转换验证通过");
            }
            catch (Exception ex)
            {
                result.AddFailure("角色转换", $"验证角色转换时发生错误: {ex.Message}");
            }
        }

        private async Task ValidateApiInterfacesAsync(ValidationResult result)
        {
            _logger.LogInformation("验证API接口...");

            try
            {
                // 这里可以添加API接口的验证逻辑
                // 例如检查控制器是否正确注册，路由是否正确等

                result.AddSuccess("API接口", "API接口验证通过");
            }
            catch (Exception ex)
            {
                result.AddFailure("API接口", $"验证API接口时发生错误: {ex.Message}");
            }

            await Task.CompletedTask;
        }
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        public List<ValidationItem> Items { get; } = new();
        public int SuccessCount => Items.Count(i => i.IsSuccess);
        public int FailureCount => Items.Count(i => !i.IsSuccess);
        public bool IsOverallSuccess => FailureCount == 0;

        public void AddSuccess(string category, string message)
        {
            Items.Add(new ValidationItem { Category = category, Message = message, IsSuccess = true });
        }

        public void AddFailure(string category, string message)
        {
            Items.Add(new ValidationItem { Category = category, Message = message, IsSuccess = false });
        }
    }

    /// <summary>
    /// 验证项
    /// </summary>
    public class ValidationItem
    {
        public string Category { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public bool IsSuccess { get; set; }
    }
}
