using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MolySite.Core.Models;
using MolySite.Core.Interfaces;
using MolySite.Identity.Data;
using MolySite.Identity.Models;

namespace MolySite.Core.Services
{
    /// <summary>
    /// 网站关注服务实现
    /// </summary>
    public class FollowService : IFollowService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<FollowService> _logger;

        public FollowService(
            ApplicationDbContext context,
            ILogger<FollowService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<Result> FollowSiteAsync(Guid userId, Guid siteId)
        {
            try
            {
                // 检查用户是否存在
                var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
                if (user == null)
                {
                    return Result.Failure("用户不存在", "USER_NOT_FOUND");
                }

                // 检查网站是否存在
                var site = await _context.Sites.FirstOrDefaultAsync(s => s.Id == siteId);
                if (site == null)
                {
                    return Result.Failure("网站不存在", "SITE_NOT_FOUND");
                }

                // 检查是否已经关注
                var existingFollow = await _context.SiteUserRoles
                    .FirstOrDefaultAsync(sr => sr.UserId == userId && sr.SiteId == siteId && sr.Role == SiteRoleType.Follower);

                if (existingFollow != null)
                {
                    if (existingFollow.IsActive)
                    {
                        return Result.Failure("您已经关注了这个网站", "ALREADY_FOLLOWING");
                    }
                    else
                    {
                        // 重新激活关注
                        existingFollow.IsActive = true;
                        existingFollow.GrantedAt = DateTime.UtcNow;
                    }
                }
                else
                {
                    // 创建新的关注关系
                    var followRelation = new SiteUserRole
                    {
                        Id = Guid.NewGuid(),
                        UserId = userId,
                        SiteId = siteId,
                        Role = SiteRoleType.Follower,
                        GrantedAt = DateTime.UtcNow,
                        IsActive = true,
                        InvitationStatus = InvitationStatus.Accepted
                    };

                    _context.SiteUserRoles.Add(followRelation);
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("用户 {UserId} 成功关注网站 {SiteId}", userId, siteId);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "关注网站时发生错误: UserId={UserId}, SiteId={SiteId}", userId, siteId);
                return Result.Failure("关注网站失败", "FOLLOW_SITE_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result> UnfollowSiteAsync(Guid userId, Guid siteId)
        {
            try
            {
                var followRelation = await _context.SiteUserRoles
                    .FirstOrDefaultAsync(sr => sr.UserId == userId && sr.SiteId == siteId && 
                                             sr.Role == SiteRoleType.Follower && sr.IsActive);

                if (followRelation == null)
                {
                    return Result.Failure("您没有关注这个网站", "NOT_FOLLOWING");
                }

                // 软删除：设置为非活跃状态
                followRelation.IsActive = false;

                await _context.SaveChangesAsync();

                _logger.LogInformation("用户 {UserId} 成功取消关注网站 {SiteId}", userId, siteId);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消关注网站时发生错误: UserId={UserId}, SiteId={SiteId}", userId, siteId);
                return Result.Failure("取消关注失败", "UNFOLLOW_SITE_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<bool>> IsFollowingSiteAsync(Guid userId, Guid siteId)
        {
            try
            {
                var isFollowing = await _context.SiteUserRoles
                    .AnyAsync(sr => sr.UserId == userId && sr.SiteId == siteId && 
                                  sr.Role == SiteRoleType.Follower && sr.IsActive);

                return Result<bool>.Success(isFollowing);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查关注状态时发生错误: UserId={UserId}, SiteId={SiteId}", userId, siteId);
                return Result<bool>.Failure("检查关注状态失败", "CHECK_FOLLOW_STATUS_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<List<Site>>> GetFollowedSitesAsync(Guid userId)
        {
            try
            {
                var followedSites = await _context.SiteUserRoles
                    .Where(sr => sr.UserId == userId && sr.Role == SiteRoleType.Follower && sr.IsActive)
                    .Include(sr => sr.Site)
                    .Select(sr => sr.Site)
                    .ToListAsync();

                return Result<List<Site>>.Success(followedSites);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户关注的网站列表时发生错误: UserId={UserId}", userId);
                return Result<List<Site>>.Failure("获取关注列表失败", "GET_FOLLOWED_SITES_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<List<ApplicationUser>>> GetSiteFollowersAsync(Guid siteId)
        {
            try
            {
                var followers = await _context.SiteUserRoles
                    .Where(sr => sr.SiteId == siteId && sr.Role == SiteRoleType.Follower && sr.IsActive)
                    .Include(sr => sr.User)
                    .Select(sr => sr.User)
                    .ToListAsync();

                return Result<List<ApplicationUser>>.Success(followers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取网站关注者列表时发生错误: SiteId={SiteId}", siteId);
                return Result<List<ApplicationUser>>.Failure("获取关注者列表失败", "GET_SITE_FOLLOWERS_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result<int>> GetSiteFollowerCountAsync(Guid siteId)
        {
            try
            {
                var count = await _context.SiteUserRoles
                    .CountAsync(sr => sr.SiteId == siteId && sr.Role == SiteRoleType.Follower && sr.IsActive);

                return Result<int>.Success(count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取网站关注者数量时发生错误: SiteId={SiteId}", siteId);
                return Result<int>.Failure("获取关注者数量失败", "GET_FOLLOWER_COUNT_ERROR");
            }
        }

        /// <inheritdoc/>
        public async Task<Result> SendNotificationToFollowersAsync(Guid siteId, string title, string content, Guid senderId)
        {
            try
            {
                // 获取网站的所有关注者
                var followers = await _context.SiteUserRoles
                    .Where(sr => sr.SiteId == siteId && sr.Role == SiteRoleType.Follower && sr.IsActive)
                    .Include(sr => sr.User)
                    .Select(sr => sr.User)
                    .ToListAsync();

                // TODO: 实现通知发送逻辑
                // 这里可以集成邮件服务、推送通知等
                
                _logger.LogInformation("向网站 {SiteId} 的 {FollowerCount} 个关注者发送通知: {Title}", 
                    siteId, followers.Count, title);

                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "向关注者发送通知时发生错误: SiteId={SiteId}", siteId);
                return Result.Failure("发送通知失败", "SEND_NOTIFICATION_ERROR");
            }
        }
    }
}
