using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MolySite.Identity.Data;
using MolySite.Identity.Models;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace MolySite
{
    /// <summary>
    /// 测试seed数据
    /// </summary>
    public class TestSeedData
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("🧪 开始测试Seed数据...");

            var host = CreateHostBuilder(args).Build();

            using (var scope = host.Services.CreateScope())
            {
                var services = scope.ServiceProvider;

                try
                {
                    var context = services.GetRequiredService<ApplicationDbContext>();
                    var userManager = services.GetRequiredService<UserManager<ApplicationUser>>();
                    var roleManager = services.GetRequiredService<RoleManager<IdentityRole<Guid>>>();

                    Console.WriteLine("🌱 执行Seed数据...");
                    
                    // 执行seed数据
                    await DataSeeder.ResetAndSeedDataAsync(context, userManager, roleManager);
                    
                    Console.WriteLine("✅ Seed数据执行完成！");
                    
                    // 验证数据
                    await VerifyDataAsync(context, userManager, roleManager);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ 错误: {ex.Message}");
                    Console.WriteLine($"详细信息: {ex}");
                }
            }

            Console.WriteLine("\n🎉 测试完成！");
            Console.WriteLine("\n📋 测试账号信息:");
            Console.WriteLine("SuperAdmin: <EMAIL> / SuperAdmin@2024!");
            Console.WriteLine("User: <EMAIL> / User@2024!");
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureServices((context, services) =>
                {
                    // 添加数据库上下文
                    services.AddDbContext<ApplicationDbContext>(options =>
                        options.UseNpgsql("Host=localhost;Port=5432;Database=molysite;Username=postgres;Password=******"));

                    // 添加Identity服务
                    services.AddIdentity<ApplicationUser, IdentityRole<Guid>>(options =>
                    {
                        options.Password.RequireDigit = true;
                        options.Password.RequireLowercase = true;
                        options.Password.RequireUppercase = true;
                        options.Password.RequireNonAlphanumeric = true;
                        options.Password.RequiredLength = 8;
                    })
                    .AddEntityFrameworkStores<ApplicationDbContext>()
                    .AddDefaultTokenProviders();
                });

        private static async Task VerifyDataAsync(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole<Guid>> roleManager)
        {
            Console.WriteLine("\n📊 验证结果:");
            Console.WriteLine(new string('=', 40));

            // 验证角色
            var roles = await roleManager.Roles.ToListAsync();
            Console.WriteLine($"\n🔐 角色 ({roles.Count}):");
            foreach (var role in roles)
            {
                Console.WriteLine($"  ✓ {role.Name}");
            }

            // 验证用户
            var users = await userManager.Users.ToListAsync();
            Console.WriteLine($"\n👥 用户 ({users.Count}):");
            foreach (var user in users)
            {
                var userRoles = await userManager.GetRolesAsync(user);
                Console.WriteLine($"  ✓ {user.UserName} ({user.Email})");
                Console.WriteLine($"    角色: {string.Join(", ", userRoles)}");
                Console.WriteLine($"    平台角色: {user.PlatformRole}");
            }

            // 验证网站
            var sites = await context.Sites.ToListAsync();
            Console.WriteLine($"\n🌐 网站 ({sites.Count}):");
            foreach (var site in sites)
            {
                var owner = await userManager.FindByIdAsync(site.OwnerId.ToString());
                Console.WriteLine($"  ✓ {site.Name} ({site.Domain})");
                Console.WriteLine($"    所有者: {owner?.UserName}");
            }

            // 验证网站用户角色
            var siteUserRoles = await context.SiteUserRoles
                .Include(sur => sur.User)
                .ToListAsync();
            Console.WriteLine($"\n🔗 网站角色关系 ({siteUserRoles.Count}):");
            foreach (var sur in siteUserRoles)
            {
                var site = await context.Sites.FindAsync(sur.SiteId);
                Console.WriteLine($"  ✓ {sur.User.UserName} -> {sur.Role} @ {site?.Name}");
            }

            Console.WriteLine("\n" + new string('=', 40));
            
            // 检查简化要求
            var expectedRoles = new[] { "SuperAdmin", "User", "Owner" };
            var actualRoles = roles.Select(r => r.Name).ToArray();
            var rolesMatch = expectedRoles.All(r => actualRoles.Contains(r));
            
            var expectedUsers = new[] { "superadmin", "user" };
            var actualUsers = users.Select(u => u.UserName).ToArray();
            var usersMatch = expectedUsers.All(u => actualUsers.Contains(u)) && actualUsers.Length == 2;
            
            Console.WriteLine($"角色检查: {(rolesMatch ? "✅ 通过" : "❌ 失败")}");
            Console.WriteLine($"用户检查: {(usersMatch ? "✅ 通过" : "❌ 失败")}");
            Console.WriteLine($"网站检查: {(sites.Count <= 1 ? "✅ 通过" : "❌ 失败")}");
            
            if (rolesMatch && usersMatch && sites.Count <= 1)
            {
                Console.WriteLine("\n🎉 所有检查都通过！简化成功！");
            }
            else
            {
                Console.WriteLine("\n⚠️ 部分检查未通过，请检查数据。");
            }
        }
    }
}
