@* 用户管理页面 *@
@page "/dashboard/users"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using MolySite.Web.Services
@using MolySite.Web.Components.Pages.Dashboard.Components
@using MolySite.Shared.Models
@attribute [Authorize(Policy = "SuperAdmin")]
@inject AuthenticationStateProvider AuthStateProvider
@inject IPermissionService PermissionService
@inject IUserService UserService
@inject ILogger<Index> Logger

<PageTitle>用户管理 - MolySite</PageTitle>

<DashboardLayout UserRole="SuperAdmin">
    <div class="space-y-6">
        <!-- 页面标题 -->
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">用户管理</h1>
                <p class="text-gray-600">管理系统中的所有用户</p>
            </div>
            <a href="/dashboard/users/create" class="btn-primary">
                <i class="bi-plus mr-2"></i>创建用户
            </a>
        </div>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <i class="bi-people text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">总用户数</p>
                        <p class="text-2xl font-bold text-gray-900">@_users.Count</p>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <i class="bi-person-check text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">活跃用户</p>
                        <p class="text-2xl font-bold text-gray-900">@_users.Count(u => u.IsActive)</p>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <i class="bi-person-circle text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">普通用户</p>
                        <p class="text-2xl font-bold text-gray-900">@_users.Count(u => u.Role == "User")</p>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <i class="bi-house text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">活跃网站</p>
                        <p class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户列表 -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">用户列表</h2>
            </div>
            
            @if (_loading)
            {
                <div class="p-6 text-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                    <p class="mt-2 text-gray-500">加载中...</p>
                </div>
            }
            else if (!_users.Any())
            {
                <div class="p-6 text-center">
                    <i class="bi-people text-gray-400 text-4xl"></i>
                    <p class="mt-2 text-gray-500">暂无用户数据</p>
                </div>
            }
            else
            {
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">注册时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach (var user in _users)
                            {
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="h-10 w-10 flex-shrink-0">
                                                <div class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                                                    <span class="text-white font-medium text-sm">@user.UserName.Substring(0, 1).ToUpper()</span>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">@user.UserName</div>
                                                <div class="text-sm text-gray-500">@user.Email</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full @GetRoleBadgeClass(user.Role)">
                                            @GetRoleDisplayName(user.Role)
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full @(user.IsActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800")">
                                            @(user.IsActive ? "活跃" : "停用")
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        @user.CreatedAt.ToString("yyyy-MM-dd")
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <a href="/dashboard/users/@user.Id/edit" class="text-blue-600 hover:text-blue-900">编辑</a>
                                        @if (user.Role != "SuperAdmin")
                                        {
                                            <button @onclick="() => ToggleUserStatus(user)" class="text-yellow-600 hover:text-yellow-900">
                                                @(user.IsActive ? "停用" : "启用")
                                            </button>
                                            <button @onclick="() => ShowDeleteConfirm(user)" class="text-red-600 hover:text-red-900">删除</button>
                                        }
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
        </div>
    </div>
</DashboardLayout>

@code {
    private bool _loading = true;
    private List<UserDto> _users = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadUsersAsync();
    }

    private async Task LoadUsersAsync()
    {
        try
        {
            _loading = true;
            _users = await UserService.GetAllUsersAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载用户列表时发生错误");
        }
        finally
        {
            _loading = false;
        }
    }

    private string GetRoleDisplayName(string role)
    {
        return role switch
        {
            "SuperAdmin" => "超级管理员",
            "User" => "普通用户",
            "SiteOwner" => "普通用户", // 向后兼容
            _ => role
        };
    }

    private string GetRoleBadgeClass(string role)
    {
        return role switch
        {
            "SuperAdmin" => "bg-red-100 text-red-800",
            "User" => "bg-blue-100 text-blue-800",
            "SiteOwner" => "bg-blue-100 text-blue-800", // 向后兼容
            _ => "bg-gray-100 text-gray-800"
        };
    }

    private async Task ToggleUserStatus(UserDto user)
    {
        // TODO: 实现用户状态切换
    }

    private void ShowDeleteConfirm(UserDto user)
    {
        // TODO: 实现删除确认对话框
    }
}
