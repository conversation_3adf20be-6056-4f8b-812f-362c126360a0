using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using MolySite.Identity.Models;
using MolySite.Shared.Dtos;

namespace MolySite.Identity.Services
{
    /// <summary>
    /// 网站权限服务接口
    /// </summary>
    public interface ISitePermissionService
    {
        /// <summary>
        /// 检查用户在特定网站的权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="siteId">网站ID</param>
        /// <param name="permission">权限名称</param>
        /// <returns>是否拥有权限</returns>
        Task<bool> HasSitePermissionAsync(Guid userId, Guid siteId, string permission);

        /// <summary>
        /// 检查用户的平台权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="permission">权限名称</param>
        /// <returns>是否拥有权限</returns>
        Task<bool> HasPlatformPermissionAsync(Guid userId, string permission);

        /// <summary>
        /// 检查用户在特定网站的权限（基于ClaimsPrincipal）
        /// </summary>
        /// <param name="user">用户声明主体</param>
        /// <param name="siteId">网站ID</param>
        /// <param name="permission">权限名称</param>
        /// <returns>是否拥有权限</returns>
        Task<bool> HasSitePermissionAsync(ClaimsPrincipal user, Guid siteId, string permission);

        /// <summary>
        /// 获取用户在网站中的角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="siteId">网站ID</param>
        /// <returns>用户角色</returns>
        Task<SiteRoleType?> GetUserRoleInSiteAsync(Guid userId, Guid siteId);

        /// <summary>
        /// 获取用户拥有的所有网站
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>网站列表</returns>
        Task<List<Site>> GetUserOwnedSitesAsync(Guid userId);

        /// <summary>
        /// 获取用户可以编辑的所有网站
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>网站列表</returns>
        Task<List<Site>> GetUserEditableSitesAsync(Guid userId);

        /// <summary>
        /// 获取用户可以访问的所有网站
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>网站列表</returns>
        Task<List<Site>> GetUserAccessibleSitesAsync(Guid userId);

        /// <summary>
        /// 检查用户是否可以访问网站
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="siteId">网站ID</param>
        /// <returns>是否可以访问</returns>
        Task<bool> CanUserAccessSiteAsync(Guid userId, Guid siteId);

        /// <summary>
        /// 获取用户在网站中的所有权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="siteId">网站ID</param>
        /// <returns>权限列表</returns>
        Task<List<string>> GetUserSitePermissionsAsync(Guid userId, Guid siteId);

        /// <summary>
        /// 获取用户的平台权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>权限列表</returns>
        Task<List<string>> GetUserPlatformPermissionsAsync(Guid userId);

        /// <summary>
        /// 为用户分配网站角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="siteId">网站ID</param>
        /// <param name="role">角色类型</param>
        /// <param name="grantedBy">授权者ID</param>
        /// <returns>是否成功</returns>
        Task<bool> AssignSiteRoleAsync(Guid userId, Guid siteId, SiteRoleType role, Guid grantedBy);

        /// <summary>
        /// 移除用户的网站角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="siteId">网站ID</param>
        /// <returns>是否成功</returns>
        Task<bool> RemoveSiteRoleAsync(Guid userId, Guid siteId);

        /// <summary>
        /// 检查用户是否为SuperAdmin
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否为SuperAdmin</returns>
        Task<bool> IsSuperAdminAsync(Guid userId);

        /// <summary>
        /// 检查用户是否为网站所有者
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="siteId">网站ID</param>
        /// <returns>是否为网站所有者</returns>
        Task<bool> IsSiteOwnerAsync(Guid userId, Guid siteId);
    }
}
