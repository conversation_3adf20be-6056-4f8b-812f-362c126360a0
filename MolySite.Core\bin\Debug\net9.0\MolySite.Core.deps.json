{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"MolySite.Core/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": "9.0.6", "Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.IdentityModel.Tokens": "8.0.1", "MolySite.Identity": "1.0.0", "MolySite.Shared": "1.0.0", "System.ComponentModel.Annotations": "5.0.0", "System.IdentityModel.Tokens.Jwt": "8.0.1"}, "runtime": {"MolySite.Core.dll": {}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.0": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.6": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.6", "Microsoft.Extensions.Identity.Stores": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.EntityFrameworkCore/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.6", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.6": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.6": {}, "Microsoft.EntityFrameworkCore.Relational/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26607"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Caching.Memory/9.0.6": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Identity.Core/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Identity.Core.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.Extensions.Identity.Stores/9.0.6": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Identity.Core": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Identity.Stores.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.Extensions.Logging/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Options/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Primitives/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Logging/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Protocols/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.0.1", "System.IdentityModel.Tokens.Jwt": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Tokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Npgsql/9.0.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.6", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.0.0"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.EntityFrameworkCore.Relational": "9.0.6", "Npgsql": "9.0.0"}, "runtime": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.0.0"}}}, "System.ComponentModel.Annotations/5.0.0": {}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "System.Text.Json/9.0.0": {}, "MolySite.Identity/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.0", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "9.0.6", "Microsoft.EntityFrameworkCore": "9.0.6", "MolySite.Shared": "1.0.0", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.0", "System.IdentityModel.Tokens.Jwt": "8.0.1"}, "runtime": {"MolySite.Identity.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "MolySite.Shared/1.0.0": {"runtime": {"MolySite.Shared.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"MolySite.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bs+1Pq3vQdS2lTyxNUd9fEhtMsq3eLUpK36k2t56iDMVrk6OrAoFtvrQrTK0Y0OetTcJrUkGU7hBlf+ORzHLqQ==", "path": "microsoft.aspnetcore.authentication.jwtbearer/9.0.0", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-AbRZPnAXFIvKKOWer6setdYy6/KY/hM9onf/LscD1O6sD8AJSyYemxmIa9T/kbfEPek2neEn9+lnPf7ClfglTQ==", "path": "microsoft.aspnetcore.cryptography.internal/9.0.6", "hashPath": "microsoft.aspnetcore.cryptography.internal.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-8yJVwMyycWClrTjbyWl9AUquizaMC9wZWj/Q+Pqdbse25cwMSb6fROrpZyZULqRfE67iV1vh6igG3S+nakIKqQ==", "path": "microsoft.aspnetcore.cryptography.keyderivation/9.0.6", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-50+a2SQEx0eOQmLPjxO0sy/leDNvwsU1/HFXpTbIjXGUURrL5fCZGlTENM3CJNWzwd2cCGB/ZLFHFgKTdSyLzw==", "path": "microsoft.aspnetcore.identity.entityframeworkcore/9.0.6", "hashPath": "microsoft.aspnetcore.identity.entityframeworkcore.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-r5hzM6Bhw4X3z28l5vmsaCPjk9VsQP4zaaY01THh1SAYjgTMVadYIvpNkCfmrv/Klks6aIf2A9eY7cpGZab/hg==", "path": "microsoft.entityframeworkcore/9.0.6", "hashPath": "microsoft.entityframeworkcore.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-7MkhPK8emb8hfOx/mFVvHuIHxQ+mH2YdlK4sFUXgsGlvR0A44vsmd2wcHavZOTTzaKhN+aFUVy3zmkztKmTo+A==", "path": "microsoft.entityframeworkcore.abstractions/9.0.6", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VKggHNQC5FCn3/vooaIM/4aEjGmrmWm78IrdRLz9lLV0Rm9bVHEr/jiWApDkU0U9ec2xGAilvQqJ5mMX7QC2cw==", "path": "microsoft.entityframeworkcore.analyzers/9.0.6", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Ht6OT17sYnO31Dx+hX72YHrc5kZt53g5napaw0FpyIekXCvb+gUVvufEG55Fa7taFm8ccy0Vzs+JVNR9NL0JlA==", "path": "microsoft.entityframeworkcore.relational/9.0.6", "hashPath": "microsoft.entityframeworkcore.relational.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-bL/xQsVNrdVkzjP5yjX4ndkQ03H3+Bk3qPpl+AMCEJR2RkfgAYmoQ/xXffPV7is64+QHShnhA12YAaFmNbfM+A==", "path": "microsoft.extensions.caching.abstractions/9.0.6", "hashPath": "microsoft.extensions.caching.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-qPW2d798tBPZcRmrlaBJqyChf2+0odDdE+0Lxvrr0ywkSNl1oNMK8AKrOfDwyXyjuLCv0ua7p6nrUExCeXhCcg==", "path": "microsoft.extensions.caching.memory/9.0.6", "hashPath": "microsoft.extensions.caching.memory.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-3GgMIi2jP8g1fBW93Z9b9Unamc0SIsgyhiCmC91gq4loTixK9vQMuxxUsfJ1kRGwn+/FqLKwOHqmn0oYWn3Fvw==", "path": "microsoft.extensions.configuration.abstractions/9.0.6", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-vS65HMo5RS10DD543fknsyVDxihMcVxVn3/hNaILgBxWYnOLxWIeCIO9X0QFuCvPRNjClvXe9Aj8KaQNx7vFkQ==", "path": "microsoft.extensions.dependencyinjection/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-UByIcYE2r8k2k8Mh+Me9qFg5jzJQ8OFe5r1bePqvI15iK118PsjUtJXppFp3miuZpMOMpYKZM9xfLZ/64MhcNg==", "path": "microsoft.extensions.identity.core/9.0.6", "hashPath": "microsoft.extensions.identity.core.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Identity.Stores/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-kAPyVejLK5/hNSXRdJ3LZomkzwsde2dEklLGVhR7OS+fJs70nIuj5f52U0LDQ05KAuQsKOTc/utaghiH07uKIg==", "path": "microsoft.extensions.identity.stores/9.0.6", "hashPath": "microsoft.extensions.identity.stores.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-XBzjitTFaQhF8EbJ645vblZezV1p52ePTxKHoVkRidHF11Xkjxg94qr0Rvp2qyxK2vBJ4OIZ41NB15YUyxTGMQ==", "path": "microsoft.extensions.logging/9.0.6", "hashPath": "microsoft.extensions.logging.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "path": "microsoft.extensions.logging.abstractions/9.0.6", "hashPath": "microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-wUPhNM1zsI58Dy10xRdF2+pnsisiUuETg5ZBncyAEEUm/CQ9Q1vmivyUWH8RDbAlqyixf2dJNQ2XZb7HsKUEQw==", "path": "microsoft.extensions.options/9.0.6", "hashPath": "microsoft.extensions.options.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw==", "path": "microsoft.extensions.primitives/9.0.6", "hashPath": "microsoft.extensions.primitives.9.0.6.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OtlIWcyX01olfdevPKZdIPfBEvbcioDyBiE/Z2lHsopsMD7twcKtlN9kMevHmI5IIPhFpfwCIiR6qHQz1WHUIw==", "path": "microsoft.identitymodel.abstractions/8.0.1", "hashPath": "microsoft.identitymodel.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s6++gF9x0rQApQzOBbSyp4jUaAlwm+DroKfL8gdOHxs83k8SJfUXhuc46rDB3rNXBQ1MVRxqKUrqFhO/M0E97g==", "path": "microsoft.identitymodel.jsonwebtokens/8.0.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UCPF2exZqBXe7v/6sGNiM6zCQOUXXQ9+v5VTb9gPB8ZSUPnX53BxlN78v2jsbIvK9Dq4GovQxo23x8JgWvm/Qg==", "path": "microsoft.identitymodel.logging/8.0.1", "hashPath": "microsoft.identitymodel.logging.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uA2vpKqU3I2mBBEaeJAWPTjT9v1TZrGWKdgK6G5qJd03CLx83kdiqO9cmiK8/n1erkHzFBwU/RphP83aAe3i3g==", "path": "microsoft.identitymodel.protocols/8.0.1", "hashPath": "microsoft.identitymodel.protocols.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-AQDbfpL+yzuuGhO/mQhKNsp44pm5Jv8/BI4KiFXR7beVGZoSH35zMV3PrmcfvSTsyI6qrcR898NzUauD6SRigg==", "path": "microsoft.identitymodel.protocols.openidconnect/8.0.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-kDimB6Dkd3nkW2oZPDkMkVHfQt3IDqO5gL0oa8WVy3OP4uE8Ij+8TXnqg9TOd9ufjsY3IDiGz7pCUbnfL18tjg==", "path": "microsoft.identitymodel.tokens/8.0.1", "hashPath": "microsoft.identitymodel.tokens.8.0.1.nupkg.sha512"}, "Npgsql/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zu1nCRt0gWP/GR0reYgg0Bl5o8qyNV7mVAgzAbVLRiAd1CYXcf/9nrubPH0mt93u8iGTKmYqWaLVECEAcE6IfQ==", "path": "npgsql/9.0.0", "hashPath": "npgsql.9.0.0.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ObngKFRLMBAeMqQzK7SC0Q6WZtWw0imPmEkVPo12yLVF3fioz2TN+w0mhNMJ5cVd/sLB2u+jei0bmA9sDMtkMw==", "path": "npgsql.entityframeworkcore.postgresql/9.0.0", "hashPath": "npgsql.entityframeworkcore.postgresql.9.0.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GJw3bYkWpOgvN3tJo5X4lYUeIFA2HD293FPUhKmp7qxS+g5ywAb34Dnd3cDAFLkcMohy5XTpoaZ4uAHuw0uSPQ==", "path": "system.identitymodel.tokens.jwt/8.0.1", "hashPath": "system.identitymodel.tokens.jwt.8.0.1.nupkg.sha512"}, "System.Text.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-js7+qAu/9mQvnhA4EfGMZNEzXtJCDxgkgj8ohuxq/Qxv+R56G+ljefhiJHOxTNiw54q8vmABCWUwkMulNdlZ4A==", "path": "system.text.json/9.0.0", "hashPath": "system.text.json.9.0.0.nupkg.sha512"}, "MolySite.Identity/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "MolySite.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}