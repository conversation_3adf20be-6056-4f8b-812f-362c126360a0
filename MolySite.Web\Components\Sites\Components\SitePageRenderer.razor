@using MolySite.Shared.Dtos

<div class="site-page-renderer">
    @if (Page != null)
    {
        <!-- 页面标题 -->
        <div class="page-header py-8 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h1 class="text-3xl font-bold text-gray-900">@Page.Title</h1>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="page-content py-8">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="prose max-w-none">
                    @((MarkupString)ProcessContent(Page.Content))
                </div>
            </div>
        </div>
    }
    else
    {
        <!-- 页面不存在 -->
        <div class="page-not-found py-16 text-center">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h1 class="text-2xl font-bold text-gray-900 mb-4">页面未找到</h1>
                <p class="text-gray-600 mb-8">抱歉，您访问的页面不存在。</p>
                <a href="/" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    返回首页
                </a>
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public SitePageDto? Page { get; set; }
    [Parameter] public SiteDto Site { get; set; } = default!;

    private string ProcessContent(string content)
    {
        if (string.IsNullOrEmpty(content))
            return "";
            
        // 简单的内容处理 - 第3周将添加Markdown支持
        return content;
    }
}
