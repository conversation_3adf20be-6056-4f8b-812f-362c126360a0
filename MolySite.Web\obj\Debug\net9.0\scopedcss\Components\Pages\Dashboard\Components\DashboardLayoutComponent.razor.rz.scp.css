/* 现代化Dashboard组件样式 - 全新设计 */

/* 全局变量 */
:root[b-a02jn9xbct] {
    --dashboard-primary-blue: #2563eb;
    --dashboard-primary-emerald: #059669;
    --dashboard-primary-purple: #7c3aed;
    --dashboard-bg-light: #f8fafc;
    --dashboard-bg-white: #ffffff;
    --dashboard-border: #e2e8f0;
    --dashboard-text-primary: #1e293b;
    --dashboard-text-secondary: #64748b;
    --dashboard-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* 主题样式 */
.dashboard-theme-admin[b-a02jn9xbct] {
    --theme-primary: var(--dashboard-primary-blue);
    --theme-primary-light: #dbeafe;
    --theme-primary-dark: #1d4ed8;
}

.dashboard-theme-owner[b-a02jn9xbct] {
    --theme-primary: var(--dashboard-primary-emerald);
    --theme-primary-light: #d1fae5;
    --theme-primary-dark: #047857;
}

.dashboard-theme-editor[b-a02jn9xbct] {
    --theme-primary: var(--dashboard-primary-purple);
    --theme-primary-light: #ede9fe;
    --theme-primary-dark: #6d28d9;
}

.dashboard-theme-default[b-a02jn9xbct] {
    --theme-primary: #6b7280;
    --theme-primary-light: #f3f4f6;
    --theme-primary-dark: #374151;
}

/* 现代化动画效果 */
.fade-in[b-a02jn9xbct] {
    animation: fadeIn-b-a02jn9xbct 0.4s ease-out;
}

@keyframes fadeIn-b-a02jn9xbct {
    from {
        opacity: 0;
        transform: translateY(16px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-right[b-a02jn9xbct] {
    animation: slideInRight-b-a02jn9xbct 0.3s ease-out;
}

@keyframes slideInRight-b-a02jn9xbct {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 导航项现代化样式 */
.nav-item[b-a02jn9xbct] {
    position: relative;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-item:hover[b-a02jn9xbct] {
    background-color: #f8fafc;
    transform: translateX(2px);
}

.nav-item-active[b-a02jn9xbct] {
    background-color: #eff6ff;
    color: #1d4ed8;
    border-right: 3px solid #2563eb;
}

.nav-item-active[b-a02jn9xbct]::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
    border-radius: 0 2px 2px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .dashboard-sidebar[b-a02jn9xbct] {
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
    }
    
    .dashboard-sidebar.open[b-a02jn9xbct] {
        transform: translateX(0);
    }
}

/* 加载状态 */
.loading-skeleton[b-a02jn9xbct] {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-b-a02jn9xbct 1.5s infinite;
}

@keyframes loading-b-a02jn9xbct {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 按钮样式增强 */
.btn-dashboard[b-a02jn9xbct] {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm;
    transition: all 0.2s ease-in-out;
}

.btn-dashboard:hover[b-a02jn9xbct] {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 主题色彩变量 */
:root[b-a02jn9xbct] {
    --color-superadmin: #3b82f6;
    --color-siteowner: #10b981;
    --color-siteeditor: #8b5cf6;
    --color-superadmin-light: #dbeafe;
    --color-siteowner-light: #d1fae5;
    --color-siteeditor-light: #ede9fe;
}

/* 角色特定主题样式 */
.dashboard-theme-superadmin[b-a02jn9xbct] {
    --primary-color: var(--color-superadmin);
    --primary-light: var(--color-superadmin-light);
}

.dashboard-theme-siteowner[b-a02jn9xbct] {
    --primary-color: var(--color-siteowner);
    --primary-light: var(--color-siteowner-light);
}

.dashboard-theme-siteeditor[b-a02jn9xbct] {
    --primary-color: var(--color-siteeditor);
    --primary-light: var(--color-siteeditor-light);
}

/* 主题化的组件样式 */
.dashboard-theme-superadmin .btn-primary[b-a02jn9xbct] {
    background-color: var(--color-superadmin);
    border-color: var(--color-superadmin);
}

.dashboard-theme-superadmin .btn-primary:hover[b-a02jn9xbct] {
    background-color: #2563eb;
    border-color: #2563eb;
}

.dashboard-theme-siteowner .btn-primary[b-a02jn9xbct] {
    background-color: var(--color-siteowner);
    border-color: var(--color-siteowner);
}

.dashboard-theme-siteowner .btn-primary:hover[b-a02jn9xbct] {
    background-color: #059669;
    border-color: #059669;
}

.dashboard-theme-siteeditor .btn-primary[b-a02jn9xbct] {
    background-color: var(--color-siteeditor);
    border-color: var(--color-siteeditor);
}

.dashboard-theme-siteeditor .btn-primary:hover[b-a02jn9xbct] {
    background-color: #7c3aed;
    border-color: #7c3aed;
}

/* 角色徽章样式 */
.role-badge-superadmin[b-a02jn9xbct] {
    background-color: var(--color-superadmin-light);
    color: var(--color-superadmin);
    border: 1px solid var(--color-superadmin);
}

.role-badge-siteowner[b-a02jn9xbct] {
    background-color: var(--color-siteowner-light);
    color: var(--color-siteowner);
    border: 1px solid var(--color-siteowner);
}

.role-badge-siteeditor[b-a02jn9xbct] {
    background-color: var(--color-siteeditor-light);
    color: var(--color-siteeditor);
    border: 1px solid var(--color-siteeditor);
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .dashboard-dark[b-a02jn9xbct] {
        @apply bg-gray-900 text-white;
    }
    
    .dashboard-dark .nav-item[b-a02jn9xbct] {
        @apply text-gray-300 hover:bg-gray-800 hover:text-white;
    }
    
    .dashboard-dark .dashboard-card[b-a02jn9xbct] {
        @apply bg-gray-800 border-gray-700;
    }
}
