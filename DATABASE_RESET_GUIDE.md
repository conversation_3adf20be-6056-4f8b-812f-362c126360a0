# 🗄️ 数据库重置指南

## 📋 概述

本指南说明如何重置数据库并重新种子简化后的角色和用户数据。

## 🎯 简化后的数据结构

### 角色 (Roles)
1. **SuperAdmin** - 平台超级管理员
2. **User** - 普通用户（注册默认角色）
3. **Owner** - 网站所有者（网站级角色，用于兼容性）

### 用户账号 (Users)
1. **superadmin**
   - 邮箱: `<EMAIL>`
   - 密码: `SuperAdmin@2024!`
   - 角色: SuperAdmin
   - 权限: 平台管理权限

2. **user**
   - 邮箱: `<EMAIL>`
   - 密码: `User@2024!`
   - 角色: User
   - 权限: 创建网站权限

### 网站 (Sites)
1. **我的第一个网站**
   - 域名: `mysite.molysite.com`
   - 所有者: user
   - 状态: 已发布、活跃

## 🚀 重置方法

### 方法1: 使用API接口（推荐）

#### 1. 检查数据库状态
```bash
GET /api/DatabaseReset/status
```

#### 2. 重置数据库
```bash
POST /api/DatabaseReset/reset
```

**注意**: 此接口仅在开发环境中可用。

### 方法2: 使用代码

```csharp
// 在 Program.cs 或启动代码中
await DataSeeder.ResetAndSeedDataAsync(context, userManager, roleManager);
```

### 方法3: 使用EF Core命令

```bash
# 删除数据库
dotnet ef database drop --project MolySite.Identity

# 重新创建并迁移
dotnet ef database update --project MolySite.Identity
```

## 🔧 清理的数据

### 删除的角色
- ~~SiteOwner~~ (已合并到User)
- ~~SiteEditor~~ (已删除)
- ~~Editor~~ (已删除)
- ~~Follower~~ (已删除)
- ~~Viewer~~ (已删除)

### 删除的用户
- 除了 `superadmin` 和 `user` 之外的所有用户
- 包括之前的 `demo` 用户等

### 清理的关联数据
- 所有旧的网站用户角色关系
- 所有旧的网站数据
- 无效的权限关系

## 📊 重置后的数据统计

```json
{
  "users": 2,
  "roles": 3,
  "sites": 1,
  "siteUserRoles": 1
}
```

## 🔐 测试账号信息

### SuperAdmin 账号
- **用户名**: `superadmin`
- **邮箱**: `<EMAIL>`
- **密码**: `SuperAdmin@2024!`
- **功能**: 
  - 平台管理
  - 用户管理
  - 系统设置
  - 查看统计

### User 账号
- **用户名**: `user`
- **邮箱**: `<EMAIL>`
- **密码**: `User@2024!`
- **功能**:
  - 创建网站
  - 管理自己的网站
  - 网站内容编辑

## ⚠️ 注意事项

1. **仅开发环境**: 数据库重置功能仅在开发环境中可用
2. **数据丢失**: 重置将删除所有现有数据，请谨慎操作
3. **备份建议**: 在生产环境中，请先备份数据库
4. **权限检查**: 确保只有授权人员可以执行重置操作

## 🧪 验证重置结果

### 1. 检查用户登录
```bash
# 测试SuperAdmin登录
POST /api/Auth/login
{
  "userNameOrEmail": "superadmin",
  "password": "SuperAdmin@2024!"
}

# 测试User登录
POST /api/Auth/login
{
  "userNameOrEmail": "user",
  "password": "User@2024!"
}
```

### 2. 检查角色权限
```bash
# 检查SuperAdmin权限
GET /api/Permissions/platform

# 检查User权限
GET /api/Permissions/site/{siteId}
```

### 3. 检查网站数据
```bash
# 获取用户网站列表
GET /api/Sites/my-sites
```

## 🔄 回滚方案

如果需要回滚到重置前的状态：

1. 从备份恢复数据库
2. 或者重新运行旧版本的种子数据
3. 检查数据完整性

## 📞 支持

如果在重置过程中遇到问题，请检查：

1. 数据库连接字符串
2. 权限设置
3. 环境变量配置
4. 日志文件中的错误信息
