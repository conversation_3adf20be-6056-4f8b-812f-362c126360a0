using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MolySite.Core.Controllers;
using MolySite.Identity.Data;
using MolySite.Identity.Models;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace MolySite.API.Controllers
{
    /// <summary>
    /// 数据库重置控制器（仅用于开发环境）
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class DatabaseResetController : BaseApiController
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole<Guid>> _roleManager;
        private readonly ILogger<DatabaseResetController> _logger;
        private readonly IWebHostEnvironment _environment;

        public DatabaseResetController(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole<Guid>> roleManager,
            ILogger<DatabaseResetController> logger,
            IWebHostEnvironment environment)
        {
            _context = context;
            _userManager = userManager;
            _roleManager = roleManager;
            _logger = logger;
            _environment = environment;
        }

        /// <summary>
        /// 重置数据库并重新种子数据（仅开发环境）
        /// </summary>
        /// <returns>重置结果</returns>
        [HttpPost("reset")]
        [AllowAnonymous]
        public async Task<IActionResult> ResetDatabase()
        {
            // 只允许在开发环境中执行
            if (!_environment.IsDevelopment())
            {
                return BadRequest(new { message = "此操作仅允许在开发环境中执行" });
            }

            try
            {
                _logger.LogWarning("开始重置数据库...");

                // 重置并重新种子数据
                await DataSeeder.ResetAndSeedDataAsync(_context, _userManager, _roleManager);

                _logger.LogInformation("数据库重置完成");

                return Ok(new
                {
                    message = "数据库重置成功",
                    accounts = new
                    {
                        superAdmin = new
                        {
                            username = "superadmin",
                            email = "<EMAIL>",
                            password = "SuperAdmin@2024!",
                            role = "SuperAdmin"
                        },
                        normalUser = new
                        {
                            username = "user",
                            email = "<EMAIL>",
                            password = "User@2024!",
                            role = "User"
                        }
                    },
                    roles = new[] { "SuperAdmin", "User", "Owner" },
                    sites = new
                    {
                        demoSite = new
                        {
                            name = "我的第一个网站",
                            domain = "mysite.molysite.com",
                            owner = "user"
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置数据库时发生错误");
                return StatusCode(500, new { message = "重置数据库失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取当前数据库状态
        /// </summary>
        /// <returns>数据库状态</returns>
        [HttpGet("status")]
        [AllowAnonymous]
        public async Task<IActionResult> GetDatabaseStatus()
        {
            try
            {
                var userCount = await _userManager.Users.CountAsync();
                var roleCount = _roleManager.Roles.Count();
                var siteCount = await _context.Sites.CountAsync();
                var siteUserRoleCount = await _context.SiteUserRoles.CountAsync();

                return Ok(new
                {
                    environment = _environment.EnvironmentName,
                    statistics = new
                    {
                        users = userCount,
                        roles = roleCount,
                        sites = siteCount,
                        siteUserRoles = siteUserRoleCount
                    },
                    canReset = _environment.IsDevelopment()
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取数据库状态时发生错误");
                return StatusCode(500, new { message = "获取数据库状态失败", error = ex.Message });
            }
        }
    }
}
