using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MolySite.Identity.Data;
using MolySite.Identity.Models;

namespace MolySite.Identity.Services
{
    /// <summary>
    /// 网站服务实现
    /// </summary>
    public class SiteService : ISiteService
    {
        private readonly ApplicationDbContext _context;
        private readonly ISitePermissionService _permissionService;
        private readonly ILogger<SiteService> _logger;

        public SiteService(
            ApplicationDbContext context,
            ISitePermissionService permissionService,
            ILogger<SiteService> logger)
        {
            _context = context;
            _permissionService = permissionService;
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<Site> CreateSiteAsync(Site site, Guid ownerId)
        {
            try
            {
                // 检查域名是否可用
                if (!await IsDomainAvailableAsync(site.Domain))
                {
                    throw new InvalidOperationException($"域名 {site.Domain} 已被使用");
                }

                // 设置网站基本信息
                site.Id = Guid.NewGuid();
                site.OwnerId = ownerId;
                site.CreatedAt = DateTime.UtcNow;
                site.IsActive = true;

                // 添加到数据库
                _context.Sites.Add(site);

                // 为所有者创建网站角色
                var ownerRole = new SiteUserRole
                {
                    Id = Guid.NewGuid(),
                    UserId = ownerId,
                    SiteId = site.Id,
                    Role = SiteRoleType.Owner,
                    GrantedAt = DateTime.UtcNow,
                    GrantedBy = ownerId,
                    IsActive = true,
                    InvitationStatus = InvitationStatus.Accepted
                };

                _context.SiteUserRoles.Add(ownerRole);

                await _context.SaveChangesAsync();

                _logger.LogInformation("网站创建成功: {SiteId}, 所有者: {OwnerId}", site.Id, ownerId);
                return site;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建网站时发生错误: {SiteName}, 所有者: {OwnerId}", site.Name, ownerId);
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<Site?> GetSiteByIdAsync(Guid siteId)
        {
            try
            {
                return await _context.Sites
                    .Include(s => s.Owner)
                    .Include(s => s.SiteUsers)
                    .ThenInclude(su => su.User)
                    .FirstOrDefaultAsync(s => s.Id == siteId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取网站信息时发生错误: {SiteId}", siteId);
                return null;
            }
        }

        /// <inheritdoc/>
        public async Task<Site?> GetSiteByDomainAsync(string domain)
        {
            try
            {
                return await _context.Sites
                    .Include(s => s.Owner)
                    .FirstOrDefaultAsync(s => s.Domain == domain && s.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据域名获取网站时发生错误: {Domain}", domain);
                return null;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> UpdateSiteAsync(Site site)
        {
            try
            {
                var existingSite = await _context.Sites.FindAsync(site.Id);
                if (existingSite == null)
                {
                    return false;
                }

                // 检查域名是否可用（排除当前网站）
                if (existingSite.Domain != site.Domain && !await IsDomainAvailableAsync(site.Domain, site.Id))
                {
                    throw new InvalidOperationException($"域名 {site.Domain} 已被使用");
                }

                // 更新属性
                existingSite.Name = site.Name;
                existingSite.Domain = site.Domain;
                existingSite.Description = site.Description;
                existingSite.Template = site.Template;
                existingSite.CustomCss = site.CustomCss;
                existingSite.CustomJavaScript = site.CustomJavaScript;
                existingSite.SiteConfig = site.SiteConfig;
                existingSite.FaviconUrl = site.FaviconUrl;
                existingSite.LogoUrl = site.LogoUrl;
                existingSite.PrimaryColor = site.PrimaryColor;
                existingSite.SecondaryColor = site.SecondaryColor;
                existingSite.FooterText = site.FooterText;
                existingSite.SocialMediaLinks = site.SocialMediaLinks;
                existingSite.NavigationMenu = site.NavigationMenu;
                existingSite.AnalyticsCode = site.AnalyticsCode;
                existingSite.EnableComments = site.EnableComments;
                existingSite.EnableSEO = site.EnableSEO;
                existingSite.SiteKeywords = site.SiteKeywords;
                existingSite.LastModifiedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("网站更新成功: {SiteId}", site.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新网站时发生错误: {SiteId}", site.Id);
                return false;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> DeleteSiteAsync(Guid siteId)
        {
            try
            {
                var site = await _context.Sites.FindAsync(siteId);
                if (site == null)
                {
                    return false;
                }

                // 软删除：设置为非活跃状态
                site.IsActive = false;
                site.LastModifiedAt = DateTime.UtcNow;

                // 同时禁用所有相关的用户角色
                var siteRoles = await _context.SiteUserRoles
                    .Where(sr => sr.SiteId == siteId)
                    .ToListAsync();

                foreach (var role in siteRoles)
                {
                    role.IsActive = false;
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("网站删除成功: {SiteId}", siteId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除网站时发生错误: {SiteId}", siteId);
                return false;
            }
        }

        /// <inheritdoc/>
        public async Task<List<Site>> GetUserSitesAsync(Guid userId)
        {
            try
            {
                // 获取用户拥有的网站和有权限访问的网站
                var userSites = await _context.SiteUserRoles
                    .Where(sur => sur.UserId == userId && sur.IsActive)
                    .Include(sur => sur.Site)
                    .ThenInclude(s => s.Owner)
                    .Select(sur => sur.Site)
                    .Where(s => s.IsActive)
                    .Distinct()
                    .OrderByDescending(s => s.CreatedAt)
                    .ToListAsync();

                return userSites;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户网站时发生错误: {UserId}", userId);
                return new List<Site>();
            }
        }

        /// <inheritdoc/>
        public async Task<List<Site>> GetAllSitesAsync()
        {
            try
            {
                return await _context.Sites
                    .Include(s => s.Owner)
                    .Where(s => s.IsActive)
                    .OrderByDescending(s => s.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有网站时发生错误");
                return new List<Site>();
            }
        }

        /// <inheritdoc/>
        public async Task<bool> IsDomainAvailableAsync(string domain, Guid? excludeSiteId = null)
        {
            try
            {
                var query = _context.Sites.Where(s => s.Domain == domain && s.IsActive);
                
                if (excludeSiteId.HasValue)
                {
                    query = query.Where(s => s.Id != excludeSiteId.Value);
                }

                return !await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查域名可用性时发生错误: {Domain}", domain);
                return false;
            }
        }





        /// <inheritdoc/>
        public async Task<List<SiteUserRole>> GetSiteUsersAsync(Guid siteId)
        {
            try
            {
                return await _context.SiteUserRoles
                    .Include(sur => sur.User)
                    .Include(sur => sur.GrantedByUser)
                    .Where(sur => sur.SiteId == siteId && sur.IsActive)
                    .OrderBy(sur => sur.Role)
                    .ThenBy(sur => sur.GrantedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取网站用户时发生错误: {SiteId}", siteId);
                return new List<SiteUserRole>();
            }
        }

        /// <inheritdoc/>
        public async Task<bool> PublishSiteAsync(Guid siteId)
        {
            try
            {
                var site = await _context.Sites.FindAsync(siteId);
                if (site == null)
                {
                    return false;
                }

                site.IsPublished = true;
                site.LastModifiedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("网站发布成功: {SiteId}", siteId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发布网站时发生错误: {SiteId}", siteId);
                return false;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> UnpublishSiteAsync(Guid siteId)
        {
            try
            {
                var site = await _context.Sites.FindAsync(siteId);
                if (site == null)
                {
                    return false;
                }

                site.IsPublished = false;
                site.LastModifiedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("网站取消发布成功: {SiteId}", siteId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消发布网站时发生错误: {SiteId}", siteId);
                return false;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> UpdateSiteSubscriptionAsync(Guid siteId, string planName, DateTime? expiryDate)
        {
            try
            {
                var site = await _context.Sites.FindAsync(siteId);
                if (site == null)
                {
                    return false;
                }

                site.SubscriptionPlan = planName;
                site.SubscriptionExpiresAt = expiryDate;
                site.LastModifiedAt = DateTime.UtcNow;

                // 根据订阅计划更新存储限制
                site.MaxStorageMB = planName switch
                {
                    "Free" => 100,
                    "Basic" => 1024,
                    "Premium" => 10240,
                    "Enterprise" => int.MaxValue,
                    _ => 100
                };

                await _context.SaveChangesAsync();

                _logger.LogInformation("网站订阅更新成功: {SiteId}, {PlanName}", siteId, planName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新网站订阅时发生错误: {SiteId}", siteId);
                return false;
            }
        }

        /// <inheritdoc/>
        public async Task<SiteStatistics> GetSiteStatisticsAsync(Guid siteId)
        {
            try
            {
                var site = await _context.Sites
                    .Include(s => s.SiteUsers)
                    .FirstOrDefaultAsync(s => s.Id == siteId);

                if (site == null)
                {
                    return new SiteStatistics();
                }

                return new SiteStatistics
                {
                    PageViews = 0, // TODO: 实现页面访问统计
                    UniqueVisitors = 0, // TODO: 实现独立访客统计
                    LastVisit = DateTime.UtcNow, // TODO: 实现最后访问时间
                    TotalPages = 1, // TODO: 实现页面数量统计
                    TotalUsers = site.SiteUsers.Count(su => su.IsActive)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取网站统计时发生错误: {SiteId}", siteId);
                return new SiteStatistics();
            }
        }

        /// <inheritdoc/>
        public async Task<StorageUsage> GetSiteStorageUsageAsync(Guid siteId)
        {
            try
            {
                var site = await _context.Sites.FindAsync(siteId);
                if (site == null)
                {
                    return new StorageUsage();
                }

                return new StorageUsage
                {
                    UsedMB = site.UsedStorageMB,
                    MaxMB = site.MaxStorageMB
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取网站存储使用情况时发生错误: {SiteId}", siteId);
                return new StorageUsage();
            }
        }
    }
}
