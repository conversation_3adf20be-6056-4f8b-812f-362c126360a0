using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using System.Net.Http.Json;
using System.Text.Json;
using MolySite.Identity.Data;
using MolySite.Identity.Models;
using MolySite.Shared.Dtos;
using Xunit;

namespace MolySite.Tests
{
    /// <summary>
    /// API集成测试
    /// </summary>
    public class ApiIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public ApiIntegrationTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory.WithWebHostBuilder(builder =>
            {
                builder.ConfigureServices(services =>
                {
                    // 移除真实的数据库上下文
                    var descriptor = services.SingleOrDefault(
                        d => d.ServiceType == typeof(DbContextOptions<ApplicationDbContext>));
                    if (descriptor != null)
                        services.Remove(descriptor);

                    // 添加内存数据库
                    services.AddDbContext<ApplicationDbContext>(options =>
                    {
                        options.UseInMemoryDatabase("TestDb");
                    });
                });
            });

            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task Register_ShouldCreateUserWithUserRole()
        {
            // Arrange
            var registerDto = new RegisterDto
            {
                UserName = "testuser",
                Email = "<EMAIL>",
                Password = "Test123!@#",
                ConfirmPassword = "Test123!@#"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/auth/register", registerDto);

            // Assert
            response.EnsureSuccessStatusCode();
            
            // 验证用户是否创建成功且角色正确
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var user = await context.Users.FirstOrDefaultAsync(u => u.Email == registerDto.Email);
            
            Assert.NotNull(user);
            Assert.Equal(PlatformRole.User, user.PlatformRole);
            Assert.Contains("User", user.PlatformRoles);
        }

        [Fact]
        public async Task Login_ShouldReturnTokenWithCorrectRole()
        {
            // Arrange
            await SeedTestUser();
            var loginDto = new LoginDto
            {
                UserNameOrEmail = "<EMAIL>",
                Password = "Test123!@#"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/auth/login", loginDto);

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var loginResult = JsonSerializer.Deserialize<LoginResultDto>(content, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            Assert.NotNull(loginResult);
            Assert.NotEmpty(loginResult.AccessToken);
            Assert.Equal("User", loginResult.Role);
        }

        [Fact]
        public async Task GetSiteFollowerCount_ShouldReturnZeroForNewSite()
        {
            // Arrange
            var siteId = await CreateTestSite();

            // Act
            var response = await _client.GetAsync($"/api/sites/{siteId}/follower-count");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var count = JsonSerializer.Deserialize<int>(content);
            
            Assert.Equal(0, count);
        }

        private async Task SeedTestUser()
        {
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            
            var user = new ApplicationUser
            {
                Id = Guid.NewGuid(),
                UserName = "testuser",
                Email = "<EMAIL>",
                PlatformRole = PlatformRole.User,
                PlatformRoles = new List<string> { "User" },
                EmailConfirmed = true,
                SecurityStamp = Guid.NewGuid().ToString()
            };

            // 这里需要正确设置密码哈希
            // 在实际测试中，你可能需要使用UserManager来创建用户
            context.Users.Add(user);
            await context.SaveChangesAsync();
        }

        private async Task<Guid> CreateTestSite()
        {
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            
            var user = await context.Users.FirstAsync();
            var site = new Site
            {
                Id = Guid.NewGuid(),
                Name = "测试网站",
                Domain = "test.example.com",
                OwnerId = user.Id,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            context.Sites.Add(site);
            await context.SaveChangesAsync();
            
            return site.Id;
        }
    }

    /// <summary>
    /// 登录结果DTO（用于测试）
    /// </summary>
    public class LoginResultDto
    {
        public string AccessToken { get; set; } = string.Empty;
        public string RefreshToken { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public Guid UserId { get; set; }
        public DateTime ExpiresAt { get; set; }
    }
}
