# 🚀 用户网站前端实现方案

## 📋 方案概述

**推荐方案**: **智能混合架构** - 在现有MolySite.Web基础上，通过智能路由和模块化设计实现用户网站前端。

## 🏗️ 架构设计

### 1. 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    MolySite.Web (统一入口)                    │
├─────────────────────────────────────────────────────────────┤
│                    Smart Router (智能路由器)                  │
│  ┌─────────────────────┐    ┌─────────────────────────────┐  │
│  │   Platform Module   │    │      Sites Module          │  │
│  │   (平台前端模块)      │    │     (用户网站前端模块)       │  │
│  │                     │    │                             │  │
│  │ ├── PublicLayout    │    │ ├── SiteLayoutBase         │  │
│  │ ├── DashboardLayout │    │ ├── BlogTemplate           │  │
│  │ └── MainLayout      │    │ ├── BusinessTemplate       │  │
│  │                     │    │ ├── PortfolioTemplate      │  │
│  └─────────────────────┘    │ └── CustomTemplate         │  │
│                              └─────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    Shared Services & Components              │
│  ├── Template Engine    ├── Content Service    ├── SEO      │
│  ├── Theme System      ├── Media Service       ├── Analytics│
│  └── Cache Manager     └── Security Service    └── CDN      │
└─────────────────────────────────────────────────────────────┘
```

### 2. 路由策略

```csharp
// 智能路由判断逻辑
public class SmartRouter
{
    public RouteResult DetermineRoute(HttpContext context)
    {
        var host = context.Request.Host.Host;
        var subdomain = ExtractSubdomain(host);
        
        return subdomain switch
        {
            null or "www" or "" => new PlatformRoute(),
            "api" => new ApiRoute(),
            _ => new SiteRoute(subdomain)
        };
    }
}

// 路由示例
// molysite.com          -> Platform Frontend
// www.molysite.com      -> Platform Frontend  
// api.molysite.com      -> API Backend
// mysite.molysite.com   -> Sites Frontend (mysite)
// blog.molysite.com     -> Sites Frontend (blog)
```

## 🎨 模块化设计

### 1. Platform Module (平台前端模块)

**位置**: `Components/Platform/`
**职责**: MolySite平台本身的前端界面

```
Components/Platform/
├── Layouts/
│   ├── PublicLayout.razor      # 平台营销页面
│   ├── DashboardLayout.razor   # 平台管理界面
│   └── MainLayout.razor        # 开发测试页面
├── Pages/
│   ├── Home/                   # 首页相关
│   ├── Auth/                   # 认证相关
│   └── Dashboard/              # 管理相关
└── Components/
    ├── Navigation/             # 平台导航
    ├── Marketing/              # 营销组件
    └── Management/             # 管理组件
```

### 2. Sites Module (用户网站前端模块)

**位置**: `Components/Sites/`
**职责**: 用户创建的网站前端展示

```
Components/Sites/
├── Layouts/
│   ├── SiteLayoutBase.razor    # 网站基础布局
│   └── Templates/              # 模板布局
│       ├── BlogTemplate.razor
│       ├── BusinessTemplate.razor
│       ├── PortfolioTemplate.razor
│       └── CustomTemplate.razor
├── Pages/
│   ├── SiteHome.razor          # 网站首页
│   ├── SitePage.razor          # 网站页面
│   ├── BlogPost.razor          # 博客文章
│   └── SiteError.razor         # 网站错误页
├── Components/
│   ├── SiteHeader.razor        # 网站头部
│   ├── SiteFooter.razor        # 网站底部
│   ├── SiteNavigation.razor    # 网站导航
│   ├── ContentRenderer.razor   # 内容渲染器
│   └── TemplateComponents/     # 模板组件
└── Services/
    ├── SiteRenderingService.cs # 网站渲染服务
    ├── TemplateEngine.cs       # 模板引擎
    └── ThemeService.cs         # 主题服务
```

## 🔧 核心技术实现

### 1. 智能路由器实现

```csharp
// Middleware/SmartRoutingMiddleware.cs
public class SmartRoutingMiddleware
{
    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        var routeResult = _smartRouter.DetermineRoute(context);
        
        switch (routeResult.Type)
        {
            case RouteType.Platform:
                // 设置平台前端上下文
                context.Items["RouteType"] = "Platform";
                break;
                
            case RouteType.Site:
                // 设置网站前端上下文
                context.Items["RouteType"] = "Site";
                context.Items["SiteDomain"] = routeResult.SiteDomain;
                
                // 预加载网站信息
                var site = await _siteService.GetSiteByDomainAsync(routeResult.SiteDomain);
                context.Items["SiteInfo"] = site;
                break;
        }
        
        await next(context);
    }
}
```

### 2. 动态布局选择器

```csharp
// Services/LayoutSelector.cs
public class LayoutSelector
{
    public Type SelectLayout(HttpContext context)
    {
        var routeType = context.Items["RouteType"]?.ToString();
        
        return routeType switch
        {
            "Platform" => SelectPlatformLayout(context),
            "Site" => SelectSiteLayout(context),
            _ => typeof(PublicLayout)
        };
    }
    
    private Type SelectSiteLayout(HttpContext context)
    {
        var site = context.Items["SiteInfo"] as SiteDto;
        
        return site?.Template switch
        {
            "Blog" => typeof(BlogTemplate),
            "Business" => typeof(BusinessTemplate),
            "Portfolio" => typeof(PortfolioTemplate),
            "Custom" => typeof(CustomTemplate),
            _ => typeof(SiteLayoutBase)
        };
    }
}
```

### 3. 模板引擎

```csharp
// Services/TemplateEngine.cs
public class TemplateEngine
{
    public async Task<string> RenderTemplateAsync(SiteDto site, PageDto page)
    {
        var template = await GetTemplateAsync(site.Template);
        var context = CreateRenderContext(site, page);
        
        // 应用自定义CSS
        if (!string.IsNullOrEmpty(site.CustomCss))
        {
            context.CustomStyles = site.CustomCss;
        }
        
        // 应用自定义JavaScript
        if (!string.IsNullOrEmpty(site.CustomJavaScript))
        {
            context.CustomScripts = site.CustomJavaScript;
        }
        
        return await template.RenderAsync(context);
    }
}
```

## 🎨 模板系统设计

### 1. 模板基类

```razor
@* Components/Sites/Layouts/SiteLayoutBase.razor *@
@inherits LayoutComponentBase
@inject ISiteRenderingService SiteService
@inject ITemplateEngine TemplateEngine

<div class="site-layout @GetThemeClass()" data-site-id="@Site.Id">
    <!-- 动态注入自定义CSS -->
    @if (!string.IsNullOrEmpty(Site.CustomCss))
    {
        <style>@((MarkupString)Site.CustomCss)</style>
    }
    
    <!-- 网站头部 -->
    <SiteHeader Site="@Site" />
    
    <!-- 主要内容 -->
    <main class="site-main">
        @Body
    </main>
    
    <!-- 网站底部 -->
    <SiteFooter Site="@Site" />
    
    <!-- 动态注入自定义JavaScript -->
    @if (!string.IsNullOrEmpty(Site.CustomJavaScript))
    {
        <script>@((MarkupString)Site.CustomJavaScript)</script>
    }
</div>

@code {
    [Parameter] public SiteDto Site { get; set; } = default!;
    
    protected override async Task OnInitializedAsync()
    {
        Site = await SiteService.GetCurrentSiteAsync();
    }
    
    private string GetThemeClass()
    {
        return $"theme-{Site.Template.ToLower()}";
    }
}
```

### 2. 具体模板实现

```razor
@* Components/Sites/Layouts/Templates/BlogTemplate.razor *@
@inherits SiteLayoutBase

<div class="blog-template">
    <!-- 博客特有的头部 -->
    <header class="blog-header">
        <div class="container">
            <h1 class="blog-title">@Site.Name</h1>
            <p class="blog-description">@Site.Description</p>
        </div>
    </header>
    
    <!-- 博客导航 -->
    <nav class="blog-nav">
        <BlogNavigation Site="@Site" />
    </nav>
    
    <!-- 博客内容 -->
    <div class="blog-content">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    @Body
                </div>
                <div class="col-md-4">
                    <BlogSidebar Site="@Site" />
                </div>
            </div>
        </div>
    </div>
</div>
```

## 🔄 内容管理系统

### 1. 内容模型

```csharp
// Models/Content/
public class Page
{
    public Guid Id { get; set; }
    public Guid SiteId { get; set; }
    public string Title { get; set; }
    public string Slug { get; set; }
    public string Content { get; set; }
    public string Template { get; set; }
    public bool IsPublished { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? PublishedAt { get; set; }
}

public class Post
{
    public Guid Id { get; set; }
    public Guid SiteId { get; set; }
    public string Title { get; set; }
    public string Slug { get; set; }
    public string Content { get; set; }
    public string Excerpt { get; set; }
    public string FeaturedImage { get; set; }
    public List<string> Tags { get; set; }
    public bool IsPublished { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? PublishedAt { get; set; }
}
```

### 2. 内容渲染器

```razor
@* Components/Sites/Components/ContentRenderer.razor *@
@inject IContentService ContentService
@inject IMarkdownService MarkdownService

<div class="content-renderer">
    @switch (ContentType)
    {
        case "markdown":
            @((MarkupString)MarkdownService.ToHtml(Content))
            break;
        case "html":
            @((MarkupString)Content)
            break;
        case "rich-text":
            <RichTextRenderer Content="@Content" />
            break;
        default:
            <p>@Content</p>
            break;
    }
</div>

@code {
    [Parameter] public string Content { get; set; } = string.Empty;
    [Parameter] public string ContentType { get; set; } = "html";
}
```

## 🎯 SEO和性能优化

### 1. SEO优化

```csharp
// Services/SeoService.cs
public class SeoService
{
    public SeoMetadata GenerateSeoMetadata(SiteDto site, PageDto page)
    {
        return new SeoMetadata
        {
            Title = $"{page.Title} - {site.Name}",
            Description = page.Excerpt ?? site.Description,
            Keywords = page.Tags?.Join(", "),
            OgTitle = page.Title,
            OgDescription = page.Excerpt,
            OgImage = page.FeaturedImage ?? site.LogoUrl,
            CanonicalUrl = $"https://{site.Domain}/{page.Slug}",
            JsonLd = GenerateJsonLd(site, page)
        };
    }
}
```

### 2. 缓存策略

```csharp
// Services/CacheService.cs
public class SiteRenderingCacheService
{
    public async Task<string> GetCachedPageAsync(string siteId, string pageSlug)
    {
        var cacheKey = $"site:{siteId}:page:{pageSlug}";
        return await _cache.GetStringAsync(cacheKey);
    }
    
    public async Task SetCachedPageAsync(string siteId, string pageSlug, string content)
    {
        var cacheKey = $"site:{siteId}:page:{pageSlug}";
        var options = new DistributedCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1),
            SlidingExpiration = TimeSpan.FromMinutes(15)
        };
        
        await _cache.SetStringAsync(cacheKey, content, options);
    }
}
```

## 📱 响应式设计

### 1. 移动优先CSS框架

```css
/* wwwroot/css/site-templates.css */
.site-layout {
    /* 移动优先设计 */
    @apply min-h-screen flex flex-col;
}

.blog-template {
    /* 博客模板样式 */
    .blog-header {
        @apply bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16;
    }
    
    .blog-content {
        @apply flex-1 py-8;
    }
}

.business-template {
    /* 商业模板样式 */
    .hero-section {
        @apply bg-gray-900 text-white py-20;
    }
    
    .features-section {
        @apply py-16 bg-gray-50;
    }
}

/* 响应式断点 */
@media (max-width: 768px) {
    .site-layout {
        @apply text-sm;
    }
}
```

## 🔐 安全考虑

### 1. 内容安全策略

```csharp
// Services/SecurityService.cs
public class ContentSecurityService
{
    public string SanitizeUserContent(string content)
    {
        // 清理用户输入的HTML内容
        return _htmlSanitizer.Sanitize(content);
    }
    
    public string ValidateCustomCss(string css)
    {
        // 验证和清理用户自定义CSS
        return _cssSanitizer.Sanitize(css);
    }
    
    public string ValidateCustomJavaScript(string js)
    {
        // 验证用户自定义JavaScript（可能需要沙箱）
        return _jsSanitizer.Sanitize(js);
    }
}
```

## 📊 分析和监控

### 1. 网站分析

```csharp
// Services/AnalyticsService.cs
public class SiteAnalyticsService
{
    public async Task TrackPageViewAsync(Guid siteId, string pageSlug, string userAgent, string ipAddress)
    {
        var pageView = new PageView
        {
            SiteId = siteId,
            PageSlug = pageSlug,
            UserAgent = userAgent,
            IpAddress = HashIpAddress(ipAddress), // 隐私保护
            Timestamp = DateTime.UtcNow
        };
        
        await _analyticsRepository.AddPageViewAsync(pageView);
    }
}
```

## 🚀 部署和扩展

### 1. 容器化部署

```dockerfile
# Dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["MolySite.Web/MolySite.Web.csproj", "MolySite.Web/"]
RUN dotnet restore "MolySite.Web/MolySite.Web.csproj"
COPY . .
WORKDIR "/src/MolySite.Web"
RUN dotnet build "MolySite.Web.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "MolySite.Web.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "MolySite.Web.dll"]
```

### 2. CDN集成

```csharp
// Services/CdnService.cs
public class CdnService
{
    public string GetCdnUrl(string assetPath, Guid siteId)
    {
        return $"https://cdn.molysite.com/sites/{siteId}/{assetPath}";
    }
    
    public async Task UploadSiteAssetAsync(Guid siteId, string fileName, Stream fileStream)
    {
        var cdnPath = $"sites/{siteId}/{fileName}";
        await _cdnClient.UploadAsync(cdnPath, fileStream);
    }
}
```

## 🎉 实施路线图

### 阶段1: 基础架构 (2-3周)
- ✅ 实现智能路由器
- ✅ 创建Sites模块基础结构
- ✅ 实现基础模板系统

### 阶段2: 核心功能 (3-4周)
- ✅ 实现内容管理系统
- ✅ 创建模板引擎
- ✅ 实现自定义CSS/JS支持

### 阶段3: 高级功能 (2-3周)
- ✅ SEO优化
- ✅ 缓存系统
- ✅ 分析和监控

### 阶段4: 优化和扩展 (持续)
- ✅ 性能优化
- ✅ 更多模板
- ✅ 高级功能

这个方案平衡了技术复杂度和实现可行性，既保持了现有架构的优势，又为用户网站前端提供了完整的解决方案。🚀
