@using Microsoft.AspNetCore.Components.Routing
@using MolySite.Shared.Dtos
@using MolySite.Web.Components.Sites.Layouts
@using MolySite.Web.Components.Sites.Components
@inject IHttpContextAccessor HttpContextAccessor
@inject ISitePageService SitePageService
@inject ILogger<SiteRouteHandler> Logger

@if (_currentSite != null)
{
    <LayoutView Layout="@typeof(SiteLayout)">
        <SitePageRenderer Page="@_currentPage" Site="@_currentSite" />
    </LayoutView>
}
else if (_siteNotFound)
{
    <LayoutView Layout="@typeof(SiteLayout)">
        <SiteNotFound Domain="@_siteDomain" />
    </LayoutView>
}

@code {
    [Parameter] public RouteData RouteData { get; set; } = default!;
    
    private SiteDto? _currentSite;
    private SitePageDto? _currentPage;
    private bool _siteNotFound;
    private string _siteDomain = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            var httpContext = HttpContextAccessor.HttpContext;
            if (httpContext == null)
            {
                Logger.LogError("HttpContext为空");
                return;
            }

            _currentSite = httpContext.Items["CurrentSite"] as SiteDto;
            _siteNotFound = httpContext.Items["SiteNotFound"] as bool? ?? false;
            _siteDomain = httpContext.Items["SiteDomain"] as string ?? string.Empty;
            
            Logger.LogDebug("SiteRouteHandler初始化: Site={SiteName}, NotFound={NotFound}, Domain={Domain}", 
                _currentSite?.Name, _siteNotFound, _siteDomain);

            if (_currentSite != null)
            {
                // 获取当前页面
                var path = httpContext.Request.Path.Value?.TrimStart('/') ?? "";
                _currentPage = await GetPageByPathAsync(path);
                
                Logger.LogDebug("页面路径: {Path}, 找到页面: {PageTitle}", path, _currentPage?.Title);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "SiteRouteHandler初始化异常");
        }
    }

    private async Task<SitePageDto?> GetPageByPathAsync(string path)
    {
        try
        {
            if (string.IsNullOrEmpty(path))
            {
                // 首页
                return await SitePageService.GetHomePageAsync(_currentSite!.Id);
            }
            
            // 根据slug查找页面
            return await SitePageService.GetPageBySlugAsync(_currentSite!.Id, path);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "获取页面失败: SiteId={SiteId}, Path={Path}", _currentSite?.Id, path);
            return null;
        }
    }
}
