using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MolySite.Identity.Data;
using MolySite.Identity.Models;
using MolySite.Shared.Dtos;

namespace MolySite.Core.Services
{
    /// <summary>
    /// 简化角色体系迁移服务
    /// </summary>
    public class SimplifyRoleSystemMigrationService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<SimplifyRoleSystemMigrationService> _logger;

        public SimplifyRoleSystemMigrationService(
            ApplicationDbContext context,
            ILogger<SimplifyRoleSystemMigrationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 执行简化角色体系迁移
        /// </summary>
        public async Task<bool> ExecuteMigrationAsync()
        {
            try
            {
                _logger.LogInformation("开始执行简化角色体系迁移...");

                using var transaction = await _context.Database.BeginTransactionAsync();

                try
                {
                    // 1. 清理非 Owner 角色的 SiteUserRoles
                    await CleanupNonOwnerRolesAsync();

                    // 2. 确保所有网站都有 Owner
                    await EnsureAllSitesHaveOwnerAsync();

                    // 3. 清理孤立的数据
                    await CleanupOrphanedDataAsync();

                    // 4. 更新用户平台角色
                    await UpdateUserPlatformRolesAsync();

                    // 5. 验证数据完整性
                    await ValidateDataIntegrityAsync();

                    await transaction.CommitAsync();

                    _logger.LogInformation("简化角色体系迁移完成");
                    return true;
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    _logger.LogError(ex, "迁移过程中发生错误，已回滚");
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行简化角色体系迁移失败");
                return false;
            }
        }

        /// <summary>
        /// 清理非 Owner 角色的记录
        /// </summary>
        private async Task CleanupNonOwnerRolesAsync()
        {
            _logger.LogInformation("清理非 Owner 角色记录...");

            // 删除所有非 Owner 角色的记录
            var nonOwnerRoles = await _context.SiteUserRoles
                .Where(sur => sur.Role != SiteRoleType.Owner)
                .ToListAsync();

            if (nonOwnerRoles.Any())
            {
                _context.SiteUserRoles.RemoveRange(nonOwnerRoles);
                await _context.SaveChangesAsync();

                _logger.LogInformation("已删除 {Count} 条非 Owner 角色记录", nonOwnerRoles.Count);
            }
        }

        /// <summary>
        /// 确保所有网站都有 Owner
        /// </summary>
        private async Task EnsureAllSitesHaveOwnerAsync()
        {
            _logger.LogInformation("确保所有网站都有 Owner...");

            var sitesWithoutOwner = await _context.Sites
                .Where(s => !_context.SiteUserRoles.Any(sur => 
                    sur.SiteId == s.Id && 
                    sur.Role == SiteRoleType.Owner && 
                    sur.IsActive))
                .ToListAsync();

            foreach (var site in sitesWithoutOwner)
            {
                // 为网站创建者分配 Owner 角色
                var ownerRole = new SiteUserRole
                {
                    Id = Guid.NewGuid(),
                    UserId = site.OwnerId,
                    SiteId = site.Id,
                    Role = SiteRoleType.Owner,
                    GrantedAt = DateTime.UtcNow,
                    GrantedBy = site.OwnerId,
                    IsActive = true
                };

                _context.SiteUserRoles.Add(ownerRole);
            }

            if (sitesWithoutOwner.Any())
            {
                await _context.SaveChangesAsync();
                _logger.LogInformation("为 {Count} 个网站分配了 Owner 角色", sitesWithoutOwner.Count);
            }
        }

        /// <summary>
        /// 清理孤立的数据
        /// </summary>
        private async Task CleanupOrphanedDataAsync()
        {
            _logger.LogInformation("清理孤立数据...");

            // 清理引用不存在用户的角色记录
            var orphanedUserRoles = await _context.SiteUserRoles
                .Where(sur => !_context.Users.Any(u => u.Id == sur.UserId))
                .ToListAsync();

            if (orphanedUserRoles.Any())
            {
                _context.SiteUserRoles.RemoveRange(orphanedUserRoles);
                _logger.LogInformation("删除了 {Count} 条引用不存在用户的角色记录", orphanedUserRoles.Count);
            }

            // 清理引用不存在网站的角色记录
            var orphanedSiteRoles = await _context.SiteUserRoles
                .Where(sur => !_context.Sites.Any(s => s.Id == sur.SiteId))
                .ToListAsync();

            if (orphanedSiteRoles.Any())
            {
                _context.SiteUserRoles.RemoveRange(orphanedSiteRoles);
                _logger.LogInformation("删除了 {Count} 条引用不存在网站的角色记录", orphanedSiteRoles.Count);
            }

            if (orphanedUserRoles.Any() || orphanedSiteRoles.Any())
            {
                await _context.SaveChangesAsync();
            }
        }

        /// <summary>
        /// 更新用户平台角色
        /// </summary>
        private async Task UpdateUserPlatformRolesAsync()
        {
            _logger.LogInformation("更新用户平台角色...");

            // 将所有非 SuperAdmin 的用户角色统一为 User
            var usersToUpdate = await _context.Users
                .Where(u => u.PlatformRole != PlatformRole.SuperAdmin && u.PlatformRole != PlatformRole.User)
                .ToListAsync();

            foreach (var user in usersToUpdate)
            {
                user.PlatformRole = PlatformRole.User;
            }

            if (usersToUpdate.Any())
            {
                await _context.SaveChangesAsync();
                _logger.LogInformation("更新了 {Count} 个用户的平台角色为 User", usersToUpdate.Count);
            }
        }

        /// <summary>
        /// 验证数据完整性
        /// </summary>
        private async Task ValidateDataIntegrityAsync()
        {
            _logger.LogInformation("验证数据完整性...");

            // 检查是否有网站没有 Owner
            var sitesWithoutOwner = await _context.Sites
                .Where(s => !_context.SiteUserRoles.Any(sur => 
                    sur.SiteId == s.Id && 
                    sur.Role == SiteRoleType.Owner && 
                    sur.IsActive))
                .CountAsync();

            if (sitesWithoutOwner > 0)
            {
                throw new InvalidOperationException($"发现 {sitesWithoutOwner} 个网站没有 Owner");
            }

            // 检查是否有非 Owner 角色
            var nonOwnerRoles = await _context.SiteUserRoles
                .Where(sur => sur.Role != SiteRoleType.Owner)
                .CountAsync();

            if (nonOwnerRoles > 0)
            {
                throw new InvalidOperationException($"发现 {nonOwnerRoles} 条非 Owner 角色记录");
            }

            // 统计迁移结果
            var totalUsers = await _context.Users.CountAsync();
            var superAdmins = await _context.Users.CountAsync(u => u.PlatformRole == PlatformRole.SuperAdmin);
            var regularUsers = await _context.Users.CountAsync(u => u.PlatformRole == PlatformRole.User);
            var totalSites = await _context.Sites.CountAsync();
            var totalOwnerRoles = await _context.SiteUserRoles.CountAsync(sur => sur.Role == SiteRoleType.Owner);

            _logger.LogInformation("迁移完成统计:");
            _logger.LogInformation("- 总用户数: {TotalUsers}", totalUsers);
            _logger.LogInformation("- SuperAdmin: {SuperAdmins}", superAdmins);
            _logger.LogInformation("- 普通用户: {RegularUsers}", regularUsers);
            _logger.LogInformation("- 总网站数: {TotalSites}", totalSites);
            _logger.LogInformation("- Owner 角色数: {TotalOwnerRoles}", totalOwnerRoles);
        }

        /// <summary>
        /// 检查是否需要执行迁移
        /// </summary>
        public async Task<bool> IsMigrationNeededAsync()
        {
            try
            {
                // 检查是否有非 Owner 角色
                var hasNonOwnerRoles = await _context.SiteUserRoles
                    .AnyAsync(sur => sur.Role != SiteRoleType.Owner);

                // 检查是否有网站没有 Owner
                var sitesWithoutOwner = await _context.Sites
                    .AnyAsync(s => !_context.SiteUserRoles.Any(sur => 
                        sur.SiteId == s.Id && 
                        sur.Role == SiteRoleType.Owner && 
                        sur.IsActive));

                return hasNonOwnerRoles || sitesWithoutOwner;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查迁移需求时发生错误");
                return false;
            }
        }
    }
}
