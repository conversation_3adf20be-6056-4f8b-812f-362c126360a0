using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MolySite.Core.Controllers;
using MolySite.Core.Services;
using MolySite.Shared.Dtos;

namespace MolySite.API.Controllers
{
    /// <summary>
    /// 网站关注控制器
    /// </summary>
    [Route("api/[controller]")]
    [Authorize]
    public class FollowController : BaseApiController
    {
        private readonly IFollowService _followService;
        private readonly ILogger<FollowController> _logger;

        public FollowController(
            IFollowService followService,
            ILogger<FollowController> logger)
        {
            _followService = followService;
            _logger = logger;
        }

        /// <summary>
        /// 关注网站
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>关注结果</returns>
        [HttpPost("{siteId}/follow")]
        public async Task<IActionResult> FollowSite(Guid siteId)
        {
            var userId = GetCurrentUserId();
            var result = await _followService.FollowSiteAsync(userId, siteId);
            
            return HandleResult(result);
        }

        /// <summary>
        /// 取消关注网站
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>取消关注结果</returns>
        [HttpDelete("{siteId}/unfollow")]
        public async Task<IActionResult> UnfollowSite(Guid siteId)
        {
            var userId = GetCurrentUserId();
            var result = await _followService.UnfollowSiteAsync(userId, siteId);
            
            return HandleResult(result);
        }

        /// <summary>
        /// 检查是否关注了网站
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>是否关注</returns>
        [HttpGet("{siteId}/is-following")]
        public async Task<IActionResult> IsFollowingSite(Guid siteId)
        {
            var userId = GetCurrentUserId();
            var result = await _followService.IsFollowingSiteAsync(userId, siteId);
            
            return HandleResult(result);
        }

        /// <summary>
        /// 获取用户关注的网站列表
        /// </summary>
        /// <returns>关注的网站列表</returns>
        [HttpGet("my-followed-sites")]
        public async Task<IActionResult> GetMyFollowedSites()
        {
            var userId = GetCurrentUserId();
            var result = await _followService.GetFollowedSitesAsync(userId);
            
            if (result.IsSuccess)
            {
                // 转换为DTO
                var followedSiteDtos = result.Data!.Select(site => new FollowedSiteDto
                {
                    SiteId = site.Id,
                    SiteName = site.Name,
                    Domain = site.Domain,
                    Description = site.Description,
                    LogoUrl = site.LogoUrl,
                    FollowedAt = DateTime.UtcNow, // TODO: 从SiteUserRole获取实际关注时间
                    LastUpdated = site.LastModifiedAt
                }).ToList();

                return Ok(followedSiteDtos);
            }
            
            return HandleResult(result);
        }

        /// <summary>
        /// 获取网站的关注者列表（仅网站Owner可访问）
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>关注者列表</returns>
        [HttpGet("{siteId}/followers")]
        public async Task<IActionResult> GetSiteFollowers(Guid siteId)
        {
            // TODO: 添加权限检查，确保只有网站Owner可以查看关注者列表
            
            var result = await _followService.GetSiteFollowersAsync(siteId);
            
            if (result.IsSuccess)
            {
                // 转换为DTO
                var followerDtos = result.Data!.Select(user => new SiteFollowerDto
                {
                    UserId = user.Id,
                    UserName = user.UserName ?? "",
                    DisplayName = user.DisplayName,
                    AvatarUrl = user.AvatarUrl,
                    FollowedAt = DateTime.UtcNow // TODO: 从SiteUserRole获取实际关注时间
                }).ToList();

                return Ok(followerDtos);
            }
            
            return HandleResult(result);
        }

        /// <summary>
        /// 获取网站的关注者数量
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <returns>关注者数量</returns>
        [HttpGet("{siteId}/follower-count")]
        [AllowAnonymous] // 关注者数量可以公开查看
        public async Task<IActionResult> GetSiteFollowerCount(Guid siteId)
        {
            var result = await _followService.GetSiteFollowerCountAsync(siteId);
            
            return HandleResult(result);
        }

        /// <summary>
        /// 向网站关注者发送通知（仅网站Owner和Editor可访问）
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <param name="request">通知请求</param>
        /// <returns>发送结果</returns>
        [HttpPost("{siteId}/notify-followers")]
        public async Task<IActionResult> NotifyFollowers(Guid siteId, [FromBody] NotifyFollowersRequest request)
        {
            // TODO: 添加权限检查，确保只有网站Owner和Editor可以发送通知
            
            var userId = GetCurrentUserId();
            var result = await _followService.SendNotificationToFollowersAsync(
                siteId, request.Title, request.Content, userId);
            
            return HandleResult(result);
        }
    }

    /// <summary>
    /// 通知关注者请求
    /// </summary>
    public class NotifyFollowersRequest
    {
        /// <summary>
        /// 通知标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 通知内容
        /// </summary>
        public string Content { get; set; } = string.Empty;
    }
}
