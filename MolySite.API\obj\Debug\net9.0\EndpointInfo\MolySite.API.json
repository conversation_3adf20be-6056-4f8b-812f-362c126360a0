{"openapi": "3.0.1", "info": {"title": "MolySite API", "version": "v1"}, "paths": {"/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/refresh-token": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/me": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/DatabaseCheck/users-and-roles": {"get": {"tags": ["DatabaseCheck"], "responses": {"200": {"description": "Success"}}}}, "/api/DatabaseCheck/users-by-role/{roleName}": {"get": {"tags": ["DatabaseCheck"], "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/DatabaseCheck/status": {"get": {"tags": ["DatabaseCheck"], "responses": {"200": {"description": "Success"}}}}, "/api/DatabaseCheck/reseed": {"post": {"tags": ["DatabaseCheck"], "responses": {"200": {"description": "Success"}}}}, "/api/DatabaseReset/reset": {"post": {"tags": ["DatabaseReset"], "responses": {"200": {"description": "Success"}}}}, "/api/DatabaseReset/status": {"get": {"tags": ["DatabaseReset"], "responses": {"200": {"description": "Success"}}}}, "/api/DatabaseTest/connection": {"get": {"tags": ["DatabaseTest"], "responses": {"200": {"description": "Success"}}}}, "/api/DatabaseTest/tables": {"get": {"tags": ["DatabaseTest"], "responses": {"200": {"description": "Success"}}}}, "/api/DatabaseTest/crud": {"post": {"tags": ["DatabaseTest"], "responses": {"200": {"description": "Success"}}}}, "/api/DatabaseTest/status": {"get": {"tags": ["DatabaseTest"], "responses": {"200": {"description": "Success"}}}}, "/api/Permissions/site/{siteId}/check": {"get": {"tags": ["Permissions"], "parameters": [{"name": "siteId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "permission", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Permissions/site/{siteId}/access": {"get": {"tags": ["Permissions"], "parameters": [{"name": "siteId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Permissions/accessible-sites": {"get": {"tags": ["Permissions"], "responses": {"200": {"description": "Success"}}}}, "/api/Permissions/editable-sites": {"get": {"tags": ["Permissions"], "responses": {"200": {"description": "Success"}}}}, "/api/Permissions/site/{siteId}/role": {"get": {"tags": ["Permissions"], "parameters": [{"name": "siteId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Permissions/site/{siteId}/users": {"get": {"tags": ["Permissions"], "parameters": [{"name": "siteId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Permissions/site/{siteId}/assign-role": {"post": {"tags": ["Permissions"], "parameters": [{"name": "siteId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignRoleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignRoleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AssignRoleRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Permissions/site/{siteId}/remove-role/{targetUserId}": {"delete": {"tags": ["Permissions"], "parameters": [{"name": "siteId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "targetUserId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Sites/my-sites": {"get": {"tags": ["Sites"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SiteDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SiteDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SiteDto"}}}}}}}}, "/api/Sites/all": {"get": {"tags": ["Sites"], "parameters": [{"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SiteDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SiteDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SiteDto"}}}}}}}}, "/api/Sites/{id}": {"get": {"tags": ["Sites"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SiteDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SiteDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SiteDto"}}}}}}, "put": {"tags": ["Sites"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSiteDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateSiteDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateSiteDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SiteDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SiteDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SiteDto"}}}}}}, "delete": {"tags": ["Sites"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Sites": {"post": {"tags": ["Sites"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSiteDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateSiteDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateSiteDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SiteDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SiteDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SiteDto"}}}}}}}, "/api/Sites/by-domain/{domain}": {"get": {"tags": ["Sites"], "parameters": [{"name": "domain", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SiteDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SiteDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SiteDto"}}}}}}}, "/api/Sites/check-domain": {"get": {"tags": ["Sites"], "parameters": [{"name": "domain", "in": "query", "schema": {"type": "string"}}, {"name": "excludeSiteId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/Sites/{id}/publish": {"post": {"tags": ["Sites"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Sites/{id}/unpublish": {"post": {"tags": ["Sites"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}}, "components": {"schemas": {"AssignRoleRequest": {"type": "object", "properties": {"userId": {"type": "string", "format": "uuid"}, "role": {"$ref": "#/components/schemas/SiteRoleType"}}, "additionalProperties": false}, "CreateSiteDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "domain": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "template": {"type": "string", "nullable": true}, "primaryColor": {"type": "string", "nullable": true}, "secondaryColor": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginDto": {"required": ["password", "userNameOrEmail"], "type": "object", "properties": {"userNameOrEmail": {"minLength": 1, "type": "string"}, "password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "RegisterDto": {"required": ["confirmPassword", "email", "password", "userName"], "type": "object", "properties": {"userName": {"maxLength": 50, "minLength": 3, "type": "string"}, "email": {"minLength": 1, "type": "string", "format": "email"}, "password": {"maxLength": 100, "minLength": 6, "type": "string"}, "confirmPassword": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "SiteDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "domain": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "template": {"type": "string", "nullable": true}, "customCss": {"type": "string", "nullable": true}, "customJavaScript": {"type": "string", "nullable": true}, "siteConfig": {"type": "string", "nullable": true}, "faviconUrl": {"type": "string", "nullable": true}, "logoUrl": {"type": "string", "nullable": true}, "primaryColor": {"type": "string", "nullable": true}, "secondaryColor": {"type": "string", "nullable": true}, "isPublished": {"type": "boolean"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "lastModifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "subscriptionPlan": {"type": "string", "nullable": true}, "subscriptionExpiresAt": {"type": "string", "format": "date-time", "nullable": true}, "maxStorageMB": {"type": "integer", "format": "int32"}, "usedStorageMB": {"type": "integer", "format": "int32"}, "footerText": {"type": "string", "nullable": true}, "socialMediaLinks": {"type": "string", "nullable": true}, "navigationMenu": {"type": "string", "nullable": true}, "analyticsCode": {"type": "string", "nullable": true}, "enableComments": {"type": "boolean"}, "enableSEO": {"type": "boolean"}, "siteKeywords": {"type": "string", "nullable": true}, "userRole": {"$ref": "#/components/schemas/SiteRoleType"}, "storageUsagePercentage": {"type": "number", "format": "double", "readOnly": true}, "isStorageOverLimit": {"type": "boolean", "readOnly": true}, "isSubscriptionExpired": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "SiteRoleType": {"enum": [1], "type": "integer", "format": "int32"}, "UpdateSiteDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "domain": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "template": {"type": "string", "nullable": true}, "customCss": {"type": "string", "nullable": true}, "customJavaScript": {"type": "string", "nullable": true}, "siteConfig": {"type": "string", "nullable": true}, "faviconUrl": {"type": "string", "nullable": true}, "logoUrl": {"type": "string", "nullable": true}, "primaryColor": {"type": "string", "nullable": true}, "secondaryColor": {"type": "string", "nullable": true}, "footerText": {"type": "string", "nullable": true}, "socialMediaLinks": {"type": "string", "nullable": true}, "navigationMenu": {"type": "string", "nullable": true}, "analyticsCode": {"type": "string", "nullable": true}, "enableComments": {"type": "boolean"}, "enableSEO": {"type": "boolean"}, "siteKeywords": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme", "scheme": "bearer"}}}, "security": [{"Bearer": []}]}