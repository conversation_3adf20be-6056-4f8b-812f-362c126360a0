using System;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MolySite.Core.Controllers;
using MolySite.Core.Interfaces;
using MolySite.Shared.Dtos;

namespace MolySite.API.Controllers
{
    /// <summary>
    /// 身份验证控制器
    /// </summary>
    [Route("api/[controller]")]
    public class AuthController : BaseApiController
    {
        private readonly IAuthService _authService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(
            IAuthService authService, 
            ILogger<AuthController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// 用户注册
        /// </summary>
        /// <param name="registerDto">注册信息</param>
        /// <returns>注册结果</returns>
        [HttpPost("register")]
        [AllowAnonymous]
        public async Task<IActionResult> Register([FromBody] RegisterDto registerDto)
        {
            _logger.LogInformation("收到注册请求: {UserName}, {Email}", registerDto.UserName, registerDto.Email);

            var result = await _authService.RegisterAsync(registerDto);

            if (result.IsSuccess)
            {
                _logger.LogInformation("注册成功: {UserId}", result.Data);
                return Success(new { userId = result.Data, message = "注册成功，您现在可以创建自己的网站了！" });
            }

            return HandleResult(result);
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="loginDto">登录信息</param>
        /// <returns>登录令牌</returns>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromBody] LoginDto loginDto)
        {
            _logger.LogInformation("收到登录请求: {UserName}", loginDto.UserNameOrEmail);

            var result = await _authService.LoginAsync(loginDto);

            if (result.IsSuccess)
            {
                _logger.LogInformation("登录成功: {UserId}", result.Data!.UserId);
                return Success(result.Data);
            }

            return HandleResult(result);
        }

        /// <summary>
        /// 刷新令牌
        /// </summary>
        /// <param name="refreshToken">刷新令牌</param>
        /// <returns>新的访问令牌</returns>
        [HttpPost("refresh-token")]
        [AllowAnonymous]
        public async Task<IActionResult> RefreshToken([FromBody] string refreshToken)
        {
            _logger.LogInformation("收到刷新令牌请求");

            var refreshTokenDto = new RefreshTokenDto { RefreshToken = refreshToken };
            var result = await _authService.RefreshTokenAsync(refreshTokenDto);

            if (result.IsSuccess)
            {
                _logger.LogInformation("刷新令牌成功: {UserId}", result.Data!.UserId);
                return Success(result.Data);
            }

            return HandleResult(result);
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        /// <returns>用户信息</returns>
        [HttpGet("me")]
        [Authorize]
        public async Task<IActionResult> GetCurrentUser()
        {
            var userId = GetCurrentUserId();
            var result = await _authService.GetUserAsync(userId);

            return HandleResult(result);
        }

        /// <summary>
        /// 用户注销
        /// </summary>
        /// <returns>注销结果</returns>
        [HttpPost("logout")]
        [Authorize]
        public async Task<IActionResult> Logout()
        {
            var userId = GetCurrentUserId();
            var token = Request.Headers["Authorization"].ToString().Replace("Bearer ", "");

            var result = await _authService.LogoutAsync(userId);

            return HandleResult(result);
        }
    }
}