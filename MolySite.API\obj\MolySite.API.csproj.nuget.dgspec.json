{"format": 1, "restore": {"E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.API\\MolySite.API.csproj": {}}, "projects": {"E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.API\\MolySite.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.API\\MolySite.API.csproj", "projectName": "MolySite.API", "projectPath": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.API\\MolySite.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Core\\MolySite.Core.csproj": {"projectPath": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Core\\MolySite.Core.csproj"}, "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Identity\\MolySite.Identity.csproj": {"projectPath": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Identity\\MolySite.Identity.csproj"}, "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Shared\\MolySite.Shared.csproj": {"projectPath": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Shared\\MolySite.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Core\\MolySite.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Core\\MolySite.Core.csproj", "projectName": "MolySite.Core", "projectPath": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Core\\MolySite.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Identity\\MolySite.Identity.csproj": {"projectPath": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Identity\\MolySite.Identity.csproj"}, "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Shared\\MolySite.Shared.csproj": {"projectPath": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Shared\\MolySite.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[8.0.1, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Identity\\MolySite.Identity.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Identity\\MolySite.Identity.csproj", "projectName": "MolySite.Identity", "projectPath": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Identity\\MolySite.Identity.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Identity\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Shared\\MolySite.Shared.csproj": {"projectPath": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Shared\\MolySite.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Shared\\MolySite.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Shared\\MolySite.Shared.csproj", "projectName": "MolySite.Shared", "projectPath": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Shared\\MolySite.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}