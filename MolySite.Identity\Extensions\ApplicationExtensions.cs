using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MolySite.Identity.Data.Migrations;

namespace MolySite.Identity.Extensions
{
    /// <summary>
    /// 应用程序扩展方法
    /// </summary>
    public static class ApplicationExtensions
    {
        /// <summary>
        /// 执行角色系统迁移
        /// </summary>
        public static async Task<IHost> MigrateRoleSystemAsync(this IHost host)
        {
            using var scope = host.Services.CreateScope();
            var services = scope.ServiceProvider;
            var logger = services.GetRequiredService<ILogger<RoleSystemMigrationService>>();

            try
            {
                logger.LogInformation("开始检查角色系统迁移...");

                var migrationService = services.GetRequiredService<RoleSystemMigrationService>();
                var success = await migrationService.MigrateRoleSystemAsync();

                if (success)
                {
                    var stats = await migrationService.GetMigrationStatsAsync();
                    logger.LogInformation("角色系统迁移检查完成:\n{Stats}", stats.ToString());
                }
                else
                {
                    logger.LogError("角色系统迁移失败");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "执行角色系统迁移时发生错误");
                // 不抛出异常，让应用继续启动
            }

            return host;
        }
    }
}
