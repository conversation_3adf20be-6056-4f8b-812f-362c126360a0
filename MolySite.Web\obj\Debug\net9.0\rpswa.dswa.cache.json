{"GlobalPropertiesHash": "MggtjpsRO97R0/WYY8bfi3EZZXTd9iqTdVQgIWPBy5A=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["Cc0VYkcawcR+xuU7mzbyF023F5iXwJi3opfeUInAgMI=", "2aYQeslNWbEYG9IBgk8kCSAan4Fn1T1NBx6m5x8+tr4=", "OmIN/gBtGngeNn3ZmpMUxAStpSweVc0KgG1ShiGo6RY=", "97eqZJAsWzQfrHOT3jOqEd5rPWC61pYthEcmeMvK8AU=", "ICuqetQSzWgq55Sjxi5ebtm6k9iH9IiiNWKqYy1gpKQ=", "/H7i0t6FgqAdLCTkhpbi0/jjLKlBzVL2cpxtP1vhoss=", "BOAzRf//EVVthDXANb+pI4DZo13tyyowwvoNJPTMwwA=", "P/he1XXS6Rssiv+MeLYv2GKkQJasUU5zhQKi+Sbg65A=", "vu4q2fpqCh3+iu8E3H/Wouzv41lVabK+Qr6V7czOJW4=", "1iSCno9kEex1fOb901opMYn/wswpt6f3YN0yZxi3I4Y=", "Njo/OBfz8yiZ8PQ98fjy5q6RZaui1GiaddimT9GBUEY=", "PbM1h12e0e1U7n11ExElaMaDmheh6pXbpYD4PUmBSks=", "HDNCJgiGZd1HestKzvTgEADG0XR2724dDumIxomdOTI=", "ThDYHy/6g8UUYafvjFTfsl2PLgsYPcdiD0ygGqk3WUg=", "v0W09jQ0GOud1JhOat2JwKnH5MtW6Ic0/vSvQDm4MBQ=", "9AP7XG4snEmRBRz5cGS0LR6yL66MrarSZHWVGJDnrfc=", "39YdwnRp4xksnqQOri4ovECvhsseoFpGECtZLUKtVlQ=", "RrGHBPu8QdMh50hrBz08oIV7khkAxYx9/8MQPXE1M0A=", "mvJbl2uTvcR3agI76GKxea0otOm0C+FU7cDFlBHNzOw=", "UeOJMGMydC/Q0X2W7MNpOP5Sv+8LH1Sr8YQ8jSVs7e0=", "IfCEtIlI1AUZuNPddvgO6Rbk+VSAlLhMEk29xr90ohU=", "I6Ay64IoHDa7r9q9AdWdExRmmzVlyxWeyD7BV+YvL5E=", "Av1+ZqYeBs7ALHtdRsUlbCZOU91b8F8b9Ji1eglQjpE=", "smTcUA4mjHmCQe9N82fKpCZh9BMyceBo4xhFcrbtdd4=", "8grBm7xkVxYklxmi0CHBL/5CiyvK8JJFoWV4y/2HT1Q=", "M0+yYzG1pCWorVza7HXrh/eU3PeBftqi2RX97IAK/zI=", "+gg2iUcwgWZimptwtK3ZSBcPPOaI4FfGCL0JoUUGqGI=", "h08gTc2fCahhKohkH4WqLrqCygjgMKD/k44ZVCrBPUo=", "RXNGY/zpL7Bh8E8ob+jEQSqqTDeqz+jmFoQT96KAlgA=", "CrYBfXwKpMXIAnRMR55yeKV01WY0nI7CAw8AKW7MfVc=", "UoxMGUJYROMQxbxoswZxqzyyKztpNd2XT9xWiESc/Oc=", "StS4kKH/KS6dvS4of93HULZhxlFcBIiKCKhFYUBqZuI=", "AxFoumrADkw+1IFNvJhTp4CqP3UBB86sbd6A2lfcE5g=", "X6vBw6x1CYEwo/oS384/veRRiAklfKBxE2M//0Om5hc=", "43CYgV+uMdPr2DGEI2vvfv/vUyIuvvfNHo71vWA3cYc=", "oQ0IxxZAKRwQaoIC1Jpqs8O+XHP/NdByQP8eEIuFjY0=", "dDdo8LVFiY2pk8pTCBz02P8noYJvOFyjoyqGAQTiZPA=", "764DJL67GUfhgdSVvBPUj4DleEW0Dcuzn9G+iDVrlIM=", "Z1fK1tv3GeaoQfUcIjbmGGeuTfwTiwpUMOhGIsuS+20=", "IrVEFQjPRwfLItf8VzrbSxipvbv29rQgXA6j6sZC1gE=", "aarK26RtbTb/PgVGaygrkUAL/BJZalxhfQGTca6nmiE=", "qBVBP1Th55HZW+lGbNCfJYQa/7BDGvr0Z8g9EFUw8T0=", "cyukxsBl/cQddwbuPpfjEsgIlqkbRTsvge7zOxJPcnI=", "fRrQkD6I6pN0NfxRGLjuG4jCVqT6FfTzeDGt0my3wzY=", "6U2HEjo4UR0vjkhcqHcP19V+lDEbL98vleMHDOBz1gg=", "mmLRPInwbsy7fYEDhhSjdrXlqpUvPi8L/qcO/A5nEwk=", "EejPwtcH3H0QznLIxbPIexTC/belGauBadvFOPrsTLY="], "CachedAssets": {"Cc0VYkcawcR+xuU7mzbyF023F5iXwJi3opfeUInAgMI=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\app.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2zmnmq58fl", "Integrity": "DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 3624, "LastWriteTime": "2025-06-23T13:38:46.3920344+00:00"}, "2aYQeslNWbEYG9IBgk8kCSAan4Fn1T1NBx6m5x8+tr4=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\dashboard.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "css/dashboard#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\dashboard.css", "FileLength": 0, "LastWriteTime": "2025-06-13T13:44:39+00:00"}, "OmIN/gBtGngeNn3ZmpMUxAStpSweVc0KgG1ShiGo6RY=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\tailwind.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "css/tailwind#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "frz2k1ad57", "Integrity": "dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\tailwind.css", "FileLength": 77985, "LastWriteTime": "2025-06-20T07:41:23+00:00"}, "97eqZJAsWzQfrHOT3jOqEd5rPWC61pYthEcmeMvK8AU=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\favicon.png", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-06-12T04:09:57+00:00"}, "ICuqetQSzWgq55Sjxi5ebtm6k9iH9IiiNWKqYy1gpKQ=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\app.js", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "js/app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab62h7jd1e", "Integrity": "dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\app.js", "FileLength": 2906, "LastWriteTime": "2025-06-23T13:16:06.9548017+00:00"}, "BOAzRf//EVVthDXANb+pI4DZo13tyyowwvoNJPTMwwA=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap-icons\\font\\bootstrap-icons.min.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap-icons/font/bootstrap-icons.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "26f9b7qkas", "Integrity": "9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap-icons\\font\\bootstrap-icons.min.css", "FileLength": 85875, "LastWriteTime": "2025-06-16T06:55:12+00:00"}, "/H7i0t6FgqAdLCTkhpbi0/jjLKlBzVL2cpxtP1vhoss=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\performance-monitor.js", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "js/performance-monitor#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1wi6zlpeou", "Integrity": "ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\performance-monitor.js", "FileLength": 6295, "LastWriteTime": "2025-06-23T13:47:53.9610416+00:00"}}, "CachedCopyCandidates": {}}