{"GlobalPropertiesHash": "MggtjpsRO97R0/WYY8bfi3EZZXTd9iqTdVQgIWPBy5A=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["Cc0VYkcawcR+xuU7mzbyF023F5iXwJi3opfeUInAgMI=", "2aYQeslNWbEYG9IBgk8kCSAan4Fn1T1NBx6m5x8+tr4=", "OmIN/gBtGngeNn3ZmpMUxAStpSweVc0KgG1ShiGo6RY=", "97eqZJAsWzQfrHOT3jOqEd5rPWC61pYthEcmeMvK8AU=", "ICuqetQSzWgq55Sjxi5ebtm6k9iH9IiiNWKqYy1gpKQ=", "/H7i0t6FgqAdLCTkhpbi0/jjLKlBzVL2cpxtP1vhoss=", "BOAzRf//EVVthDXANb+pI4DZo13tyyowwvoNJPTMwwA=", "P/he1XXS6Rssiv+MeLYv2GKkQJasUU5zhQKi+Sbg65A=", "vu4q2fpqCh3+iu8E3H/Wouzv41lVabK+Qr6V7czOJW4=", "6qbfgNDugcjrH+XHlz3onhURH//R6oolYyoZsk1YZ/E=", "zN5l/bXPW414iSTwmWPXoiOYwbNOxLMyLB833LjLN1Q=", "LRUa2hybDF2H+ALBOuOiq8awd5bXUCdsvHzRk4x4q14=", "lJ9SCTf57N5qlLuM2Q5I1yGxdNtUfpA8cpXMc6aCVaI=", "rKf4IjM3/0C4LIlYxjH5qF6wjr2axZoytC8HzwYApss=", "zF467SlKjioFsC4V8EszQ0Uw542+xoAEocrV33rvasQ=", "NZfk6wf8Kwa/RP0K6QjllUnzUkmeojXD2Qy0tctujHw=", "7l2Au8oQAWiBx8JcZNEM2v+0V+F92xcreblf8MKwFIk=", "Kdcwk8GwGh3lAo+BBujK3B/++SJE3EuSMmiuyBPFmhA=", "fCNBM5UQiDhYC/qQej7DDeAQmeP/zkj6WHTZWYbimAI=", "Amh8VneIGlGtTzatKsPfjwG15OquD1cnfa2NbAhRk7Q=", "yjChRTMch2y0RaJhbDLoClPJG3TSHeqwWUps0v7z+Xs=", "nHOLbA19i6QBE3nwQNn1KEb+PV7BTBNa4aJI5Ct+hnM=", "o5yPOE3Mim0U4/nvwAtnI+TGIH5ctmoMHz3G/nszxLw=", "bZHH67Pqm+LAIxtnGhMK5tVSlZu7yjH6WSvzv+lCaA4=", "Ya8r7L7MFRcErLA/Q/Og9kkso6g0iO1ZBLxOYlQC4Qs=", "scyX1mlzoZJAah40UmLfMyX8Gs4AwLtpDQyZWskQjQg=", "5j8HQlqU0ntmP8ZRCqlaPJv7h0KfTsjbUgwX0UXy7JI=", "m3a6Eg11biBB1ZpXaEI+LtZYQUfesAf6vaOfnrRFXc0=", "SrAX3SZPGrzydOUzYxswY25F4oXyvE/M08ugS/mhrMg=", "74tc1hOYcgK15LXk2L4MtSQ+t3+YLJPOIDMwsjl7W9k=", "Qd9A41IY5ss0v8vrUDpmBqH/ac+DXl2N6jKYvD9yFd4=", "Fhxd4jvyPFDzhTZ2Dk3qFKWIwUIKrJpc+B5lFmckl6o=", "CaytaFlhfwuXKz/NTis9v9eEavGz0it/6d4zf6OQ2KA=", "PxottEQihPm4sCTbsNfC/SstSEP6Tb0k+R4BTUzsW/U=", "d4ThAv3fogZXRFbv3r7mv27YloDb4LNhNhWz5FLh64I=", "uUC6KPK9Sl29VLwoabaRcKvmKXBCbpf5WpP/OYuWxng=", "DkMTQI0P5qriRhz5XHBtBoVzLKNkh8TUTRQSDuwhpws=", "7m0i2Ee8aXYTLd5B++YPaCLI38w30aw6kmt+JKSRCwQ=", "tnHoASP8HjNGP2T2hmnwvDd4izTWnnZgiHLXgPiULvA=", "7u0s4Q22QcUvTjjZK3tbHmveSfJ3XW4JSpu9Q7JTJ60=", "meb6kMU9nRse0BUU2Hbdwdyk80oObaaqf3axvCls0WQ=", "mH/Iyv97hnckDFAug0hP3pV0UbqdkeTG18xHYu3iP1E=", "60hCJK9klo1JfzpCKMzDcRjBTccCIC3XyXHLRUNFR0E=", "dpSuV8sfdWZfnA30IdXh1dDaj2M4SIUBFv+23pdsGoI=", "LtkM6dIgAL4RXBQi2QKeP/FUsmDnuvn6qFk20NayerY=", "Npv335dK5WqQMXLLa1Rf6EyPqHixF+BzwQNpJYwpxvs=", "ikNJYBBV8rv8yj9O102EXiRY7x8gWSSNtznOFIMdV64=", "aMJTqOqE1JXFmkPS6DBkb/zzpI4x2A+h/jGsVeyJOJo=", "GjN2INIaxFUHaHWcBOpYnYs4U8S31i08KlGrbDhDwBo=", "lUvY1IpzY+mXeIcQqgvbb2OMk+G/AD6rdbUd9Xe+I8E=", "jRgARikbS/uD1NBb+tkExZ6kw6aeN+KAufsXgc+ejlo=", "F/agovqO7dIcMOF+tSn12rf6c3rP5249qYHk7prJ/r4=", "NF85ohjPr5TTlj8UVvhENVDPHLgOMkzod+iXeETC2Wc=", "aGn/cLOzKy0f3qxbKSIT8x9xIY12h9yDKcq505xNsSM=", "/CoozjyAR+6SXjEX6svVoJ1q0jxeR1M4KzsY5QvLvqc=", "NktYeOOIh3rUxXHmzu905FrfsssyDcJq+Yad33F08CY=", "EYy2w3IPcReqViUrcK39/Cf8tapwJlmRnYHITYuzp1Q="], "CachedAssets": {"BOAzRf//EVVthDXANb+pI4DZo13tyyowwvoNJPTMwwA=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap-icons\\font\\bootstrap-icons.min.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap-icons/font/bootstrap-icons.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "26f9b7qkas", "Integrity": "9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap-icons\\font\\bootstrap-icons.min.css", "FileLength": 85875, "LastWriteTime": "2025-06-16T06:55:12+00:00"}, "/H7i0t6FgqAdLCTkhpbi0/jjLKlBzVL2cpxtP1vhoss=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\performance-monitor.js", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "js/performance-monitor#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1wi6zlpeou", "Integrity": "ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\performance-monitor.js", "FileLength": 6295, "LastWriteTime": "2025-06-23T13:47:53.9610416+00:00"}, "ICuqetQSzWgq55Sjxi5ebtm6k9iH9IiiNWKqYy1gpKQ=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\app.js", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "js/app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab62h7jd1e", "Integrity": "dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\app.js", "FileLength": 2906, "LastWriteTime": "2025-06-23T13:16:06.9548017+00:00"}, "97eqZJAsWzQfrHOT3jOqEd5rPWC61pYthEcmeMvK8AU=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\favicon.png", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-06-12T04:09:57+00:00"}, "OmIN/gBtGngeNn3ZmpMUxAStpSweVc0KgG1ShiGo6RY=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\tailwind.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "css/tailwind#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "frz2k1ad57", "Integrity": "dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\tailwind.css", "FileLength": 77985, "LastWriteTime": "2025-06-20T07:41:23+00:00"}, "2aYQeslNWbEYG9IBgk8kCSAan4Fn1T1NBx6m5x8+tr4=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\dashboard.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "css/dashboard#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\dashboard.css", "FileLength": 0, "LastWriteTime": "2025-06-13T13:44:39+00:00"}, "Cc0VYkcawcR+xuU7mzbyF023F5iXwJi3opfeUInAgMI=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\app.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2zmnmq58fl", "Integrity": "DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 3624, "LastWriteTime": "2025-06-23T13:38:46.3920344+00:00"}}, "CachedCopyCandidates": {}}