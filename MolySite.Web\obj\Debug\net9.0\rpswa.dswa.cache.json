{"GlobalPropertiesHash": "MggtjpsRO97R0/WYY8bfi3EZZXTd9iqTdVQgIWPBy5A=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["Ye0inGj6BEaB/cucKbOgB6VcAVqA2awonCR7tjaI6x8=", "/rkw2VmML4/ZbDsjNZxGFfGJnWL4sUuX1hrqlA6akfg=", "9ZKXKCUzedHGvD0rMHPSJi3iXBeKcLdkS4mA3f9p/CM=", "cdnljPUyG3PP1soRjrzVRDvXXRkm1RDbS/7R+vP+PXQ=", "B6ohlj3Pd5N7vDYTXe1y3iD23Iuq+GRlHc1jZHLWxsQ=", "kBWcFQkcD45+B/dAraBNbnbQJ0uaJCesKnDHLZQqEoE=", "eYZ0XBl5CRiK2zlcNa2RpZUGB62VrKk1plORdxj8L38=", "P/he1XXS6Rssiv+MeLYv2GKkQJasUU5zhQKi+Sbg65A=", "vu4q2fpqCh3+iu8E3H/Wouzv41lVabK+Qr6V7czOJW4=", "7gvXzsV058H4GWqM2ieZO4bP4VqaJIJ2DkzU4hRSsHk=", "JYHtH+33WXoiMQzpaLIXgFGoJRYKLK2VN3QVl0QMbv0=", "Ms8znTOmWl5orr7OeKDbMEdYvUnPF3fmLjSZXyl7uwQ=", "+HXhKI5lFfNB0WkFRlyQVC8SMbbFitqLrazMTOmxu+8=", "xb/+/YfBdoWSGDKbVaTHW0DrqZqGJe/jbo59d+Eb3y4=", "THYmfxE7FMvb/UALEN6QD4cB1XLZceqLqrzp72usWpM=", "RyPuzcted6C515D+Ixkwz31QiwPho8FTALmlUf9gzxI=", "b37hLTAJoDpKngX2m1+gGOzyz3LN3GQ8nqAID1rqTwk=", "bo34yxgecCs2JTrcceUE8AsC/+5494YugVh9wOlIY68=", "DkwRlZotEi+MYNgrZU4QNCf5Y3fQnVWrwdrIJ/NKl6o=", "zjA82LMvHzDwYIxJ/T/ZfQW/t83QsEFLVJJ/4ytv/UY=", "gocDRbVgihqS8jTfov+l+jdhuMwvm121+1SGObmqvZ8=", "nrf1ofqrJAHft9me71gXPYZ15YDxC3H7YpTtvu54hGw=", "+pY3iZJ3ENj/K2m5pvqkdfxFiLi+tCSmpfLlwUtes/w=", "o4x2vKXwFdXvP/V6W9pgUNe2sCKTfsPe1o56wRsb6bY=", "AK4hRI7Nnfa3EiFTk0O0TQ8HI9Oh2jeFczdHF6OMTQA=", "Z87+J30OnTH5lGSbJ+Z0PgNyBlDpVs6AYdhIWy61gMY=", "hSyFJo7DtZ0RTH8uz5MKHDE/PZPZcFSasTLAe/lpHvg=", "jYzMFMG5L7AaK1XMx0hCZ0IWMw0xJS2LLqWihuPv4YQ=", "bcSgwb14W/yYrCwSzVSHMAtgF6W4uuZv2sgr06fV2DM=", "UOsrY41oepwHY2dGub1xvlTJ/LuzfE7N+Pi5Ng+zeWM=", "0huKjOaHqg52expP2ndkQj6nIo1ESbJ7kg0lHv+0ZFw=", "QMCUeUVZwA9XSlxVmfYTzFOm7M/9Qk7a/+X+oqwsCZk=", "27RkpIlmITqQopTB63+5YrT7Pq7Q38kr2zyRZHQjB3w=", "A5OEk4YwN+bJOXHfj9j1gK8mmtxU5KT50fK3YLa75Po=", "kKh6PxueJE3/2Q2vX9ZXL2XL6uSODPpn73E6h+JP40o=", "5JzXtAFcAEr4hoWhoP4wyJHic1LfRLFKopLNdJ3nQyc=", "h6BaUxkhPKveqlvWZK7ynqooMy2lmd6PpMAVzoY/UCQ=", "PUZ08DQnN/RXhfTPTu6azt2ExIDjjGBv4RqRoy1gmFs=", "nchI9aqxm0i29wiZcQ75wWz2DuZI2dcyjTHFlcKRrGs=", "D6WsgN7uUUx6fIbww4u0hM85zbp8ZtaSH7rpyqYZ+lQ=", "QrpZKAAnGOYefBC01I+UpD+ljcvrGwzOvsREzTiY5D0=", "VT7WtCeyzZFBQ5+tk/xN8fD+BLtU3jfm255qM6bGCcU=", "+j3gwHZLVeoafSG9Xq/cF5wrvmnkAzIYPBDALcTQf8c=", "iZJQxqR0ByIjc5OhXveP1EPZh3j9NregGEHh8yJBflM=", "ftB7G18qZekHuuyrJTDKnXOgqM2kNCJgIPympS5B1OU=", "nNWFT+pHBQOe7jsUUAjtV9hnS+Kab6PT9/+dNFTKPqU=", "2sT/BS28wWN3M1aNLYjxAYCxZ3vAZ+OvCgwYSgUbOVg=", "v3lsFiWstFJLDlnQYBzo/7Bsru5EPj9YCaTDVGIw5XM=", "HXu+DJITjGLJtKVA321wB2eQZWB7/P+NNFthOU0zYAM=", "FD6VT1q6CKt6Qx5xls9jkgpkk156bpjOZdA5ejJWX7A=", "reOUF8k4nDjd5A4vAQd0IFriMCp3RTTZ/A0vTgIDO7I="], "CachedAssets": {"eYZ0XBl5CRiK2zlcNa2RpZUGB62VrKk1plORdxj8L38=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap-icons\\font\\bootstrap-icons.min.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap-icons/font/bootstrap-icons.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "26f9b7qkas", "Integrity": "9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap-icons\\font\\bootstrap-icons.min.css", "FileLength": 85875, "LastWriteTime": "2025-06-16T06:55:12+00:00"}, "kBWcFQkcD45+B/dAraBNbnbQJ0uaJCesKnDHLZQqEoE=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\performance-monitor.js", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "js/performance-monitor#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1wi6zlpeou", "Integrity": "ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\performance-monitor.js", "FileLength": 6295, "LastWriteTime": "2025-06-23T13:47:53.9610416+00:00"}, "B6ohlj3Pd5N7vDYTXe1y3iD23Iuq+GRlHc1jZHLWxsQ=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\js\\app.js", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "js/app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab62h7jd1e", "Integrity": "dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\app.js", "FileLength": 2906, "LastWriteTime": "2025-06-23T13:16:06.9548017+00:00"}, "cdnljPUyG3PP1soRjrzVRDvXXRkm1RDbS/7R+vP+PXQ=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\favicon.png", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-06-12T04:09:57+00:00"}, "9ZKXKCUzedHGvD0rMHPSJi3iXBeKcLdkS4mA3f9p/CM=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\tailwind.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "css/tailwind#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "frz2k1ad57", "Integrity": "dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\tailwind.css", "FileLength": 77985, "LastWriteTime": "2025-06-20T07:41:23+00:00"}, "/rkw2VmML4/ZbDsjNZxGFfGJnWL4sUuX1hrqlA6akfg=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\css\\dashboard.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "css/dashboard#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\dashboard.css", "FileLength": 0, "LastWriteTime": "2025-06-13T13:44:39+00:00"}, "Ye0inGj6BEaB/cucKbOgB6VcAVqA2awonCR7tjaI6x8=": {"Identity": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\app.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2zmnmq58fl", "Integrity": "DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 3624, "LastWriteTime": "2025-06-23T13:38:46.3920344+00:00"}}, "CachedCopyCandidates": {}}