using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using MolySite.Core.Services;
using MolySite.Identity.Data;
using MolySite.Identity.Models;
using Xunit;

namespace MolySite.Tests
{
    /// <summary>
    /// 角色系统测试
    /// </summary>
    public class RoleSystemTests : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly PermissionService _permissionService;
        private readonly FollowService _followService;
        private readonly Mock<ILogger<PermissionService>> _permissionLogger;
        private readonly Mock<ILogger<FollowService>> _followLogger;

        public RoleSystemTests()
        {
            // 创建内存数据库
            var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new ApplicationDbContext(options);
            _permissionLogger = new Mock<ILogger<PermissionService>>();
            _followLogger = new Mock<ILogger<FollowService>>();
            
            _permissionService = new PermissionService(_context, _permissionLogger.Object);
            _followService = new FollowService(_context, _followLogger.Object);

            // 初始化测试数据
            SeedTestData();
        }

        private void SeedTestData()
        {
            // 创建测试用户
            var superAdmin = new ApplicationUser
            {
                Id = Guid.NewGuid(),
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                PlatformRole = PlatformRole.SuperAdmin,
                PlatformRoles = new List<string> { "SuperAdmin" }
            };

            var user1 = new ApplicationUser
            {
                Id = Guid.NewGuid(),
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                PlatformRole = PlatformRole.User,
                PlatformRoles = new List<string> { "User" }
            };

            var user2 = new ApplicationUser
            {
                Id = Guid.NewGuid(),
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                PlatformRole = PlatformRole.User,
                PlatformRoles = new List<string> { "User" }
            };

            // 创建测试网站
            var site1 = new Site
            {
                Id = Guid.NewGuid(),
                Name = "测试网站1",
                Domain = "test1.com",
                OwnerId = user1.Id,
                Owner = user1,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            var site2 = new Site
            {
                Id = Guid.NewGuid(),
                Name = "测试网站2",
                Domain = "test2.com",
                OwnerId = user2.Id,
                Owner = user2,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            // 创建网站用户角色关系
            var ownerRole1 = new SiteUserRole
            {
                Id = Guid.NewGuid(),
                UserId = user1.Id,
                SiteId = site1.Id,
                Role = SiteRoleType.Owner,
                IsActive = true,
                GrantedAt = DateTime.UtcNow,
                InvitationStatus = InvitationStatus.Accepted
            };

            var editorRole = new SiteUserRole
            {
                Id = Guid.NewGuid(),
                UserId = user2.Id,
                SiteId = site1.Id,
                Role = SiteRoleType.Editor,
                IsActive = true,
                GrantedAt = DateTime.UtcNow,
                InvitationStatus = InvitationStatus.Accepted
            };

            _context.Users.AddRange(superAdmin, user1, user2);
            _context.Sites.AddRange(site1, site2);
            _context.SiteUserRoles.AddRange(ownerRole1, editorRole);
            _context.SaveChanges();
        }

        [Fact]
        public async Task SuperAdmin_ShouldHaveLimitedSitePermissions()
        {
            // Arrange
            var superAdmin = await _context.Users.FirstAsync(u => u.PlatformRole == PlatformRole.SuperAdmin);
            var site = await _context.Sites.FirstAsync();

            // Act & Assert
            var canViewSettings = await _permissionService.HasSitePermissionAsync(
                superAdmin.Id, site.Id, "Site.ViewSettings");
            var canEditContent = await _permissionService.HasSitePermissionAsync(
                superAdmin.Id, site.Id, "Site.EditContent");

            // SuperAdmin可以查看设置但不能编辑内容
            Assert.True(canViewSettings.IsSuccess && canViewSettings.Data);
            Assert.True(canEditContent.IsSuccess && !canEditContent.Data);
        }

        [Fact]
        public async Task User_ShouldBeAbleToFollowSites()
        {
            // Arrange
            var user = await _context.Users.FirstAsync(u => u.PlatformRole == PlatformRole.User);
            var site = await _context.Sites.FirstAsync(s => s.OwnerId != user.Id);

            // Act
            var followResult = await _followService.FollowSiteAsync(user.Id, site.Id);
            var isFollowingResult = await _followService.IsFollowingSiteAsync(user.Id, site.Id);

            // Assert
            Assert.True(followResult.IsSuccess);
            Assert.True(isFollowingResult.IsSuccess && isFollowingResult.Data);
        }

        [Fact]
        public async Task User_ShouldNotBeAbleToFollowSameSiteTwice()
        {
            // Arrange
            var user = await _context.Users.FirstAsync(u => u.PlatformRole == PlatformRole.User);
            var site = await _context.Sites.FirstAsync(s => s.OwnerId != user.Id);

            // Act
            await _followService.FollowSiteAsync(user.Id, site.Id);
            var secondFollowResult = await _followService.FollowSiteAsync(user.Id, site.Id);

            // Assert
            Assert.False(secondFollowResult.IsSuccess);
            Assert.Equal("ALREADY_FOLLOWING", secondFollowResult.ErrorCode);
        }

        [Fact]
        public async Task SiteOwner_ShouldHaveFullSitePermissions()
        {
            // Arrange
            var owner = await _context.Users.FirstAsync(u => u.PlatformRole == PlatformRole.User);
            var site = await _context.Sites.FirstAsync(s => s.OwnerId == owner.Id);

            // Act & Assert
            var canEditSettings = await _permissionService.HasSitePermissionAsync(
                owner.Id, site.Id, "Site.EditSettings");
            var canEditContent = await _permissionService.HasSitePermissionAsync(
                owner.Id, site.Id, "Site.EditContent");
            var canManageUsers = await _permissionService.HasSitePermissionAsync(
                owner.Id, site.Id, "Site.ManageUsers");

            Assert.True(canEditSettings.IsSuccess && canEditSettings.Data);
            Assert.True(canEditContent.IsSuccess && canEditContent.Data);
            Assert.True(canManageUsers.IsSuccess && canManageUsers.Data);
        }

        [Fact]
        public async Task SiteEditor_ShouldHaveLimitedSitePermissions()
        {
            // Arrange
            var editor = await _context.Users
                .Where(u => u.SiteRoles.Any(sr => sr.Role == SiteRoleType.Editor))
                .FirstAsync();
            var site = await _context.Sites
                .Where(s => s.SiteUsers.Any(su => su.UserId == editor.Id && su.Role == SiteRoleType.Editor))
                .FirstAsync();

            // Act & Assert
            var canEditContent = await _permissionService.HasSitePermissionAsync(
                editor.Id, site.Id, "Site.EditContent");
            var canEditSettings = await _permissionService.HasSitePermissionAsync(
                editor.Id, site.Id, "Site.EditSettings");
            var canManageUsers = await _permissionService.HasSitePermissionAsync(
                editor.Id, site.Id, "Site.ManageUsers");

            Assert.True(canEditContent.IsSuccess && canEditContent.Data);
            Assert.True(canEditSettings.IsSuccess && !canEditSettings.Data);
            Assert.True(canManageUsers.IsSuccess && !canManageUsers.Data);
        }

        [Fact]
        public async Task GetFollowedSites_ShouldReturnCorrectSites()
        {
            // Arrange
            var user = await _context.Users.FirstAsync(u => u.PlatformRole == PlatformRole.User);
            var sites = await _context.Sites.Where(s => s.OwnerId != user.Id).ToListAsync();

            // Follow multiple sites
            foreach (var site in sites)
            {
                await _followService.FollowSiteAsync(user.Id, site.Id);
            }

            // Act
            var followedSitesResult = await _followService.GetFollowedSitesAsync(user.Id);

            // Assert
            Assert.True(followedSitesResult.IsSuccess);
            Assert.Equal(sites.Count, followedSitesResult.Data!.Count);
        }

        [Fact]
        public async Task GetSiteFollowerCount_ShouldReturnCorrectCount()
        {
            // Arrange
            var site = await _context.Sites.FirstAsync();
            var users = await _context.Users
                .Where(u => u.PlatformRole == PlatformRole.User && u.Id != site.OwnerId)
                .ToListAsync();

            // Multiple users follow the site
            foreach (var user in users)
            {
                await _followService.FollowSiteAsync(user.Id, site.Id);
            }

            // Act
            var followerCountResult = await _followService.GetSiteFollowerCountAsync(site.Id);

            // Assert
            Assert.True(followerCountResult.IsSuccess);
            Assert.Equal(users.Count, followerCountResult.Data);
        }

        [Fact]
        public async Task UnfollowSite_ShouldRemoveFollowRelation()
        {
            // Arrange
            var user = await _context.Users.FirstAsync(u => u.PlatformRole == PlatformRole.User);
            var site = await _context.Sites.FirstAsync(s => s.OwnerId != user.Id);

            await _followService.FollowSiteAsync(user.Id, site.Id);

            // Act
            var unfollowResult = await _followService.UnfollowSiteAsync(user.Id, site.Id);
            var isFollowingResult = await _followService.IsFollowingSiteAsync(user.Id, site.Id);

            // Assert
            Assert.True(unfollowResult.IsSuccess);
            Assert.True(isFollowingResult.IsSuccess && !isFollowingResult.Data);
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
