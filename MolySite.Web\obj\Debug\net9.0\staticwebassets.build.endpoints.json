{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "MolySite.Web.56vxggxec6.styles.css", "AssetFile": "MolySite.Web.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000238208671"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4197"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU=\""}, {"Name": "ETag", "Value": "W/\"DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "56vxggxec6"}, {"Name": "integrity", "Value": "sha256-DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk="}, {"Name": "label", "Value": "MolySite.Web.styles.css"}]}, {"Route": "MolySite.Web.56vxggxec6.styles.css", "AssetFile": "MolySite.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "56vxggxec6"}, {"Name": "integrity", "Value": "sha256-DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk="}, {"Name": "label", "Value": "MolySite.Web.styles.css"}]}, {"Route": "MolySite.Web.56vxggxec6.styles.css.gz", "AssetFile": "MolySite.Web.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4197"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "56vxggxec6"}, {"Name": "integrity", "Value": "sha256-1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU="}, {"Name": "label", "Value": "MolySite.Web.styles.css.gz"}]}, {"Route": "MolySite.Web.styles.css", "AssetFile": "MolySite.Web.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000238208671"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4197"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU=\""}, {"Name": "ETag", "Value": "W/\"DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk="}]}, {"Route": "MolySite.Web.styles.css", "AssetFile": "MolySite.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DfwovXYRep0tJRAxExRNWinOyUANrAXF/yJnk9LoXvk="}]}, {"Route": "MolySite.Web.styles.css.gz", "AssetFile": "MolySite.Web.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4197"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1MgkklOUBYGb4bHndkc+idO8E264qukNapSSiUmFTEU="}]}, {"Route": "app.2zmnmq58fl.css", "AssetFile": "app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000479846449"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2083"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vmCTBoClvSCFZfmb8tAJuYUolfSfB2KO9sVfZTLEF6s=\""}, {"Name": "ETag", "Value": "W/\"DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2zmnmq58fl"}, {"Name": "integrity", "Value": "sha256-DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "app.2zmnmq58fl.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3624"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 13:38:46 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2zmnmq58fl"}, {"Name": "integrity", "Value": "sha256-DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "app.2zmnmq58fl.css.gz", "AssetFile": "app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2083"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vmCTBoClvSCFZfmb8tAJuYUolfSfB2KO9sVfZTLEF6s=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2zmnmq58fl"}, {"Name": "integrity", "Value": "sha256-vmCTBoClvSCFZfmb8tAJuYUolfSfB2KO9sVfZTLEF6s="}, {"Name": "label", "Value": "app.css.gz"}]}, {"Route": "app.css", "AssetFile": "app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000479846449"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2083"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vmCTBoClvSCFZfmb8tAJuYUolfSfB2KO9sVfZTLEF6s=\""}, {"Name": "ETag", "Value": "W/\"DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY="}]}, {"Route": "app.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3624"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 13:38:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DqIp11WHew0zM/FylEGQeND+/yT8gheYxafrg1VRDnY="}]}, {"Route": "app.css.gz", "AssetFile": "app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2083"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vmCTBoClvSCFZfmb8tAJuYUolfSfB2KO9sVfZTLEF6s=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vmCTBoClvSCFZfmb8tAJuYUolfSfB2KO9sVfZTLEF6s="}]}, {"Route": "css/dashboard.5ipweew5fc.css", "AssetFile": "css/dashboard.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "css/dashboard.css"}]}, {"Route": "css/dashboard.5ipweew5fc.css", "AssetFile": "css/dashboard.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 13:44:39 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "css/dashboard.css"}]}, {"Route": "css/dashboard.5ipweew5fc.css.gz", "AssetFile": "css/dashboard.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "css/dashboard.css.gz"}]}, {"Route": "css/dashboard.css", "AssetFile": "css/dashboard.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "css/dashboard.css", "AssetFile": "css/dashboard.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 13:44:39 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "css/dashboard.css.gz", "AssetFile": "css/dashboard.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "css/tailwind.css", "AssetFile": "css/tailwind.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083430669"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11985"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IS/sCGGsIsV2JtjyIh3KAp86XMLn8tE3QRPaQgNo51k=\""}, {"Name": "ETag", "Value": "W/\"dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU="}]}, {"Route": "css/tailwind.css", "AssetFile": "css/tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "77985"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 07:41:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU="}]}, {"Route": "css/tailwind.css.gz", "AssetFile": "css/tailwind.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11985"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IS/sCGGsIsV2JtjyIh3KAp86XMLn8tE3QRPaQgNo51k=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IS/sCGGsIsV2JtjyIh3KAp86XMLn8tE3QRPaQgNo51k="}]}, {"Route": "css/tailwind.frz2k1ad57.css", "AssetFile": "css/tailwind.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083430669"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11985"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IS/sCGGsIsV2JtjyIh3KAp86XMLn8tE3QRPaQgNo51k=\""}, {"Name": "ETag", "Value": "W/\"dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "frz2k1ad57"}, {"Name": "integrity", "Value": "sha256-dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU="}, {"Name": "label", "Value": "css/tailwind.css"}]}, {"Route": "css/tailwind.frz2k1ad57.css", "AssetFile": "css/tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "77985"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 07:41:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "frz2k1ad57"}, {"Name": "integrity", "Value": "sha256-dcaRRoB2DU3sc9ZVRs0kaq4TGFzmqFJ2QeGDABeCIoU="}, {"Name": "label", "Value": "css/tailwind.css"}]}, {"Route": "css/tailwind.frz2k1ad57.css.gz", "AssetFile": "css/tailwind.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11985"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IS/sCGGsIsV2JtjyIh3KAp86XMLn8tE3QRPaQgNo51k=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "frz2k1ad57"}, {"Name": "integrity", "Value": "sha256-IS/sCGGsIsV2JtjyIh3KAp86XMLn8tE3QRPaQgNo51k="}, {"Name": "label", "Value": "css/tailwind.css.gz"}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 04:09:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}, {"Name": "label", "Value": "favicon.png"}]}, {"Route": "favicon.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 04:09:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "js/app.ab62h7jd1e.js", "AssetFile": "js/app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000796178344"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1255"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VQz0orU4C6NuYKE1xPMzkdBfauBccLAUYOmEliARlx0=\""}, {"Name": "ETag", "Value": "W/\"dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ab62h7jd1e"}, {"Name": "integrity", "Value": "sha256-dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY="}, {"Name": "label", "Value": "js/app.js"}]}, {"Route": "js/app.ab62h7jd1e.js", "AssetFile": "js/app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2906"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 13:16:06 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ab62h7jd1e"}, {"Name": "integrity", "Value": "sha256-dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY="}, {"Name": "label", "Value": "js/app.js"}]}, {"Route": "js/app.ab62h7jd1e.js.gz", "AssetFile": "js/app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1255"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VQz0orU4C6NuYKE1xPMzkdBfauBccLAUYOmEliARlx0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ab62h7jd1e"}, {"Name": "integrity", "Value": "sha256-VQz0orU4C6NuYKE1xPMzkdBfauBccLAUYOmEliARlx0="}, {"Name": "label", "Value": "js/app.js.gz"}]}, {"Route": "js/app.js", "AssetFile": "js/app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000796178344"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1255"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VQz0orU4C6NuYKE1xPMzkdBfauBccLAUYOmEliARlx0=\""}, {"Name": "ETag", "Value": "W/\"dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY="}]}, {"Route": "js/app.js", "AssetFile": "js/app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2906"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 13:16:06 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dilwlIRApZcMnvDtU8VFm1A+rOiZkb3/JRz5UCmFCTY="}]}, {"Route": "js/app.js.gz", "AssetFile": "js/app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1255"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VQz0orU4C6NuYKE1xPMzkdBfauBccLAUYOmEliARlx0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VQz0orU4C6NuYKE1xPMzkdBfauBccLAUYOmEliARlx0="}]}, {"Route": "js/performance-monitor.1wi6zlpeou.js", "AssetFile": "js/performance-monitor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000510725230"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1957"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xGhPw4+DdRO5TZd2TfPCMXv9GqX/iHWf6ehSOEtuFjg=\""}, {"Name": "ETag", "Value": "W/\"ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1wi6zlpeou"}, {"Name": "integrity", "Value": "sha256-ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc="}, {"Name": "label", "Value": "js/performance-monitor.js"}]}, {"Route": "js/performance-monitor.1wi6zlpeou.js", "AssetFile": "js/performance-monitor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6295"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 13:47:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1wi6zlpeou"}, {"Name": "integrity", "Value": "sha256-ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc="}, {"Name": "label", "Value": "js/performance-monitor.js"}]}, {"Route": "js/performance-monitor.1wi6zlpeou.js.gz", "AssetFile": "js/performance-monitor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1957"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xGhPw4+DdRO5TZd2TfPCMXv9GqX/iHWf6ehSOEtuFjg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1wi6zlpeou"}, {"Name": "integrity", "Value": "sha256-xGhPw4+DdRO5TZd2TfPCMXv9GqX/iHWf6ehSOEtuFjg="}, {"Name": "label", "Value": "js/performance-monitor.js.gz"}]}, {"Route": "js/performance-monitor.js", "AssetFile": "js/performance-monitor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000510725230"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1957"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xGhPw4+DdRO5TZd2TfPCMXv9GqX/iHWf6ehSOEtuFjg=\""}, {"Name": "ETag", "Value": "W/\"ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc="}]}, {"Route": "js/performance-monitor.js", "AssetFile": "js/performance-monitor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6295"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 13:47:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ISyFr8Y2KS5sGuCrY/xvm57nzBdGGajVGC/zGMZp4Oc="}]}, {"Route": "js/performance-monitor.js.gz", "AssetFile": "js/performance-monitor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1957"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xGhPw4+DdRO5TZd2TfPCMXv9GqX/iHWf6ehSOEtuFjg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xGhPw4+DdRO5TZd2TfPCMXv9GqX/iHWf6ehSOEtuFjg="}]}, {"Route": "lib/bootstrap-icons/font/bootstrap-icons.min.26f9b7qkas.css", "AssetFile": "lib/bootstrap-icons/font/bootstrap-icons.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071968334"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13894"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IglclAz7SLu0YmGB7PxVX8owYskkCvVdjd0rSP0X+mw=\""}, {"Name": "ETag", "Value": "W/\"9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "26f9b7qkas"}, {"Name": "integrity", "Value": "sha256-9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI="}, {"Name": "label", "Value": "lib/bootstrap-icons/font/bootstrap-icons.min.css"}]}, {"Route": "lib/bootstrap-icons/font/bootstrap-icons.min.26f9b7qkas.css", "AssetFile": "lib/bootstrap-icons/font/bootstrap-icons.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "85875"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 06:55:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "26f9b7qkas"}, {"Name": "integrity", "Value": "sha256-9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI="}, {"Name": "label", "Value": "lib/bootstrap-icons/font/bootstrap-icons.min.css"}]}, {"Route": "lib/bootstrap-icons/font/bootstrap-icons.min.26f9b7qkas.css.gz", "AssetFile": "lib/bootstrap-icons/font/bootstrap-icons.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13894"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IglclAz7SLu0YmGB7PxVX8owYskkCvVdjd0rSP0X+mw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "26f9b7qkas"}, {"Name": "integrity", "Value": "sha256-IglclAz7SLu0YmGB7PxVX8owYskkCvVdjd0rSP0X+mw="}, {"Name": "label", "Value": "lib/bootstrap-icons/font/bootstrap-icons.min.css.gz"}]}, {"Route": "lib/bootstrap-icons/font/bootstrap-icons.min.css", "AssetFile": "lib/bootstrap-icons/font/bootstrap-icons.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071968334"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13894"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IglclAz7SLu0YmGB7PxVX8owYskkCvVdjd0rSP0X+mw=\""}, {"Name": "ETag", "Value": "W/\"9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI="}]}, {"Route": "lib/bootstrap-icons/font/bootstrap-icons.min.css", "AssetFile": "lib/bootstrap-icons/font/bootstrap-icons.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "85875"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 06:55:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI="}]}, {"Route": "lib/bootstrap-icons/font/bootstrap-icons.min.css.gz", "AssetFile": "lib/bootstrap-icons/font/bootstrap-icons.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13894"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IglclAz7SLu0YmGB7PxVX8owYskkCvVdjd0rSP0X+mw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 10:01:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IglclAz7SLu0YmGB7PxVX8owYskkCvVdjd0rSP0X+mw="}]}]}