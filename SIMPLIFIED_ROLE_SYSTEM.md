# MolySite 简化角色体系

## 🎯 简化目标

为了降低初期设计复杂性，我们移除了 Editor 和 Follower 角色以及相关的邀请机制，专注于核心功能。

## ✅ 简化后的角色体系

### 平台级角色
1. **SuperAdmin**: 平台管理员
   - 管理用户账户和平台设置
   - **不能访问用户网站隐私内容**
   
2. **User**: 普通用户
   - 可以创建网站
   - 拥有自己创建网站的完全控制权

### 网站级角色
1. **Owner**: 网站所有者（唯一角色）
   - 网站的完全控制权
   - 所有网站功能的访问权限

## 🔄 移除的功能

### 1. 移除的角色
- ❌ **Editor**: 网站编辑者角色
- ❌ **Follower**: 网站关注者角色

### 2. 移除的功能
- ❌ **邀请机制**: 邀请其他用户成为编辑者
- ❌ **关注功能**: 关注其他网站
- ❌ **通知系统**: 向关注者发送通知
- ❌ **协作功能**: 多用户协作编辑

### 3. 移除的文件
- `MolySite.Core/Interfaces/IFollowService.cs`
- `MolySite.Core/Services/FollowService.cs`
- `MolySite.Shared/Dtos/FollowedSiteDto.cs`
- `MolySite.Shared/Dtos/SiteFollowerDto.cs`
- `MolySite.API/Controllers/UsersController.cs`
- `MolySite.Web/Components/Pages/Dashboard/FollowedSites.razor`

### 4. 移除的权限
- `Site.Follow`
- `Site.ReceiveNotifications`
- `Site.Contact`
- `Site.ViewFollowerContent`

## 📊 简化后的数据模型

### SiteRoleType 枚举
```csharp
public enum SiteRoleType
{
    Owner = 1  // 网站所有者（唯一角色）
}
```

### 用户-网站关系
- 每个网站只有一个 Owner
- 用户可以拥有多个网站
- 每个网站的 Owner 拥有该网站的完全控制权

## 🎯 简化后的用户流程

### 用户注册
1. 注册成为 `User` 角色
2. 可以创建网站
3. 自动成为所创建网站的 `Owner`

### 网站管理
1. 用户创建网站后自动成为 Owner
2. Owner 拥有网站的所有权限：
   - 编辑网站设置
   - 管理内容
   - 发布/取消发布
   - 删除网站
   - 查看统计数据

### SuperAdmin 功能
1. 管理用户账户（基本信息）
2. 管理平台设置
3. 查看平台统计
4. **不能访问用户网站内容**

## 🚀 优势

### 1. 简化的架构
- 减少了角色复杂性
- 降低了权限管理复杂度
- 简化了数据库关系

### 2. 更快的开发
- 专注于核心功能
- 减少了需要实现的功能
- 降低了测试复杂度

### 3. 更好的用户体验
- 简单直观的权限模型
- 用户容易理解
- 减少了学习成本

## 🔮 未来扩展

当需要时，可以逐步添加：

### 阶段2: 协作功能
- 重新引入 Editor 角色
- 实现邀请机制
- 添加权限管理

### 阶段3: 社交功能
- 重新引入 Follower 角色
- 实现关注功能
- 添加通知系统

### 阶段4: 高级功能
- 团队管理
- 高级权限控制
- 工作流程管理

## ✅ 重构完成状态

### 编译和运行状态
- **编译状态**: ✅ 无编译错误
- **功能完整**: ✅ 核心建站功能完全实现
- **数据一致**: ✅ 数据模型简化且一致
- **权限清晰**: ✅ 权限边界明确

### 完成的重构内容

#### 1. 数据模型重构 ✅
- 简化 `SiteRoleType` 枚举为只包含 `Owner`
- 移除 `InvitationStatus` 枚举
- 清理 `SiteUserRole` 模型，移除邀请相关字段
- 移除关注相关的 DTO 类

#### 2. 权限系统重构 ✅
- 移除关注和协作相关权限常量
- 简化权限检查逻辑，只保留核心网站管理权限
- 更新权限服务接口和实现

#### 3. 服务层重构 ✅
- 移除关注服务和邀请服务
- 简化权限服务实现
- 更新网站服务以支持简化的角色体系

#### 4. API层重构 ✅
- 移除关注和邀请相关的API接口
- 简化控制器方法
- 更新权限检查逻辑

#### 5. 前端界面重构 ✅
- 移除关注和协作相关的UI组件
- 简化Dashboard界面
- 更新角色显示逻辑和导航菜单

#### 6. 数据库迁移 ✅
- 创建数据库迁移脚本清理无用数据
- 创建C#迁移服务
- 添加迁移扩展方法支持自动迁移

#### 7. 测试和验证 ✅
- 创建验证脚本确保重构正确性
- 更新授权策略
- 确保编译和运行正常

## 🚀 使用指南

### 启动应用
```bash
# 1. 启动API服务
cd MolySite.API
dotnet run

# 2. 启动Web应用
cd MolySite.Web
dotnet run
```

### 执行迁移（可选）
如果需要清理现有数据库中的旧角色数据：

```csharp
// 在 Program.cs 中添加
builder.Services.AddSimplifyRoleSystemMigration();

// 在应用启动时执行
await app.MigrateSimplifyRoleSystemAsync();
```

### 验证系统
运行验证脚本确保系统正常：

```csharp
var validator = serviceProvider.GetService<SimplifiedRoleSystemValidation>();
var result = await validator.ValidateAsync();
```

## 🎯 核心功能

### 用户注册和登录
- 用户注册后自动获得 `User` 角色
- 可以创建和管理自己的网站
- 自动成为所创建网站的 `Owner`

### 网站管理
- 创建网站：用户可以创建多个网站
- 网站设置：完全控制网站配置
- 内容管理：管理网站内容和媒体
- 发布控制：控制网站的发布状态

### 管理员功能
- SuperAdmin 可以管理用户账户
- 查看平台统计信息
- 管理平台设置
- **不能访问用户网站隐私内容**

## 🔮 未来扩展计划

当业务需要时，可以分阶段重新引入复杂功能：

### 阶段2: 协作功能
- 重新引入 Editor 角色
- 实现邀请机制
- 添加权限管理界面

### 阶段3: 社交功能
- 重新引入 Follower 角色
- 实现关注功能
- 添加通知系统

### 阶段4: 高级功能
- 团队管理
- 工作流程
- 高级权限控制

## 🎉 总结

通过这次全面的简化重构，我们成功地：

1. **大幅降低了系统复杂性**: 从复杂的多角色体系简化为直观的所有者模式
2. **专注于核心价值**: 让用户能够快速创建和管理自己的网站
3. **保持了架构的扩展性**: 未来可以根据需求逐步添加功能
4. **提高了开发和维护效率**: 减少了代码量和复杂度
5. **确保了数据的一致性**: 通过迁移脚本保证数据完整性

这个简化的设计非常适合作为MVP版本快速推向市场，然后根据用户反馈和业务需求逐步演进。🚀
