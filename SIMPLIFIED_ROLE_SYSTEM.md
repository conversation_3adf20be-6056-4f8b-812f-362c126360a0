# MolySite 简化角色体系

## 🎯 简化目标

为了降低初期设计复杂性，我们移除了 Editor 和 Follower 角色以及相关的邀请机制，专注于核心功能。

## ✅ 简化后的角色体系

### 平台级角色
1. **SuperAdmin**: 平台管理员
   - 管理用户账户和平台设置
   - **不能访问用户网站隐私内容**
   
2. **User**: 普通用户
   - 可以创建网站
   - 拥有自己创建网站的完全控制权

### 网站级角色
1. **Owner**: 网站所有者（唯一角色）
   - 网站的完全控制权
   - 所有网站功能的访问权限

## 🔄 移除的功能

### 1. 移除的角色
- ❌ **Editor**: 网站编辑者角色
- ❌ **Follower**: 网站关注者角色

### 2. 移除的功能
- ❌ **邀请机制**: 邀请其他用户成为编辑者
- ❌ **关注功能**: 关注其他网站
- ❌ **通知系统**: 向关注者发送通知
- ❌ **协作功能**: 多用户协作编辑

### 3. 移除的文件
- `MolySite.Core/Interfaces/IFollowService.cs`
- `MolySite.Core/Services/FollowService.cs`
- `MolySite.Shared/Dtos/FollowedSiteDto.cs`
- `MolySite.Shared/Dtos/SiteFollowerDto.cs`
- `MolySite.API/Controllers/UsersController.cs`
- `MolySite.Web/Components/Pages/Dashboard/FollowedSites.razor`

### 4. 移除的权限
- `Site.Follow`
- `Site.ReceiveNotifications`
- `Site.Contact`
- `Site.ViewFollowerContent`

## 📊 简化后的数据模型

### SiteRoleType 枚举
```csharp
public enum SiteRoleType
{
    Owner = 1  // 网站所有者（唯一角色）
}
```

### 用户-网站关系
- 每个网站只有一个 Owner
- 用户可以拥有多个网站
- 每个网站的 Owner 拥有该网站的完全控制权

## 🎯 简化后的用户流程

### 用户注册
1. 注册成为 `User` 角色
2. 可以创建网站
3. 自动成为所创建网站的 `Owner`

### 网站管理
1. 用户创建网站后自动成为 Owner
2. Owner 拥有网站的所有权限：
   - 编辑网站设置
   - 管理内容
   - 发布/取消发布
   - 删除网站
   - 查看统计数据

### SuperAdmin 功能
1. 管理用户账户（基本信息）
2. 管理平台设置
3. 查看平台统计
4. **不能访问用户网站内容**

## 🚀 优势

### 1. 简化的架构
- 减少了角色复杂性
- 降低了权限管理复杂度
- 简化了数据库关系

### 2. 更快的开发
- 专注于核心功能
- 减少了需要实现的功能
- 降低了测试复杂度

### 3. 更好的用户体验
- 简单直观的权限模型
- 用户容易理解
- 减少了学习成本

## 🔮 未来扩展

当需要时，可以逐步添加：

### 阶段2: 协作功能
- 重新引入 Editor 角色
- 实现邀请机制
- 添加权限管理

### 阶段3: 社交功能
- 重新引入 Follower 角色
- 实现关注功能
- 添加通知系统

### 阶段4: 高级功能
- 团队管理
- 高级权限控制
- 工作流程管理

## ✅ 当前状态

- **编译状态**: ✅ 无编译错误
- **功能完整**: ✅ 核心功能完全实现
- **数据一致**: ✅ 数据模型简化且一致
- **权限清晰**: ✅ 权限边界明确

## 🎉 总结

通过简化角色体系，我们：

1. **降低了复杂性**: 从5个角色简化为3个角色
2. **专注核心功能**: 用户创建和管理网站
3. **保持扩展性**: 架构支持未来功能扩展
4. **提高开发效率**: 减少了需要实现和维护的代码

这个简化的设计让我们能够快速推出MVP版本，然后根据用户反馈逐步添加更复杂的功能。
