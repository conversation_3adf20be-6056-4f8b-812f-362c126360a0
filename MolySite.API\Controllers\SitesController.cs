using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MolySite.Core.Controllers;
using MolySite.Core.Interfaces;
using MolySite.Core.Models;
using MolySite.Shared.Dtos;

namespace MolySite.API.Controllers
{
    /// <summary>
    /// 网站管理控制器
    /// </summary>
    [Route("api/[controller]")]
    [Authorize]
    public class SitesController : BaseApiController
    {
        private readonly ISiteService _siteService;
        private readonly IPermissionService _permissionService;

        private readonly ILogger<SitesController> _logger;

        public SitesController(
            ISiteService siteService,
            IPermissionService permissionService,
            ILogger<SitesController> logger)
        {
            _siteService = siteService;
            _permissionService = permissionService;
            _logger = logger;
        }

        /// <summary>
        /// 获取当前用户的所有网站
        /// </summary>
        [HttpGet("my-sites")]
        public async Task<ActionResult<List<SiteDto>>> GetMySites()
        {
            var userId = GetCurrentUserId();
            var result = await _siteService.GetUserSitesAsync(userId);

            return HandleResultForAction(result);
        }

        /// <summary>
        /// 获取所有网站（管理员权限）
        /// </summary>
        [HttpGet("all")]
        public async Task<ActionResult<List<SiteDto>>> GetAllSites([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 20)
        {
            var userId = GetCurrentUserId();
            var result = await _siteService.GetAllSitesAsync(userId, pageNumber, pageSize);

            return HandleResultForAction(result.IsSuccess
                ? Result<List<SiteDto>>.Success(result.Data!.Items)
                : Result<List<SiteDto>>.Failure(result.ErrorMessage!, result.ErrorCode!));
        }

        /// <summary>
        /// 获取指定网站信息
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<SiteDto>> GetSite(Guid id)
        {
            var userId = GetCurrentUserId();

            // 检查用户是否有权限访问该网站
            var canAccessResult = await _permissionService.CanUserAccessSiteAsync(userId, id);
            if (!canAccessResult.IsSuccess || !canAccessResult.Data)
            {
                return Forbid();
            }

            var result = await _siteService.GetSiteByIdAsync(id);
            return HandleResultForAction(result);
        }

        /// <summary>
        /// 创建新网站
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<SiteDto>> CreateSite([FromBody] CreateSiteDto createSiteDto)
        {
            var userId = GetCurrentUserId();
            var result = await _siteService.CreateSiteAsync(createSiteDto, userId);

            if (result.IsSuccess)
            {
                return CreatedAtAction(nameof(GetSite), new { id = result.Data!.Id }, result.Data);
            }

            return HandleResultForAction(result);
        }

        /// <summary>
        /// 更新网站信息
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<SiteDto>> UpdateSite(Guid id, [FromBody] UpdateSiteDto updateSiteDto)
        {
            var userId = GetCurrentUserId();
            var result = await _siteService.UpdateSiteAsync(id, updateSiteDto, userId);

            return HandleResultForAction(result);
        }

        /// <summary>
        /// 删除网站
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteSite(Guid id)
        {
            var userId = GetCurrentUserId();
            var result = await _siteService.DeleteSiteAsync(id, userId);

            if (result.IsSuccess)
            {
                return NoContent();
            }

            return HandleResultForAction(result);
        }

        /// <summary>
        /// 根据域名获取网站
        /// </summary>
        /// <param name="domain">域名</param>
        /// <returns>网站信息</returns>
        [HttpGet("by-domain/{domain}")]
        [AllowAnonymous]
        public async Task<ActionResult<SiteDto>> GetSiteByDomain(string domain)
        {
            var result = await _siteService.GetSiteByDomainAsync(domain);
            return HandleResultForAction(result);
        }

        /// <summary>
        /// 检查域名是否可用
        /// </summary>
        /// <param name="domain">域名</param>
        /// <param name="excludeSiteId">排除的网站ID</param>
        /// <returns>是否可用</returns>
        [HttpGet("check-domain")]
        public async Task<ActionResult<bool>> CheckDomainAvailability([FromQuery] string domain, [FromQuery] Guid? excludeSiteId = null)
        {
            var result = await _siteService.IsDomainAvailableAsync(domain, excludeSiteId);
            return HandleResultForAction(result);
        }

        /// <summary>
        /// 发布网站
        /// </summary>
        /// <param name="id">网站ID</param>
        /// <returns>发布结果</returns>
        [HttpPost("{id}/publish")]
        public async Task<ActionResult> PublishSite(Guid id)
        {
            var userId = GetCurrentUserId();
            var result = await _siteService.PublishSiteAsync(id, userId);

            if (result.IsSuccess)
            {
                return NoContent();
            }

            return HandleResultForAction(result);
        }

        /// <summary>
        /// 取消发布网站
        /// </summary>
        /// <param name="id">网站ID</param>
        /// <returns>取消发布结果</returns>
        [HttpPost("{id}/unpublish")]
        public async Task<ActionResult> UnpublishSite(Guid id)
        {
            var userId = GetCurrentUserId();
            var result = await _siteService.UnpublishSiteAsync(id, userId);

            if (result.IsSuccess)
            {
                return NoContent();
            }

            return HandleResultForAction(result);
        }



    }
}
