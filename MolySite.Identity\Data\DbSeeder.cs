using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MolySite.Identity.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MolySite.Identity.Data
{
    /// <summary>
    /// 数据库种子数据
    /// </summary>
    public static class DbSeeder
    {
        /// <summary>
        /// 初始化种子数据（使用简化的DataSeeder）
        /// </summary>
        public static async Task SeedAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole<Guid>>>();
            var loggerFactory = scope.ServiceProvider.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger("DbSeeder");

            try
            {
                logger.LogInformation("🌱 开始执行简化的种子数据...");

                // 使用更新的DataSeeder来重置和种子数据
                await DataSeeder.ResetAndSeedDataAsync(context, userManager, roleManager);

                logger.LogInformation("✅ 简化的种子数据执行完成！");

                // 记录创建的账号信息
                logger.LogInformation("📋 创建的测试账号:");
                logger.LogInformation("SuperAdmin: superadmin / SuperAdmin@2024!");
                logger.LogInformation("User: user / User@2024!");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "❌ 数据库种子数据初始化失败");
                throw;
            }
        }


    }
}
