using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MolySite.Identity.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MolySite.Identity.Data
{
    /// <summary>
    /// 数据库种子数据
    /// </summary>
    public static class DbSeeder
    {
        /// <summary>
        /// 初始化种子数据
        /// </summary>
        public static async Task SeedAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole<Guid>>>();
            var loggerFactory = scope.ServiceProvider.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger("DbSeeder");

            try
            {
                // 创建默认角色
                await <PERSON><PERSON><PERSON><PERSON>sAsync(roleManager, logger);

                // 创建默认用户
                await CreateUsersAsync(userManager, logger);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "数据库种子数据初始化失败");
                throw;
            }
        }

        /// <summary>
        /// 创建默认角色
        /// </summary>
        private static async Task CreateRolesAsync(RoleManager<IdentityRole<Guid>> roleManager, ILogger logger)
        {
            var roles = new[] { "SuperAdmin", "User", "SiteEditor" };

            foreach (var roleName in roles)
            {
                if (!await roleManager.RoleExistsAsync(roleName))
                {
                    var role = new IdentityRole<Guid>
                    {
                        Id = Guid.NewGuid(),
                        Name = roleName,
                        NormalizedName = roleName.ToUpper()
                    };

                    var result = await roleManager.CreateAsync(role);
                    if (result.Succeeded)
                    {
                        logger.LogInformation("角色 {RoleName} 创建成功", roleName);
                    }
                    else
                    {
                        logger.LogError("角色 {RoleName} 创建失败: {Errors}", roleName, 
                            string.Join(", ", result.Errors.Select(e => e.Description)));
                    }
                }
            }
        }

        /// <summary>
        /// 创建默认用户
        /// </summary>
        private static async Task CreateUsersAsync(UserManager<ApplicationUser> userManager, ILogger logger)
        {
            // 创建SaaS平台超级管理员
            var superAdminEmail = "<EMAIL>";
            var superAdminUser = await userManager.FindByEmailAsync(superAdminEmail);

            if (superAdminUser == null)
            {
                superAdminUser = new ApplicationUser
                {
                    Id = Guid.NewGuid(),
                    UserName = "superadmin",
                    Email = superAdminEmail,
                    EmailConfirmed = true,
                    DisplayName = "SaaS平台超级管理员",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    PlatformRoles = new List<string> { "SuperAdmin" },
                    Permissions = new List<string>()
                };

                var result = await userManager.CreateAsync(superAdminUser, "SuperAdmin123!");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(superAdminUser, "SuperAdmin");
                    logger.LogInformation("SaaS平台超级管理员创建成功: {Email}", superAdminEmail);
                }
                else
                {
                    logger.LogError("SaaS平台超级管理员创建失败: {Errors}",
                        string.Join(", ", result.Errors.Select(e => e.Description)));
                }
            }

            // 创建普通用户（网站管理员）
            var userEmail = "<EMAIL>";
            var normalUser = await userManager.FindByEmailAsync(userEmail);

            if (normalUser == null)
            {
                normalUser = new ApplicationUser
                {
                    Id = Guid.NewGuid(),
                    UserName = "user",
                    Email = userEmail,
                    EmailConfirmed = true,
                    DisplayName = "普通用户",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    PlatformRole = PlatformRole.User,
                    PlatformRoles = new List<string> { "User" },
                    Permissions = new List<string>()
                };

                var result = await userManager.CreateAsync(normalUser, "User123!");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(normalUser, "User");
                    logger.LogInformation("普通用户创建成功: {Email}", userEmail);
                }
                else
                {
                    logger.LogError("普通用户创建失败: {Errors}",
                        string.Join(", ", result.Errors.Select(e => e.Description)));
                }
            }

            // 创建站点编辑者（仅作为网站级角色存在）
            var siteEditorEmail = "<EMAIL>";
            var siteEditorUser = await userManager.FindByEmailAsync(siteEditorEmail);

            if (siteEditorUser == null)
            {
                siteEditorUser = new ApplicationUser
                {
                    Id = Guid.NewGuid(),
                    UserName = "siteeditor",
                    Email = siteEditorEmail,
                    EmailConfirmed = true,
                    DisplayName = "网站编辑者",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    PlatformRole = PlatformRole.User, // 平台级角色是User
                    PlatformRoles = new List<string> { "User" }, // 平台级角色是User
                    Permissions = new List<string>()
                };

                var result = await userManager.CreateAsync(siteEditorUser, "SiteEditor123!");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(siteEditorUser, "User"); // 平台级角色是User
                    logger.LogInformation("网站编辑者用户创建成功: {Email}", siteEditorEmail);
                }
                else
                {
                    logger.LogError("网站编辑者用户创建失败: {Errors}",
                        string.Join(", ", result.Errors.Select(e => e.Description)));
                }
            }

            // 保留原有的admin用户作为兼容性（映射到SuperAdmin）
            var adminEmail = "<EMAIL>";
            var adminUser = await userManager.FindByEmailAsync(adminEmail);

            if (adminUser == null)
            {
                adminUser = new ApplicationUser
                {
                    Id = Guid.NewGuid(),
                    UserName = "admin",
                    Email = adminEmail,
                    EmailConfirmed = true,
                    DisplayName = "系统管理员（兼容）",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    PlatformRoles = new List<string> { "SuperAdmin" },
                    Permissions = new List<string>()
                };

                var result = await userManager.CreateAsync(adminUser, "Admin123!");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(adminUser, "SuperAdmin");
                    logger.LogInformation("兼容性管理员用户创建成功: {Email}", adminEmail);
                }
                else
                {
                    logger.LogError("兼容性管理员用户创建失败: {Errors}",
                        string.Join(", ", result.Errors.Select(e => e.Description)));
                }
            }
        }
    }
}
