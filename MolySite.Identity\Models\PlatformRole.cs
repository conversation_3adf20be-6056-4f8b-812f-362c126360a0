namespace MolySite.Identity.Models
{
    /// <summary>
    /// 平台角色枚举
    /// </summary>
    public enum PlatformRole
    {
        /// <summary>
        /// 平台超级管理员 - 拥有平台管理权限，但不能访问用户网站隐私内容
        /// </summary>
        SuperAdmin = 1,

        /// <summary>
        /// 普通用户（注册默认角色）- 可以创建网站，可以被邀请为其他网站的编辑者
        /// </summary>
        User = 2
    }

    /// <summary>
    /// 平台角色扩展方法
    /// </summary>
    public static class PlatformRoleExtensions
    {
        /// <summary>
        /// 获取角色显示名称
        /// </summary>
        public static string GetDisplayName(this PlatformRole role)
        {
            return role switch
            {
                PlatformRole.SuperAdmin => "平台超级管理员",
                PlatformRole.User => "普通用户",
                _ => role.ToString()
            };
        }

        /// <summary>
        /// 获取角色描述
        /// </summary>
        public static string GetDescription(this PlatformRole role)
        {
            return role switch
            {
                PlatformRole.SuperAdmin => "拥有平台管理权限，可以管理系统设置和用户账户基本信息，但不能访问用户网站隐私内容",
                PlatformRole.User => "可以创建网站并成为网站拥有者，也可以被邀请为其他网站的编辑者或查看者",
                _ => string.Empty
            };
        }

        /// <summary>
        /// 检查是否为管理员角色
        /// </summary>
        public static bool IsAdminRole(this PlatformRole role)
        {
            return role == PlatformRole.SuperAdmin;
        }

        /// <summary>
        /// 从字符串转换为角色枚举
        /// </summary>
        public static PlatformRole? FromString(string roleString)
        {
            return roleString?.ToLower() switch
            {
                "superadmin" => PlatformRole.SuperAdmin,
                "user" => PlatformRole.User,
                "siteowner" => PlatformRole.User, // 向后兼容
                _ => null
            };
        }

        /// <summary>
        /// 转换为字符串
        /// </summary>
        public static string ToString(this PlatformRole role)
        {
            return role switch
            {
                PlatformRole.SuperAdmin => "SuperAdmin",
                PlatformRole.User => "User",
                _ => role.ToString()
            };
        }
    }
}
