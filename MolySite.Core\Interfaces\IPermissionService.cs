using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MolySite.Core.Models;

namespace MolySite.Core.Interfaces
{
    /// <summary>
    /// 统一权限服务接口
    /// </summary>
    public interface IPermissionService
    {
        /// <summary>
        /// 检查用户是否有平台权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="permission">权限名称</param>
        /// <returns>是否有权限</returns>
        Task<Result<bool>> HasPlatformPermissionAsync(Guid userId, string permission);

        /// <summary>
        /// 检查用户是否有网站权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="siteId">网站ID</param>
        /// <param name="permission">权限名称</param>
        /// <returns>是否有权限</returns>
        Task<Result<bool>> HasSitePermissionAsync(Guid userId, Guid siteId, string permission);

        /// <summary>
        /// 获取用户的平台权限列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>权限列表</returns>
        Task<Result<List<string>>> GetUserPlatformPermissionsAsync(Guid userId);

        /// <summary>
        /// 获取用户在指定网站的权限列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="siteId">网站ID</param>
        /// <returns>权限列表</returns>
        Task<Result<List<string>>> GetUserSitePermissionsAsync(Guid userId, Guid siteId);

        /// <summary>
        /// 检查用户是否可以访问网站
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="siteId">网站ID</param>
        /// <returns>是否可以访问</returns>
        Task<Result<bool>> CanUserAccessSiteAsync(Guid userId, Guid siteId);

        /// <summary>
        /// 获取用户可访问的网站ID列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>网站ID列表</returns>
        Task<Result<List<Guid>>> GetUserAccessibleSiteIdsAsync(Guid userId);

        /// <summary>
        /// 获取用户可编辑的网站ID列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>网站ID列表</returns>
        Task<Result<List<Guid>>> GetUserEditableSiteIdsAsync(Guid userId);

        /// <summary>
        /// 为用户分配网站角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="siteId">网站ID</param>
        /// <param name="role">角色</param>
        /// <param name="grantedBy">授权者ID</param>
        /// <returns>分配结果</returns>
        Task<Result> AssignSiteRoleAsync(Guid userId, Guid siteId, string role, Guid grantedBy);

        /// <summary>
        /// 移除用户的网站角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="siteId">网站ID</param>
        /// <param name="removedBy">移除者ID</param>
        /// <returns>移除结果</returns>
        Task<Result> RemoveSiteRoleAsync(Guid userId, Guid siteId, Guid removedBy);

        /// <summary>
        /// 获取网站的用户角色列表
        /// </summary>
        /// <param name="siteId">网站ID</param>
        /// <param name="requestUserId">请求用户ID</param>
        /// <returns>用户角色列表</returns>
        Task<Result<List<SiteUserRoleDto>>> GetSiteUserRolesAsync(Guid siteId, Guid requestUserId);
    }

    /// <summary>
    /// 网站用户角色DTO
    /// </summary>
    public class SiteUserRoleDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 用户邮箱
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 角色
        /// </summary>
        public string Role { get; set; } = string.Empty;

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 授权时间
        /// </summary>
        public DateTime GrantedAt { get; set; }

        /// <summary>
        /// 授权者ID
        /// </summary>
        public Guid? GrantedBy { get; set; }


    }
}
