using System.Collections.Generic;

namespace MolySite.Identity.Authorization
{
    /// <summary>
    /// 网站权限常量定义
    /// </summary>
    public static class SitePermissions
    {
        /// <summary>
        /// 平台级权限（SuperAdmin专用）
        /// </summary>
        public static class Platform
        {
            /// <summary>
            /// 完全访问权限（SuperAdmin专用）
            /// </summary>
            public const string FullAccess = "Platform.FullAccess";

            /// <summary>
            /// 查看系统设置
            /// </summary>
            public const string ViewSettings = "Platform.ViewSettings";

            /// <summary>
            /// 修改系统设置
            /// </summary>
            public const string ManageSettings = "Platform.ManageSettings";

            /// <summary>
            /// 查看系统日志
            /// </summary>
            public const string ViewLogs = "Platform.ViewLogs";

            /// <summary>
            /// 查看系统统计数据
            /// </summary>
            public const string ViewStatistics = "Platform.ViewStatistics";

            /// <summary>
            /// 管理订阅计划
            /// </summary>
            public const string ManageSubscriptionPlans = "Platform.ManageSubscriptionPlans";

            /// <summary>
            /// 查看所有用户
            /// </summary>
            public const string ViewAllUsers = "Platform.ViewAllUsers";

            /// <summary>
            /// 暂停用户账户
            /// </summary>
            public const string SuspendUsers = "Platform.SuspendUsers";

            /// <summary>
            /// 查看所有网站（仅元数据）
            /// </summary>
            public const string ViewAllSites = "Platform.ViewAllSites";

            /// <summary>
            /// 暂停网站
            /// </summary>
            public const string SuspendSites = "Platform.SuspendSites";
        }

        /// <summary>
        /// User权限（平台级普通用户权限）
        /// </summary>
        public static class User
        {
            /// <summary>
            /// 创建网站
            /// </summary>
            public const string CreateSite = "User.CreateSite";

            /// <summary>
            /// 查看自己的网站列表
            /// </summary>
            public const string ViewOwnSites = "User.ViewOwnSites";

            /// <summary>
            /// 管理个人资料
            /// </summary>
            public const string ManageProfile = "User.ManageProfile";

            /// <summary>
            /// 查看账单信息
            /// </summary>
            public const string ViewBilling = "User.ViewBilling";

            /// <summary>
            /// 关注网站
            /// </summary>
            public const string FollowSites = "User.FollowSites";
        }

        /// <summary>
        /// 网站级权限
        /// </summary>
        public static class Site
        {
            /// <summary>
            /// 查看网站设置
            /// </summary>
            public const string ViewSettings = "Site.ViewSettings";

            /// <summary>
            /// 编辑网站设置
            /// </summary>
            public const string EditSettings = "Site.EditSettings";

            /// <summary>
            /// 删除网站
            /// </summary>
            public const string Delete = "Site.Delete";

            /// <summary>
            /// 绑定域名
            /// </summary>
            public const string BindDomain = "Site.BindDomain";

            /// <summary>
            /// 发布网站
            /// </summary>
            public const string Publish = "Site.Publish";

            /// <summary>
            /// 管理网站主题
            /// </summary>
            public const string ManageTheme = "Site.ManageTheme";

            /// <summary>
            /// 编辑自定义代码
            /// </summary>
            public const string EditCustomCode = "Site.EditCustomCode";

            /// <summary>
            /// 查看网站统计
            /// </summary>
            public const string ViewAnalytics = "Site.ViewAnalytics";

            /// <summary>
            /// 管理网站用户
            /// </summary>
            public const string ManageUsers = "Site.ManageUsers";

            /// <summary>
            /// 邀请用户
            /// </summary>
            public const string InviteUsers = "Site.InviteUsers";

            /// <summary>
            /// 管理订阅
            /// </summary>
            public const string ManageSubscription = "Site.ManageSubscription";

            /// <summary>
            /// 关注网站
            /// </summary>
            public const string Follow = "Site.Follow";

            /// <summary>
            /// 接收网站通知
            /// </summary>
            public const string ReceiveNotifications = "Site.ReceiveNotifications";

            /// <summary>
            /// 联系网站
            /// </summary>
            public const string Contact = "Site.Contact";

            /// <summary>
            /// 查看关注者专属内容
            /// </summary>
            public const string ViewFollowerContent = "Site.ViewFollowerContent";
        }

        /// <summary>
        /// 内容管理权限
        /// </summary>
        public static class Content
        {
            /// <summary>
            /// 查看内容
            /// </summary>
            public const string View = "Content.View";

            /// <summary>
            /// 编辑内容
            /// </summary>
            public const string Edit = "Content.Edit";

            /// <summary>
            /// 创建内容
            /// </summary>
            public const string Create = "Content.Create";

            /// <summary>
            /// 删除内容
            /// </summary>
            public const string Delete = "Content.Delete";

            /// <summary>
            /// 管理媒体文件
            /// </summary>
            public const string ManageMedia = "Content.ManageMedia";

            /// <summary>
            /// 管理菜单
            /// </summary>
            public const string ManageMenu = "Content.ManageMenu";

            /// <summary>
            /// 管理SEO设置
            /// </summary>
            public const string ManageSEO = "Content.ManageSEO";
        }

        /// <summary>
        /// 获取SuperAdmin角色的所有权限
        /// </summary>
        public static List<string> GetSuperAdminPermissions()
        {
            return new List<string>
            {
                // 平台权限
                Platform.FullAccess,
                Platform.ViewSettings,
                Platform.ManageSettings,
                Platform.ViewLogs,
                Platform.ViewStatistics,
                Platform.ManageSubscriptionPlans,
                Platform.ViewAllUsers,
                Platform.SuspendUsers,
                Platform.ViewAllSites,
                Platform.SuspendSites,

                // User权限（SuperAdmin也可以创建网站）
                User.CreateSite,
                User.ViewOwnSites,
                User.ManageProfile,
                User.ViewBilling,
                User.FollowSites,

                // 网站权限（SuperAdmin只能进行平台管理，不能访问网站内容）
                Site.ViewSettings,  // 只能查看基本设置
                Site.ManageSubscription  // 管理订阅状态
                // 注意：SuperAdmin不能访问网站内容、编辑设置、发布等涉及隐私的操作
            };
        }

        /// <summary>
        /// 获取User角色的权限（平台级普通用户）
        /// </summary>
        public static List<string> GetUserPermissions()
        {
            return new List<string>
            {
                // User基础权限
                User.CreateSite,
                User.ViewOwnSites,
                User.ManageProfile,
                User.ViewBilling,
                User.FollowSites
            };
        }

        /// <summary>
        /// 获取网站所有者在特定网站的权限
        /// </summary>
        public static List<string> GetSiteOwnerSitePermissions()
        {
            return new List<string>
            {
                // 网站管理权限
                Site.ViewSettings,
                Site.EditSettings,
                Site.Delete,
                Site.BindDomain,
                Site.Publish,
                Site.ManageTheme,
                Site.EditCustomCode,
                Site.ViewAnalytics,
                Site.ManageUsers,
                Site.InviteUsers,
                Site.ManageSubscription,

                // 内容权限
                Content.View,
                Content.Edit,
                Content.Create,
                Content.Delete,
                Content.ManageMedia,
                Content.ManageMenu,
                Content.ManageSEO
            };
        }

        /// <summary>
        /// 获取网站关注者权限
        /// </summary>
        public static List<string> GetSiteFollowerPermissions()
        {
            return new List<string>
            {
                // 关注者权限
                Site.Follow,
                Site.ReceiveNotifications,
                Site.Contact,
                Site.ViewFollowerContent
            };
        }



        /// <summary>
        /// 获取网站所有者权限（向后兼容）
        /// </summary>
        public static List<string> GetSiteOwnerPermissions()
        {
            return GetSiteOwnerSitePermissions();
        }

        /// <summary>
        /// 获取网站编辑者权限
        /// </summary>
        public static List<string> GetSiteEditorPermissions()
        {
            return new List<string>
            {
                // 基础查看权限
                Site.ViewSettings,
                Site.ViewAnalytics,

                // 内容编辑权限
                Content.View,
                Content.Edit,
                Content.Create,
                Content.Delete,
                Content.ManageMedia,
                Content.ManageMenu,
                Content.ManageSEO
            };
        }
    }
}
