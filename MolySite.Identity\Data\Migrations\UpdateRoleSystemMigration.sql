-- 角色体系重构迁移脚本
-- 将 SiteOwner 平台角色改为 User，移除平台级的 SiteEditor
-- 添加 Follower 网站级角色

BEGIN;

-- 1. 更新用户的平台角色
-- 将所有 PlatformRole = 2 (SiteOwner) 的用户改为 PlatformRole = 2 (User)
-- 将所有 PlatformRole = 3 (SiteEditor) 的用户改为 PlatformRole = 2 (User)
UPDATE "AspNetUsers" 
SET "PlatformRole" = 2 
WHERE "PlatformRole" IN (2, 3);

-- 2. 更新用户的平台角色字符串表示
-- 将 PlatformRoles 字段中的 "SiteOwner" 和 "SiteEditor" 都改为 "User"
UPDATE "AspNetUsers" 
SET "PlatformRoles" = 'User'
WHERE "PlatformRoles" IN ('SiteOwner', 'SiteEditor');

-- 3. 处理混合角色的情况（如果存在）
UPDATE "AspNetUsers" 
SET "PlatformRoles" = REPLACE("PlatformRoles", 'SiteOwner', 'User')
WHERE "PlatformRoles" LIKE '%SiteOwner%';

UPDATE "AspNetUsers" 
SET "PlatformRoles" = REPLACE("PlatformRoles", 'SiteEditor', 'User')
WHERE "PlatformRoles" LIKE '%SiteEditor%';

-- 4. 清理重复的角色（如果替换后出现重复）
UPDATE "AspNetUsers" 
SET "PlatformRoles" = 'User'
WHERE "PlatformRoles" LIKE '%User%User%' OR "PlatformRoles" LIKE '%User,User%';

-- 5. 确保所有非SuperAdmin用户都是User角色
UPDATE "AspNetUsers" 
SET "PlatformRole" = 2, "PlatformRoles" = 'User'
WHERE "PlatformRole" NOT IN (1) AND ("PlatformRole" IS NULL OR "PlatformRole" = 0);

-- 6. 添加数据验证检查
-- 检查是否还有无效的平台角色
DO $$
DECLARE
    invalid_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO invalid_count
    FROM "AspNetUsers" 
    WHERE "PlatformRole" NOT IN (1, 2) OR "PlatformRole" IS NULL;
    
    IF invalid_count > 0 THEN
        RAISE EXCEPTION '发现 % 个用户的平台角色无效，请检查数据', invalid_count;
    END IF;
    
    RAISE NOTICE '平台角色更新完成，所有用户角色已标准化';
END $$;

-- 7. 更新网站用户角色表的注释（如果需要）
-- SiteUserRole 表中的 Role 字段现在支持：
-- 1 = Owner (网站所有者)
-- 2 = Editor (网站编辑者) 
-- 3 = Follower (网站关注者)

-- 8. 添加索引优化（如果不存在）
-- 为关注功能优化查询性能
CREATE INDEX IF NOT EXISTS "IX_SiteUserRoles_Role_IsActive" 
ON "SiteUserRoles" ("Role", "IsActive") 
WHERE "Role" = 3; -- Follower 角色

CREATE INDEX IF NOT EXISTS "IX_SiteUserRoles_UserId_Role_IsActive" 
ON "SiteUserRoles" ("UserId", "Role", "IsActive");

CREATE INDEX IF NOT EXISTS "IX_SiteUserRoles_SiteId_Role_IsActive" 
ON "SiteUserRoles" ("SiteId", "Role", "IsActive");

-- 9. 数据完整性检查
DO $$
DECLARE
    total_users INTEGER;
    superadmin_count INTEGER;
    user_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_users FROM "AspNetUsers";
    SELECT COUNT(*) INTO superadmin_count FROM "AspNetUsers" WHERE "PlatformRole" = 1;
    SELECT COUNT(*) INTO user_count FROM "AspNetUsers" WHERE "PlatformRole" = 2;
    
    RAISE NOTICE '迁移完成统计:';
    RAISE NOTICE '总用户数: %', total_users;
    RAISE NOTICE 'SuperAdmin 用户数: %', superadmin_count;
    RAISE NOTICE 'User 用户数: %', user_count;
    
    IF (superadmin_count + user_count) != total_users THEN
        RAISE EXCEPTION '角色分配不完整，请检查数据';
    END IF;
END $$;

COMMIT;

-- 迁移完成后的验证查询
-- 可以手动运行这些查询来验证迁移结果

-- 查看所有用户的角色分布
-- SELECT "PlatformRole", "PlatformRoles", COUNT(*) as count
-- FROM "AspNetUsers" 
-- GROUP BY "PlatformRole", "PlatformRoles"
-- ORDER BY "PlatformRole";

-- 查看网站用户角色分布
-- SELECT "Role", "IsActive", COUNT(*) as count
-- FROM "SiteUserRoles"
-- GROUP BY "Role", "IsActive"
-- ORDER BY "Role", "IsActive";

-- 检查是否有孤立的网站用户角色记录
-- SELECT COUNT(*) as orphaned_roles
-- FROM "SiteUserRoles" sur
-- LEFT JOIN "AspNetUsers" u ON sur."UserId" = u."Id"
-- LEFT JOIN "Sites" s ON sur."SiteId" = s."Id"
-- WHERE u."Id" IS NULL OR s."Id" IS NULL;
