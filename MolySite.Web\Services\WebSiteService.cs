using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MolySite.Core.Interfaces;
using MolySite.Shared.Dtos;
using MolySite.Web.Models;

namespace MolySite.Web.Services
{
    /// <summary>
    /// Web层网站服务适配器，将Core层服务适配为Web层接口
    /// </summary>
    public class WebSiteService : ISiteService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<WebSiteService> _logger;

        public WebSiteService(HttpClient httpClient, ILogger<WebSiteService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<List<SiteDto>> GetMySitesAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("api/sites/my-sites");
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var apiResponse = JsonSerializer.Deserialize<JsonElement>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (apiResponse.TryGetProperty("success", out var successElement) && successElement.GetBoolean() &&
                        apiResponse.TryGetProperty("data", out var dataElement))
                    {
                        var sites = JsonSerializer.Deserialize<List<SiteDto>>(dataElement.GetRawText(), new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });

                        return sites ?? new List<SiteDto>();
                    }
                }

                _logger.LogWarning("获取用户网站失败: {StatusCode}", response.StatusCode);
                return new List<SiteDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户网站时发生异常");
                return new List<SiteDto>();
            }
        }

        /// <inheritdoc/>
        public async Task<List<SiteDto>> GetAllSitesAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("api/sites/all");
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var apiResponse = JsonSerializer.Deserialize<JsonElement>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (apiResponse.TryGetProperty("success", out var successElement) && successElement.GetBoolean() &&
                        apiResponse.TryGetProperty("data", out var dataElement))
                    {
                        var sites = JsonSerializer.Deserialize<List<SiteDto>>(dataElement.GetRawText(), new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });

                        return sites ?? new List<SiteDto>();
                    }
                }

                _logger.LogWarning("获取所有网站失败: {StatusCode}", response.StatusCode);
                return new List<SiteDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有网站时发生异常");
                return new List<SiteDto>();
            }
        }

        /// <inheritdoc/>
        public async Task<SiteDto?> GetSiteAsync(Guid siteId)
        {
            return await GetSiteByIdAsync(siteId);
        }

        /// <inheritdoc/>
        public async Task<SiteDto?> GetSiteByIdAsync(Guid siteId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"api/sites/{siteId}");
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var apiResponse = JsonSerializer.Deserialize<JsonElement>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (apiResponse.TryGetProperty("success", out var successElement) && successElement.GetBoolean() &&
                        apiResponse.TryGetProperty("data", out var dataElement))
                    {
                        return JsonSerializer.Deserialize<SiteDto>(dataElement.GetRawText(), new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });
                    }
                }

                _logger.LogWarning("获取网站详情失败: {SiteId}, {StatusCode}", siteId, response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取网站详情时发生异常: {SiteId}", siteId);
                return null;
            }
        }

        /// <inheritdoc/>
        public async Task<SiteDto?> GetSiteByDomainAsync(string domain)
        {
            try
            {
                var response = await _httpClient.GetAsync($"api/sites/by-domain/{Uri.EscapeDataString(domain)}");
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var apiResponse = JsonSerializer.Deserialize<JsonElement>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (apiResponse.TryGetProperty("success", out var successElement) && successElement.GetBoolean() &&
                        apiResponse.TryGetProperty("data", out var dataElement))
                    {
                        return JsonSerializer.Deserialize<SiteDto>(dataElement.GetRawText(), new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });
                    }
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    _logger.LogDebug("域名对应的网站不存在: {Domain}", domain);
                    return null;
                }

                _logger.LogWarning("根据域名获取网站失败: {Domain}, {StatusCode}", domain, response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据域名获取网站时发生异常: {Domain}", domain);
                return null;
            }
        }

        /// <inheritdoc/>
        public async Task<SiteDto?> CreateSiteAsync(CreateSiteDto createSiteDto)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync("api/sites", createSiteDto);
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var apiResponse = JsonSerializer.Deserialize<JsonElement>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (apiResponse.TryGetProperty("success", out var successElement) && successElement.GetBoolean() &&
                        apiResponse.TryGetProperty("data", out var dataElement))
                    {
                        return JsonSerializer.Deserialize<SiteDto>(dataElement.GetRawText(), new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });
                    }
                }

                _logger.LogWarning("创建网站失败: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建网站时发生异常");
                return null;
            }
        }

        /// <inheritdoc/>
        public async Task<SiteDto?> UpdateSiteAsync(Guid siteId, UpdateSiteDto updateSiteDto)
        {
            try
            {
                var response = await _httpClient.PutAsJsonAsync($"api/sites/{siteId}", updateSiteDto);
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var apiResponse = JsonSerializer.Deserialize<JsonElement>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (apiResponse.TryGetProperty("success", out var successElement) && successElement.GetBoolean() &&
                        apiResponse.TryGetProperty("data", out var dataElement))
                    {
                        return JsonSerializer.Deserialize<SiteDto>(dataElement.GetRawText(), new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });
                    }
                }

                _logger.LogWarning("更新网站失败: {SiteId}, {StatusCode}", siteId, response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新网站时发生异常: {SiteId}", siteId);
                return null;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> DeleteSiteAsync(Guid siteId)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"api/sites/{siteId}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除网站时发生异常: {SiteId}", siteId);
                return false;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> IsDomainAvailableAsync(string domain)
        {
            try
            {
                var url = $"api/sites/check-domain?domain={Uri.EscapeDataString(domain)}";

                var response = await _httpClient.GetAsync(url);
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var apiResponse = JsonSerializer.Deserialize<JsonElement>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (apiResponse.TryGetProperty("success", out var successElement) && successElement.GetBoolean() &&
                        apiResponse.TryGetProperty("data", out var dataElement))
                    {
                        return dataElement.GetBoolean();
                    }
                }

                _logger.LogWarning("检查域名可用性失败: {Domain}, {StatusCode}", domain, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查域名可用性时发生异常: {Domain}", domain);
                return false;
            }
        }


    }
}
