@* 全新设计的现代化Dashboard导航菜单组件 *@
@using Microsoft.AspNetCore.Components.Routing

<!-- 现代化侧边栏导航 - 全新设计 -->
<nav class="h-full flex flex-col bg-white">
    <!-- 导航头部 - 简洁设计 -->
    <div class="p-6 border-b border-gray-100">
        <div class="flex items-center space-x-3">
            <div class="@GetRoleIconClass() w-10 h-10 rounded-lg flex items-center justify-center">
                <i class="@GetRoleIcon() text-white text-lg"></i>
            </div>
            <div>
                <p class="text-sm font-semibold text-gray-900">@GetRoleDisplayName()</p>
                <p class="text-xs text-gray-500">@GetRoleSubtitle()</p>
            </div>
        </div>
    </div>

    <!-- 导航菜单 - 现代化分组 -->
    <div class="flex-1 py-6 overflow-y-auto">
        @if (UserRole == "SuperAdmin")
        {
            @RenderNavSection("平台管理", new[]
            {
                ("bi-speedometer2", "仪表盘", "/dashboard", "系统概览和统计"),
                ("bi-people-fill", "用户管理", "/dashboard/users", "管理系统用户"),
                ("bi-buildings", "站点管理", "/dashboard/sites", "管理所有站点"),
                ("bi-graph-up-arrow", "数据分析", "/dashboard/analytics", "查看平台数据"),
                ("bi-gear-fill", "系统设置", "/dashboard/settings", "平台配置管理")
            })

            @RenderNavSection("订阅服务", new[]
            {
                ("bi-credit-card-fill", "订阅计划", "/dashboard/subscription-plans", "管理订阅套餐"),
                ("bi-currency-dollar", "收入报表", "/dashboard/revenue", "查看收入统计"),
                ("bi-receipt", "账单管理", "/dashboard/billing", "管理账单信息")
            })
        }

        @if (UserRole == "User" || UserRole == "SuperAdmin")
        {
            @RenderNavSection("站点管理", new[]
            {
                ("bi-house-door-fill", "我的站点", "/dashboard", "站点概览"),
                ("bi-collection-fill", "站点列表", "/dashboard/my-sites", "管理我的站点"),
                ("bi-plus-circle-fill", "创建站点", "/dashboard/sites/create", "创建新站点"),
                ("bi-palette-fill", "主题模板", "/dashboard/themes", "选择站点主题")
            })

            @RenderNavSection("内容管理", new[]
            {
                ("bi-file-text-fill", "文章管理", "/dashboard/posts", "管理文章内容"),
                ("bi-images", "媒体库", "/dashboard/media", "管理图片和文件"),
                ("bi-tags-fill", "标签分类", "/dashboard/categories", "管理内容分类"),
                ("bi-bar-chart-fill", "站点统计", "/dashboard/site-analytics", "查看站点数据")
            })
        }

        @RenderNavSection("个人中心", new[]
        {
            ("bi-person-circle", "个人资料", "/dashboard/profile", "编辑个人信息"),
            ("bi-shield-lock-fill", "安全设置", "/dashboard/security", "账户安全管理"),
            ("bi-bell-fill", "通知设置", "/dashboard/notifications", "消息通知配置")
        })
    </div>

    <!-- 导航底部 - 帮助和支持 -->
    <div class="p-4 border-t border-gray-100">
        <a href="/dashboard/help" class="block bg-gradient-to-r @GetGradientClass() rounded-lg p-4 text-white hover:shadow-lg transition-all duration-200">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                    <i class="bi-question-circle text-white text-sm"></i>
                </div>
                <div>
                    <p class="text-sm font-medium">需要帮助？</p>
                    <p class="text-xs opacity-90">查看帮助文档</p>
                </div>
            </div>
        </a>
    </div>
</nav>

@code {
    /// <summary>
    /// 用户角色
    /// </summary>
    [Parameter] public string UserRole { get; set; } = "";

    /// <summary>
    /// 获取角色图标类
    /// </summary>
    private string GetRoleIconClass()
    {
        return UserRole switch
        {
            "SuperAdmin" => "bg-gradient-to-br from-blue-600 to-blue-700",
            "SiteOwner" => "bg-gradient-to-br from-emerald-600 to-emerald-700",
            "SiteEditor" => "bg-gradient-to-br from-purple-600 to-purple-700",
            _ => "bg-gradient-to-br from-gray-600 to-gray-700"
        };
    }

    /// <summary>
    /// 获取角色图标
    /// </summary>
    private string GetRoleIcon()
    {
        return UserRole switch
        {
            "SuperAdmin" => "bi-shield-check-fill",
            "SiteOwner" => "bi-building-gear",
            "SiteEditor" => "bi-pencil-square",
            _ => "bi-person-circle"
        };
    }

    /// <summary>
    /// 获取角色显示名称
    /// </summary>
    private string GetRoleDisplayName()
    {
        return UserRole switch
        {
            "SuperAdmin" => "超级管理员",
            "SiteOwner" => "站点所有者",
            "SiteEditor" => "内容编辑者",
            _ => "用户"
        };
    }

    /// <summary>
    /// 获取角色副标题
    /// </summary>
    private string GetRoleSubtitle()
    {
        return UserRole switch
        {
            "SuperAdmin" => "平台管理控制台",
            "SiteOwner" => "站点管理控制台",
            "SiteEditor" => "内容管理控制台",
            _ => "用户控制台"
        };
    }

    /// <summary>
    /// 获取渐变色类
    /// </summary>
    private string GetGradientClass()
    {
        return UserRole switch
        {
            "SuperAdmin" => "from-blue-600 to-blue-700",
            "SiteOwner" => "from-emerald-600 to-emerald-700",
            "SiteEditor" => "from-purple-600 to-purple-700",
            _ => "from-gray-600 to-gray-700"
        };
    }

    /// <summary>
    /// 渲染导航分组 - 现代化设计
    /// </summary>
    private RenderFragment RenderNavSection(string title, (string icon, string text, string link, string tooltip)[] items) => __builder =>
    {
        <div class="mb-8">
            <!-- 分组标题 -->
            <div class="px-6 mb-3">
                <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">@title</h3>
            </div>

            <!-- 导航项列表 -->
            <div class="space-y-1 px-3">
                @foreach (var item in items)
                {
                    @RenderNavItem(item.icon, item.text, item.link, item.tooltip)
                }
            </div>
        </div>
    };

    /// <summary>
    /// 渲染导航项 - 现代化设计
    /// </summary>
    private RenderFragment RenderNavItem(string icon, string text, string link, string tooltip) => __builder =>
    {
        <div class="relative">
            <NavLink href="@link"
                     class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 hover:bg-gray-50 hover:text-gray-900"
                     ActiveClass="bg-blue-50 text-blue-700 border-r-2 border-blue-600">
                <div class="flex items-center space-x-3 w-full">
                    <div class="flex-shrink-0 w-5 h-5 flex items-center justify-center">
                        <i class="@icon text-gray-500 group-hover:text-gray-700 transition-colors duration-200"></i>
                    </div>
                    <span class="text-gray-700 group-hover:text-gray-900 transition-colors duration-200 font-medium">@text</span>
                </div>

                <!-- 活跃状态指示器 -->
                <div class="ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <i class="bi-chevron-right text-xs text-gray-400"></i>
                </div>
            </NavLink>

            <!-- 悬停工具提示 -->
            <div class="absolute left-full ml-3 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white text-xs rounded-lg px-3 py-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap z-50 pointer-events-none">
                @tooltip
                <!-- 箭头 -->
                <div class="absolute right-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-r-gray-900"></div>
            </div>
        </div>
    };
}
