using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using MolySite.Identity.Data;
using MolySite.Identity.Models;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;

namespace MolySite.Identity.Services
{
    /// <summary>
    /// JWT令牌服务
    /// </summary>
    public class TokenService : ITokenService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IConfiguration _configuration;

        public TokenService(ApplicationDbContext dbContext, IConfiguration configuration)
        {
            _dbContext = dbContext;
            _configuration = configuration;
        }

        /// <summary>
        /// 存储令牌
        /// </summary>
        public async Task StoreTokenAsync(Guid userId, string accessToken, string refreshToken)
        {
            // 删除用户之前的令牌
            var existingTokens = await _dbContext.TokenStores
                .Where(t => t.UserId == userId)
                .ToListAsync();
            _dbContext.TokenStores.RemoveRange(existingTokens);

            // 创建新的令牌存储
            var tokenStore = new TokenStore
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                AccessTokenExpiration = DateTime.UtcNow.AddMinutes(30),
                RefreshTokenExpiration = DateTime.UtcNow.AddDays(7),
                CreatedAt = DateTime.UtcNow,
                IsRevoked = false
            };

            _dbContext.TokenStores.Add(tokenStore);
            await _dbContext.SaveChangesAsync();
        }

        /// <summary>
        /// 验证刷新令牌
        /// </summary>
        public async Task<TokenStore?> ValidateRefreshTokenAsync(Guid userId, string refreshToken)
        {
            var tokenStore = await _dbContext.TokenStores
                .FirstOrDefaultAsync(t => 
                    t.UserId == userId && 
                    t.RefreshToken == refreshToken && 
                    t.RefreshTokenExpiration > DateTime.UtcNow &&
                    !t.IsRevoked);

            return tokenStore;
        }

        /// <summary>
        /// 吊销令牌
        /// </summary>
        public async Task RevokeTokenAsync(string token)
        {
            // 查找并吊销令牌存储
            var tokenStore = await _dbContext.TokenStores
                .FirstOrDefaultAsync(t => t.AccessToken == token);

            if (tokenStore != null)
            {
                tokenStore.IsRevoked = true;
                await _dbContext.SaveChangesAsync();
            }

            // 添加到黑名单
            var blacklistEntry = new TokenBlacklist
            {
                Id = Guid.NewGuid(),
                Token = token,
                RevokedAt = DateTime.UtcNow,
                Expiration = DateTime.UtcNow.AddMinutes(30)
            };

            _dbContext.TokenBlacklists.Add(blacklistEntry);
            await _dbContext.SaveChangesAsync();
        }

        /// <summary>
        /// 检查令牌是否已被吊销
        /// </summary>
        public async Task<bool> IsTokenRevokedAsync(string token)
        {
            // 检查令牌存储
            var tokenStore = await _dbContext.TokenStores
                .FirstOrDefaultAsync(t => t.AccessToken == token && t.IsRevoked);

            if (tokenStore != null)
            {
                return true;
            }

            // 检查黑名单
            var blacklistEntry = await _dbContext.TokenBlacklists
                .FirstOrDefaultAsync(b => b.Token == token && b.Expiration > DateTime.UtcNow);

            return blacklistEntry != null;
        }

        /// <summary>
        /// 清理过期的令牌和黑名单
        /// </summary>
        public async Task<int> CleanupExpiredTokensAsync()
        {
            var now = DateTime.UtcNow;
            
            // 删除过期的令牌存储
            var expiredTokenStores = await _dbContext.TokenStores
                .Where(t => t.AccessTokenExpiration < now || t.RefreshTokenExpiration < now)
                .ExecuteDeleteAsync();

            // 删除过期的黑名单条目
            var expiredBlacklist = await _dbContext.TokenBlacklists
                .Where(b => b.Expiration < now)
                .ExecuteDeleteAsync();

            return expiredTokenStores + expiredBlacklist;
        }

        /// <summary>
        /// 生成访问令牌
        /// </summary>
        public string GenerateAccessToken(ApplicationUser user)
        {
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.UserName ?? string.Empty),
                new Claim(ClaimTypes.Email, user.Email ?? string.Empty),
                new Claim("PlatformRole", user.PlatformRole.ToString())
            };

            // 添加角色声明
            if (user.PlatformRoles != null)
            {
                claims.AddRange(user.PlatformRoles.Select(role => new Claim(ClaimTypes.Role, role)));
            }

            // 添加权限声明
            if (user.Permissions != null)
            {
                claims.AddRange(user.Permissions.Select(permission => new Claim("Permission", permission)));
            }

            var key = Encoding.UTF8.GetBytes(_configuration["Jwt:Key"] ?? throw new InvalidOperationException("JWT Key is not configured"));
            var securityKey = new SymmetricSecurityKey(key);
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

            var token = new JwtSecurityToken(
                issuer: _configuration["Jwt:Issuer"],
                audience: _configuration["Jwt:Audience"],
                claims: claims,
                expires: DateTime.UtcNow.AddMinutes(Convert.ToDouble(_configuration["Jwt:ExpirationInMinutes"] ?? "60")),
                signingCredentials: credentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        /// <summary>
        /// 生成刷新令牌
        /// </summary>
        public string GenerateRefreshToken()
        {
            var randomNumber = new byte[32];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(randomNumber);
                return Convert.ToBase64String(randomNumber);
            }
        }

        /// <summary>
        /// 从令牌中获取主体
        /// </summary>
        public ClaimsPrincipal GetPrincipalFromToken(string token)
        {
            var tokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = false, // 允许过期的令牌
                ValidIssuer = _configuration["Jwt:Issuer"],
                ValidAudience = _configuration["Jwt:Audience"],
                IssuerSigningKey = new SymmetricSecurityKey(
                    Encoding.UTF8.GetBytes(_configuration["Jwt:Key"] ?? throw new InvalidOperationException("JWT Key is not configured"))
                )
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out _);
            return principal;
        }
    }
} 