{"Version": 1, "WorkspaceRootPath": "E:\\Project-SaaS-PostgreSQL\\MolySite\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{8D0F26E9-8B8D-0308-728D-630402333FFD}|MolySite.API\\MolySite.API.csproj|e:\\project-saas-postgresql\\molysite\\molysite.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8D0F26E9-8B8D-0308-728D-630402333FFD}|MolySite.API\\MolySite.API.csproj|solutionrelative:molysite.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E3B6E67F-5D2C-4C79-BB44-BF6C912588A5}|MolySite.Web\\MolySite.Web.csproj|e:\\project-saas-postgresql\\molysite\\molysite.web\\components\\sites\\components\\sitepagerenderer.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{E3B6E67F-5D2C-4C79-BB44-BF6C912588A5}|MolySite.Web\\MolySite.Web.csproj|solutionrelative:molysite.web\\components\\sites\\components\\sitepagerenderer.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{E1107B07-E4DA-70FF-B670-9F720DD5A037}|MolySite.Identity\\MolySite.Identity.csproj|e:\\project-saas-postgresql\\molysite\\molysite.identity\\data\\dataseeder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E1107B07-E4DA-70FF-B670-9F720DD5A037}|MolySite.Identity\\MolySite.Identity.csproj|solutionrelative:molysite.identity\\data\\dataseeder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "SitePageRenderer.razor", "DocumentMoniker": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\Components\\Sites\\Components\\SitePageRenderer.razor", "RelativeDocumentMoniker": "MolySite.Web\\Components\\Sites\\Components\\SitePageRenderer.razor", "ToolTip": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Web\\Components\\Sites\\Components\\SitePageRenderer.razor", "RelativeToolTip": "MolySite.Web\\Components\\Sites\\Components\\SitePageRenderer.razor", "ViewState": "AgIAAAYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-06-25T00:33:41.494Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.API\\Program.cs", "RelativeDocumentMoniker": "MolySite.API\\Program.cs", "ToolTip": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.API\\Program.cs", "RelativeToolTip": "MolySite.API\\Program.cs", "ViewState": "AgIAAJEAAAAAAAAAAAArwJ8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T15:35:14.111Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "DataSeeder.cs", "DocumentMoniker": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Identity\\Data\\DataSeeder.cs", "RelativeDocumentMoniker": "MolySite.Identity\\Data\\DataSeeder.cs", "ToolTip": "E:\\Project-SaaS-PostgreSQL\\MolySite\\MolySite.Identity\\Data\\DataSeeder.cs", "RelativeToolTip": "MolySite.Identity\\Data\\DataSeeder.cs", "ViewState": "AgIAACgAAAAAAAAAAAArwDUAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T15:24:03.045Z"}]}]}]}