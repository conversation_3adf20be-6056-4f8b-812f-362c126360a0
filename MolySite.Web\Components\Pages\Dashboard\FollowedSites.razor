@page "/dashboard/followed-sites"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using MolySite.Web.Services
@using MolySite.Shared.Dtos
@using MolySite.Web.Components.Layout
@layout DashboardLayout
@attribute [Authorize]
@inject ISiteService SiteService
@inject ILogger<FollowedSites> Logger
@inject NavigationManager Navigation

<PageTitle>关注的网站 - MolySite</PageTitle>

<div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="bi-heart-fill text-red-500 mr-3"></i>
                关注的网站
            </h1>
            <p class="text-gray-600 mt-1">管理您关注的所有网站，获取最新更新</p>
        </div>
        <div class="flex items-center space-x-3">
            <button @onclick="RefreshAsync" class="btn btn-outline-primary">
                <i class="bi-arrow-clockwise mr-2"></i>
                刷新
            </button>
        </div>
    </div>

    @if (_loading)
    {
        <div class="flex items-center justify-center py-12">
            <div class="text-center">
                <div class="loading-spinner h-12 w-12 mx-auto mb-4"></div>
                <p class="text-gray-600">正在加载关注的网站...</p>
            </div>
        </div>
    }
    else if (!_followedSites.Any())
    {
        <!-- 空状态 -->
        <div class="text-center py-16">
            <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                <i class="bi-heart text-4xl text-gray-400"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">还没有关注任何网站</h3>
            <p class="text-gray-600 mb-6 max-w-md mx-auto">
                开始关注感兴趣的网站，获取最新内容更新和通知。
            </p>
            <button @onclick="() => Navigation.NavigateTo('/dashboard/sites')" class="btn btn-primary">
                <i class="bi-search mr-2"></i>
                发现网站
            </button>
        </div>
    }
    else
    {
        <!-- 统计信息 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-sm p-6 border">
                <div class="flex items-center">
                    <div class="p-3 bg-red-100 rounded-lg">
                        <i class="bi-heart-fill text-red-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">关注总数</p>
                        <p class="text-2xl font-bold text-gray-900">@_followedSites.Count</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6 border">
                <div class="flex items-center">
                    <div class="p-3 bg-blue-100 rounded-lg">
                        <i class="bi-clock text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">最近更新</p>
                        <p class="text-2xl font-bold text-gray-900">@GetRecentlyUpdatedCount()</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6 border">
                <div class="flex items-center">
                    <div class="p-3 bg-green-100 rounded-lg">
                        <i class="bi-bell text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">新通知</p>
                        <p class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 网站列表 -->
        <div class="bg-white rounded-xl shadow-sm border">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-gray-900">关注列表</h2>
                    <div class="flex items-center space-x-3">
                        <select @bind="_sortBy" @bind:after="SortSites" class="form-select text-sm">
                            <option value="name">按名称排序</option>
                            <option value="followedAt">按关注时间排序</option>
                            <option value="lastUpdated">按更新时间排序</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="divide-y divide-gray-200">
                @foreach (var site in _sortedSites)
                {
                    <div class="p-6 hover:bg-gray-50 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="flex-shrink-0">
                                    @if (!string.IsNullOrEmpty(site.LogoUrl))
                                    {
                                        <img src="@site.LogoUrl" alt="@site.SiteName" class="w-12 h-12 rounded-lg object-cover">
                                    }
                                    else
                                    {
                                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                                            <span class="text-white font-bold text-lg">@GetSiteInitial(site.SiteName)</span>
                                        </div>
                                    }
                                </div>
                                <div class="flex-1 min-w-0">
                                    <h3 class="text-lg font-semibold text-gray-900 truncate">@site.SiteName</h3>
                                    <p class="text-sm text-gray-600 truncate">@site.Domain</p>
                                    @if (!string.IsNullOrEmpty(site.Description))
                                    {
                                        <p class="text-sm text-gray-500 mt-1 line-clamp-2">@site.Description</p>
                                    }
                                    <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                        <span>关注于 @site.FollowedAt.ToString("yyyy-MM-dd")</span>
                                        @if (site.LastUpdated.HasValue)
                                        {
                                            <span>更新于 @site.LastUpdated.Value.ToString("yyyy-MM-dd")</span>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3">
                                <button @onclick="() => VisitSite(site.Domain)" class="btn btn-outline-primary btn-sm">
                                    <i class="bi-box-arrow-up-right mr-1"></i>
                                    访问
                                </button>
                                <button @onclick="() => UnfollowSite(site.SiteId)" class="btn btn-outline-danger btn-sm">
                                    <i class="bi-heart-fill mr-1"></i>
                                    取消关注
                                </button>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    }
</div>

@code {
    private bool _loading = true;
    private List<FollowedSiteDto> _followedSites = new();
    private List<FollowedSiteDto> _sortedSites = new();
    private string _sortBy = "name";

    protected override async Task OnInitializedAsync()
    {
        await LoadFollowedSitesAsync();
    }

    private async Task LoadFollowedSitesAsync()
    {
        try
        {
            _loading = true;
            
            // TODO: 调用API获取关注的网站列表
            // var result = await SiteService.GetFollowedSitesAsync();
            // if (result.IsSuccess)
            // {
            //     _followedSites = result.Data ?? new List<FollowedSiteDto>();
            // }
            
            // 临时模拟数据
            _followedSites = new List<FollowedSiteDto>();
            
            SortSites();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载关注的网站时发生错误");
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task RefreshAsync()
    {
        await LoadFollowedSitesAsync();
    }

    private void SortSites()
    {
        _sortedSites = _sortBy switch
        {
            "name" => _followedSites.OrderBy(s => s.SiteName).ToList(),
            "followedAt" => _followedSites.OrderByDescending(s => s.FollowedAt).ToList(),
            "lastUpdated" => _followedSites.OrderByDescending(s => s.LastUpdated ?? DateTime.MinValue).ToList(),
            _ => _followedSites.ToList()
        };
        StateHasChanged();
    }

    private async Task UnfollowSite(Guid siteId)
    {
        try
        {
            // TODO: 调用API取消关注
            // var result = await SiteService.UnfollowSiteAsync(siteId);
            // if (result.IsSuccess)
            // {
            //     _followedSites.RemoveAll(s => s.SiteId == siteId);
            //     SortSites();
            // }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "取消关注网站时发生错误: {SiteId}", siteId);
        }
    }

    private void VisitSite(string domain)
    {
        var url = domain.StartsWith("http") ? domain : $"https://{domain}";
        Navigation.NavigateTo(url, true);
    }

    private string GetSiteInitial(string siteName)
    {
        return string.IsNullOrEmpty(siteName) ? "?" : siteName[0].ToString().ToUpper();
    }

    private int GetRecentlyUpdatedCount()
    {
        var oneWeekAgo = DateTime.UtcNow.AddDays(-7);
        return _followedSites.Count(s => s.LastUpdated >= oneWeekAgo);
    }
}
