using System;

namespace MolySite.Shared.Dtos
{
    /// <summary>
    /// 网站角色类型
    /// </summary>
    public enum SiteRoleType
    {
        /// <summary>
        /// 网站所有者
        /// </summary>
        Owner = 1
    }
    /// <summary>
    /// 网站数据传输对象
    /// </summary>
    public class SiteDto
    {
        /// <summary>
        /// 网站ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 网站名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 网站域名
        /// </summary>
        public string Domain { get; set; } = string.Empty;

        /// <summary>
        /// 网站描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 使用的模板
        /// </summary>
        public string Template { get; set; } = "Default";

        /// <summary>
        /// 自定义CSS样式
        /// </summary>
        public string? CustomCss { get; set; }

        /// <summary>
        /// 自定义JavaScript代码
        /// </summary>
        public string? CustomJavaScript { get; set; }

        /// <summary>
        /// 网站配置（JSON格式）
        /// </summary>
        public string? SiteConfig { get; set; }

        /// <summary>
        /// 网站图标URL
        /// </summary>
        public string? FaviconUrl { get; set; }

        /// <summary>
        /// 网站Logo URL
        /// </summary>
        public string? LogoUrl { get; set; }

        /// <summary>
        /// 主色调
        /// </summary>
        public string PrimaryColor { get; set; } = "#3b82f6";

        /// <summary>
        /// 次要色调
        /// </summary>
        public string SecondaryColor { get; set; } = "#10b981";

        /// <summary>
        /// 是否已发布
        /// </summary>
        public bool IsPublished { get; set; }

        /// <summary>
        /// 网站状态（活跃/禁用）
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LastModifiedAt { get; set; }

        /// <summary>
        /// 订阅计划
        /// </summary>
        public string SubscriptionPlan { get; set; } = "Free";

        /// <summary>
        /// 订阅到期时间
        /// </summary>
        public DateTime? SubscriptionExpiresAt { get; set; }

        /// <summary>
        /// 最大存储空间（MB）
        /// </summary>
        public int MaxStorageMB { get; set; }

        /// <summary>
        /// 已使用存储空间（MB）
        /// </summary>
        public int UsedStorageMB { get; set; }

        /// <summary>
        /// 页脚文本
        /// </summary>
        public string? FooterText { get; set; }

        /// <summary>
        /// 社交媒体链接（JSON格式）
        /// </summary>
        public string? SocialMediaLinks { get; set; }

        /// <summary>
        /// 导航菜单（JSON格式）
        /// </summary>
        public string? NavigationMenu { get; set; }

        /// <summary>
        /// 分析代码（如Google Analytics）
        /// </summary>
        public string? AnalyticsCode { get; set; }

        /// <summary>
        /// 是否启用评论功能
        /// </summary>
        public bool EnableComments { get; set; }

        /// <summary>
        /// 是否启用搜索引擎索引
        /// </summary>
        public bool EnableSEO { get; set; }

        /// <summary>
        /// 网站关键词
        /// </summary>
        public string? SiteKeywords { get; set; }

        /// <summary>
        /// 当前用户在该网站的角色
        /// </summary>
        public SiteRoleType? UserRole { get; set; }



        /// <summary>
        /// 存储使用百分比
        /// </summary>
        public double StorageUsagePercentage => MaxStorageMB > 0 ? (double)UsedStorageMB / MaxStorageMB * 100 : 0;

        /// <summary>
        /// 是否存储空间不足
        /// </summary>
        public bool IsStorageOverLimit => UsedStorageMB > MaxStorageMB;

        /// <summary>
        /// 订阅是否已过期
        /// </summary>
        public bool IsSubscriptionExpired => SubscriptionExpiresAt.HasValue && SubscriptionExpiresAt.Value < DateTime.UtcNow;
    }

    /// <summary>
    /// 创建网站DTO
    /// </summary>
    public class CreateSiteDto
    {
        /// <summary>
        /// 网站名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 网站域名
        /// </summary>
        public string Domain { get; set; } = string.Empty;

        /// <summary>
        /// 网站描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 使用的模板
        /// </summary>
        public string? Template { get; set; }

        /// <summary>
        /// 主色调
        /// </summary>
        public string? PrimaryColor { get; set; }

        /// <summary>
        /// 次要色调
        /// </summary>
        public string? SecondaryColor { get; set; }
    }

    /// <summary>
    /// 更新网站DTO
    /// </summary>
    public class UpdateSiteDto
    {
        /// <summary>
        /// 网站名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 网站域名
        /// </summary>
        public string Domain { get; set; } = string.Empty;

        /// <summary>
        /// 网站描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 使用的模板
        /// </summary>
        public string Template { get; set; } = "Default";

        /// <summary>
        /// 自定义CSS样式
        /// </summary>
        public string? CustomCss { get; set; }

        /// <summary>
        /// 自定义JavaScript代码
        /// </summary>
        public string? CustomJavaScript { get; set; }

        /// <summary>
        /// 网站配置（JSON格式）
        /// </summary>
        public string? SiteConfig { get; set; }

        /// <summary>
        /// 网站图标URL
        /// </summary>
        public string? FaviconUrl { get; set; }

        /// <summary>
        /// 网站Logo URL
        /// </summary>
        public string? LogoUrl { get; set; }

        /// <summary>
        /// 主色调
        /// </summary>
        public string PrimaryColor { get; set; } = "#3b82f6";

        /// <summary>
        /// 次要色调
        /// </summary>
        public string SecondaryColor { get; set; } = "#10b981";

        /// <summary>
        /// 页脚文本
        /// </summary>
        public string? FooterText { get; set; }

        /// <summary>
        /// 社交媒体链接（JSON格式）
        /// </summary>
        public string? SocialMediaLinks { get; set; }

        /// <summary>
        /// 导航菜单（JSON格式）
        /// </summary>
        public string? NavigationMenu { get; set; }

        /// <summary>
        /// 分析代码（如Google Analytics）
        /// </summary>
        public string? AnalyticsCode { get; set; }

        /// <summary>
        /// 是否启用评论功能
        /// </summary>
        public bool EnableComments { get; set; } = true;

        /// <summary>
        /// 是否启用搜索引擎索引
        /// </summary>
        public bool EnableSEO { get; set; } = true;

        /// <summary>
        /// 网站关键词
        /// </summary>
        public string? SiteKeywords { get; set; }
    }


}
