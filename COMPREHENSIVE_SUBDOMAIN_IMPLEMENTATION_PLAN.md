# 🌐 MolySite二级域名设置及分配完整实施方案

## 📋 目录
1. [总体架构设计](#总体架构设计)
2. [域名分配策略](#域名分配策略)
3. [技术实现方案](#技术实现方案)
4. [数据库设计](#数据库设计)
5. [API接口设计](#api接口设计)
6. [前端界面设计](#前端界面设计)
7. [域名管理系统](#域名管理系统)
8. [安全与验证](#安全与验证)
9. [性能优化](#性能优化)
10. [部署与运维](#部署与运维)
11. [监控与分析](#监控与分析)
12. [扩展规划](#扩展规划)

## 🏗️ 总体架构设计

### 核心设计原则

1. **多租户隔离** - 每个用户网站完全独立
2. **高可用性** - 99.9%以上的服务可用性
3. **可扩展性** - 支持百万级网站规模
4. **SEO友好** - 优化搜索引擎收录
5. **用户友好** - 简单直观的操作体验
6. **安全可靠** - 全方位的安全保护

### 域名架构层次

```
molysite.com (主域名)
├── www.molysite.com (平台官网)
├── app.molysite.com (管理平台)
├── api.molysite.com (API服务)
├── cdn.molysite.com (静态资源)
└── *.molysite.com (用户网站)
    ├── blog.molysite.com (用户网站1)
    ├── portfolio.molysite.com (用户网站2)
    ├── shop.molysite.com (用户网站3)
    └── ... (更多用户网站)
```

### 技术栈选择

**后端技术**:
- .NET 8.0 (主要框架)
- PostgreSQL (主数据库)
- Redis (缓存和会话)
- Nginx (反向代理和负载均衡)

**前端技术**:
- Blazor Server (管理界面)
- Blazor WebAssembly (用户网站)
- Tailwind CSS (样式框架)
- JavaScript (交互增强)

**基础设施**:
- Docker (容器化)
- Kubernetes (容器编排)
- CloudFlare (CDN和DNS)
- Let's Encrypt (SSL证书)

## 🎯 域名分配策略

### 域名命名规范

#### 基础规则
```
格式: {subdomain}.molysite.com
长度: 3-63个字符
字符: a-z, 0-9, - (连字符)
限制: 不能以连字符开头或结尾
```

#### 命名策略层次

**1. 直接命名策略**
```
网站名称 → 域名
"我的博客" → "myblog.molysite.com"
"设计作品集" → "portfolio.molysite.com"
"在线商店" → "shop.molysite.com"
```

**2. 智能建议策略**
```
基于网站类型的前缀:
- blog-{name} (博客类)
- shop-{name} (商店类)
- portfolio-{name} (作品集类)
- biz-{name} (企业类)
- me-{name} (个人类)
```

**3. 冲突解决策略**
```
原始: myblog
冲突时依次尝试:
1. myblog2024
2. myblog-site
3. my-blog
4. myblog1, myblog2, myblog3...
5. myblog-{random4digits}
```

### 域名分类管理

#### 保留域名列表
```javascript
const RESERVED_DOMAINS = [
    // 系统保留
    'www', 'api', 'app', 'admin', 'mail', 'ftp', 'cdn', 'static',
    'assets', 'images', 'files', 'download', 'upload', 'backup',

    // 功能保留
    'blog', 'shop', 'store', 'news', 'help', 'support', 'docs',
    'forum', 'community', 'chat', 'live', 'stream', 'video',

    // 安全保留
    'secure', 'ssl', 'vpn', 'proxy', 'gateway', 'firewall',

    // 开发保留
    'dev', 'test', 'demo', 'staging', 'prod', 'beta', 'alpha',

    // 品牌保留
    'molysite', 'moly', 'site', 'builder', 'platform'
];
```

#### 禁用词汇过滤
```javascript
const BANNED_WORDS = [
    // 不当内容
    'porn', 'sex', 'adult', 'xxx', 'nude', 'naked',

    // 违法内容
    'drugs', 'casino', 'gambling', 'bet', 'poker', 'lottery',

    // 恶意内容
    'phishing', 'scam', 'fraud', 'fake', 'spam', 'virus',

    // 仇恨内容
    'hate', 'nazi', 'terrorist', 'violence', 'kill', 'death',

    // 侵权内容
    'piracy', 'crack', 'hack', 'warez', 'torrent', 'illegal'
];
```

### 域名生命周期管理

#### 状态定义
```javascript
const DOMAIN_STATUS = {
    AVAILABLE: 'available',      // 可用
    RESERVED: 'reserved',        // 预留中
    ALLOCATED: 'allocated',      // 已分配
    SUSPENDED: 'suspended',      // 暂停
    EXPIRED: 'expired',          // 过期
    RECYCLED: 'recycled',        // 回收池
    BANNED: 'banned'             // 禁用
};
```

#### 生命周期流程
```
1. 可用 (Available)
   ↓ 用户选择
2. 预留 (Reserved) - 24小时
   ↓ 确认创建 / ↓ 超时释放
3. 已分配 (Allocated)
   ↓ 网站删除 / 违规
4. 回收池 (Recycled) - 30天
   ↓ 重新可用
5. 可用 (Available)
```

## 🔧 技术实现方案

### 智能路由系统

#### 中间件架构
```csharp
public class SubdomainRoutingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ISubdomainResolver _resolver;
    private readonly ILogger<SubdomainRoutingMiddleware> _logger;

    public async Task InvokeAsync(HttpContext context)
    {
        var host = context.Request.Host.Host;
        var subdomain = ExtractSubdomain(host);

        if (IsUserSite(subdomain))
        {
            await HandleUserSiteRequest(context, subdomain);
        }
        else
        {
            await HandlePlatformRequest(context);
        }
    }
}
```

#### 域名解析器
```csharp
public interface ISubdomainResolver
{
    Task<SiteInfo> ResolveSiteAsync(string subdomain);
    bool IsValidSubdomain(string subdomain);
    string ExtractSubdomain(string host);
}

public class SubdomainResolver : ISubdomainResolver
{
    private readonly IMemoryCache _cache;
    private readonly ISiteRepository _siteRepository;

    public async Task<SiteInfo> ResolveSiteAsync(string subdomain)
    {
        // 1. 检查缓存
        if (_cache.TryGetValue($"site:{subdomain}", out SiteInfo cachedSite))
            return cachedSite;

        // 2. 查询数据库
        var site = await _siteRepository.GetByDomainAsync(subdomain);

        // 3. 缓存结果
        if (site != null)
        {
            _cache.Set($"site:{subdomain}", site, TimeSpan.FromMinutes(15));
        }

        return site;
    }
}
```

### 域名分配引擎

#### 智能建议算法
```csharp
public class DomainSuggestionEngine
{
    public async Task<List<DomainSuggestion>> GenerateSuggestionsAsync(
        string siteName,
        SiteType siteType,
        string userId)
    {
        var suggestions = new List<DomainSuggestion>();
        var baseName = SanitizeName(siteName);

        // 1. 直接使用网站名
        await AddSuggestionIfAvailable(suggestions, baseName, SuggestionType.Direct);

        // 2. 添加类型前缀
        var typePrefix = GetTypePrefix(siteType);
        if (!string.IsNullOrEmpty(typePrefix))
        {
            await AddSuggestionIfAvailable(suggestions, $"{typePrefix}-{baseName}", SuggestionType.TypePrefix);
        }

        // 3. 添加用户标识
        var userPrefix = await GetUserPrefix(userId);
        await AddSuggestionIfAvailable(suggestions, $"{userPrefix}-{baseName}", SuggestionType.UserPrefix);

        // 4. 添加常用后缀
        var suffixes = new[] { "site", "web", "online", "hub", "zone" };
        foreach (var suffix in suffixes)
        {
            await AddSuggestionIfAvailable(suggestions, $"{baseName}-{suffix}", SuggestionType.CommonSuffix);
        }

        // 5. 添加数字后缀
        for (int i = 1; i <= 10; i++)
        {
            await AddSuggestionIfAvailable(suggestions, $"{baseName}{i}", SuggestionType.NumberSuffix);
        }

        // 6. 添加年份
        var year = DateTime.Now.Year;
        await AddSuggestionIfAvailable(suggestions, $"{baseName}{year}", SuggestionType.YearSuffix);

        // 7. 智能组合
        await GenerateSmartCombinations(suggestions, baseName, siteType);

        return suggestions.OrderByDescending(s => s.Score).Take(10).ToList();
    }
}
```

#### SEO评分算法
```csharp
public class SEOScoreCalculator
{
    public int CalculateScore(string domain)
    {
        int score = 100;

        // 长度评分 (最佳长度: 6-15字符)
        if (domain.Length < 6) score -= 10;
        if (domain.Length > 15) score -= 5;
        if (domain.Length > 25) score -= 15;
        if (domain.Length > 35) score -= 25;

        // 字符组成评分
        var letterCount = domain.Count(char.IsLetter);
        var digitCount = domain.Count(char.IsDigit);
        var hyphenCount = domain.Count(c => c == '-');

        // 字母比例 (理想: 80%以上)
        var letterRatio = (double)letterCount / domain.Length;
        if (letterRatio < 0.5) score -= 20;
        else if (letterRatio < 0.7) score -= 10;
        else if (letterRatio > 0.9) score += 5;

        // 数字评分 (少量数字可接受)
        if (digitCount > 0 && digitCount <= 2) score -= 3;
        else if (digitCount > 2) score -= digitCount * 3;

        // 连字符评分 (1-2个可接受)
        if (hyphenCount == 1) score -= 2;
        else if (hyphenCount == 2) score -= 5;
        else if (hyphenCount > 2) score -= hyphenCount * 5;

        // 可读性评分
        if (IsReadable(domain)) score += 10;
        if (IsPronounceable(domain)) score += 5;

        // 关键词评分
        if (ContainsRelevantKeywords(domain)) score += 8;
        if (ContainsBrandKeywords(domain)) score += 5;

        // 记忆性评分
        if (IsMemorable(domain)) score += 7;

        // 避免混淆评分
        if (AvoidConfusion(domain)) score += 3;

        return Math.Max(0, Math.Min(100, score));
    }
}
```

## 🗄️ 数据库设计

### 核心表结构

#### 1. 域名分配表 (domain_allocations)
```sql
CREATE TABLE domain_allocations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subdomain VARCHAR(63) NOT NULL UNIQUE,
    site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL DEFAULT 'allocated',
    allocated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    last_accessed_at TIMESTAMP WITH TIME ZONE,
    access_count BIGINT DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- 索引
    INDEX idx_subdomain (subdomain),
    INDEX idx_site_id (site_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_allocated_at (allocated_at)
);
```

#### 2. 域名预留表 (domain_reservations)
```sql
CREATE TABLE domain_reservations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subdomain VARCHAR(63) NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_id VARCHAR(255),
    reserved_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '24 hours'),
    reservation_type VARCHAR(20) DEFAULT 'user', -- user, system, admin
    metadata JSONB,

    -- 索引
    INDEX idx_subdomain (subdomain),
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_session_id (session_id)
);
```

#### 3. 禁用域名表 (banned_domains)
```sql
CREATE TABLE banned_domains (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    pattern VARCHAR(100) NOT NULL,
    pattern_type VARCHAR(20) DEFAULT 'exact', -- exact, prefix, suffix, contains, regex
    reason VARCHAR(255),
    banned_by UUID REFERENCES users(id),
    banned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,

    -- 索引
    INDEX idx_pattern (pattern),
    INDEX idx_pattern_type (pattern_type),
    INDEX idx_is_active (is_active)
);
```

#### 4. 域名历史表 (domain_history)
```sql
CREATE TABLE domain_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subdomain VARCHAR(63) NOT NULL,
    site_id UUID,
    user_id UUID,
    action VARCHAR(50) NOT NULL, -- allocated, released, suspended, expired, transferred
    old_status VARCHAR(20),
    new_status VARCHAR(20),
    reason VARCHAR(255),
    performed_by UUID REFERENCES users(id),
    performed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB,

    -- 索引
    INDEX idx_subdomain (subdomain),
    INDEX idx_site_id (site_id),
    INDEX idx_action (action),
    INDEX idx_performed_at (performed_at)
);
```

#### 5. 域名统计表 (domain_analytics)
```sql
CREATE TABLE domain_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subdomain VARCHAR(63) NOT NULL,
    date DATE NOT NULL,
    page_views BIGINT DEFAULT 0,
    unique_visitors BIGINT DEFAULT 0,
    bounce_rate DECIMAL(5,2),
    avg_session_duration INTEGER, -- seconds
    top_referrers JSONB,
    top_pages JSONB,
    device_stats JSONB,
    geo_stats JSONB,

    -- 复合主键
    UNIQUE(subdomain, date),

    -- 索引
    INDEX idx_subdomain_date (subdomain, date),
    INDEX idx_date (date)
);
```

### 数据库优化策略

#### 分区策略
```sql
-- 按时间分区域名历史表
CREATE TABLE domain_history_y2024m01 PARTITION OF domain_history
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- 按哈希分区域名分配表
CREATE TABLE domain_allocations_0 PARTITION OF domain_allocations
FOR VALUES WITH (MODULUS 4, REMAINDER 0);
```

#### 缓存策略
```csharp
public class DomainCacheService
{
    private readonly IMemoryCache _memoryCache;
    private readonly IDistributedCache _distributedCache;

    public async Task<SiteInfo> GetSiteByDomainAsync(string subdomain)
    {
        // L1 缓存 (内存)
        var cacheKey = $"site:domain:{subdomain}";
        if (_memoryCache.TryGetValue(cacheKey, out SiteInfo site))
            return site;

        // L2 缓存 (Redis)
        var cachedJson = await _distributedCache.GetStringAsync(cacheKey);
        if (!string.IsNullOrEmpty(cachedJson))
        {
            site = JsonSerializer.Deserialize<SiteInfo>(cachedJson);
            _memoryCache.Set(cacheKey, site, TimeSpan.FromMinutes(5));
            return site;
        }

        // 数据库查询
        site = await _repository.GetSiteByDomainAsync(subdomain);
        if (site != null)
        {
            // 缓存到 L2 (30分钟)
            await _distributedCache.SetStringAsync(
                cacheKey,
                JsonSerializer.Serialize(site),
                new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30)
                });

            // 缓存到 L1 (5分钟)
            _memoryCache.Set(cacheKey, site, TimeSpan.FromMinutes(5));
        }

        return site;
    }
}
```

## 🔌 API接口设计

### RESTful API 规范

#### 域名管理 API
```csharp
[ApiController]
[Route("api/v1/domains")]
public class DomainsController : ControllerBase
{
    // 检查域名可用性
    [HttpPost("check-availability")]
    public async Task<ActionResult<DomainAvailabilityResponse>> CheckAvailability(
        [FromBody] DomainAvailabilityRequest request)
    {
        var result = await _domainService.CheckAvailabilityAsync(request.Subdomain);
        return Ok(new DomainAvailabilityResponse
        {
            Subdomain = request.Subdomain,
            IsAvailable = result.IsAvailable,
            Reason = result.Reason,
            Suggestions = result.Suggestions
        });
    }

    // 获取域名建议
    [HttpPost("suggestions")]
    public async Task<ActionResult<DomainSuggestionsResponse>> GetSuggestions(
        [FromBody] DomainSuggestionsRequest request)
    {
        var suggestions = await _domainService.GenerateSuggestionsAsync(
            request.SiteName,
            request.SiteType,
            request.UserId);

        return Ok(new DomainSuggestionsResponse
        {
            Suggestions = suggestions,
            TotalCount = suggestions.Count
        });
    }

    // 预留域名
    [HttpPost("reserve")]
    [Authorize]
    public async Task<ActionResult<DomainReservationResponse>> ReserveDomain(
        [FromBody] DomainReservationRequest request)
    {
        var userId = GetCurrentUserId();
        var result = await _domainService.ReserveDomainAsync(request.Subdomain, userId);

        return Ok(new DomainReservationResponse
        {
            Subdomain = request.Subdomain,
            IsReserved = result.IsSuccess,
            ReservationId = result.ReservationId,
            ExpiresAt = result.ExpiresAt,
            Message = result.Message
        });
    }

    // 分配域名
    [HttpPost("allocate")]
    [Authorize]
    public async Task<ActionResult<DomainAllocationResponse>> AllocateDomain(
        [FromBody] DomainAllocationRequest request)
    {
        var userId = GetCurrentUserId();
        var result = await _domainService.AllocateDomainAsync(
            request.Subdomain,
            request.SiteId,
            userId);

        return Ok(new DomainAllocationResponse
        {
            Subdomain = request.Subdomain,
            SiteId = request.SiteId,
            IsAllocated = result.IsSuccess,
            AllocationId = result.AllocationId,
            Message = result.Message
        });
    }

    // 释放域名
    [HttpDelete("{subdomain}")]
    [Authorize]
    public async Task<ActionResult> ReleaseDomain(string subdomain)
    {
        var userId = GetCurrentUserId();
        var result = await _domainService.ReleaseDomainAsync(subdomain, userId);

        if (result.IsSuccess)
            return NoContent();
        else
            return BadRequest(result.Message);
    }

    // 获取用户域名列表
    [HttpGet("my-domains")]
    [Authorize]
    public async Task<ActionResult<UserDomainsResponse>> GetMyDomains(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        var userId = GetCurrentUserId();
        var result = await _domainService.GetUserDomainsAsync(userId, page, pageSize);

        return Ok(new UserDomainsResponse
        {
            Domains = result.Domains,
            TotalCount = result.TotalCount,
            Page = page,
            PageSize = pageSize
        });
    }
}
```

#### 域名分析 API
```csharp
[ApiController]
[Route("api/v1/domain-analytics")]
public class DomainAnalyticsController : ControllerBase
{
    // 获取域名统计
    [HttpGet("{subdomain}/stats")]
    [Authorize]
    public async Task<ActionResult<DomainStatsResponse>> GetDomainStats(
        string subdomain,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        var userId = GetCurrentUserId();

        // 验证用户权限
        if (!await _domainService.HasDomainAccessAsync(subdomain, userId))
            return Forbid();

        var stats = await _analyticsService.GetDomainStatsAsync(
            subdomain,
            startDate ?? DateTime.UtcNow.AddDays(-30),
            endDate ?? DateTime.UtcNow);

        return Ok(stats);
    }

    // 获取SEO分析
    [HttpGet("{subdomain}/seo")]
    [Authorize]
    public async Task<ActionResult<SEOAnalysisResponse>> GetSEOAnalysis(string subdomain)
    {
        var userId = GetCurrentUserId();

        if (!await _domainService.HasDomainAccessAsync(subdomain, userId))
            return Forbid();

        var analysis = await _seoService.AnalyzeDomainAsync(subdomain);
        return Ok(analysis);
    }
}
```

### GraphQL API (高级查询)

```csharp
public class DomainQuery
{
    [UseProjection]
    [UseFiltering]
    [UseSorting]
    public IQueryable<DomainAllocation> GetDomains([Service] ApplicationDbContext context)
        => context.DomainAllocations;

    public async Task<DomainAvailability> CheckDomainAvailability(
        string subdomain,
        [Service] IDomainService domainService)
        => await domainService.CheckAvailabilityAsync(subdomain);

    public async Task<IEnumerable<DomainSuggestion>> GetDomainSuggestions(
        string siteName,
        SiteType siteType,
        [Service] IDomainService domainService)
        => await domainService.GenerateSuggestionsAsync(siteName, siteType);
}

public class DomainMutation
{
    public async Task<DomainReservationResult> ReserveDomain(
        string subdomain,
        [Service] IDomainService domainService,
        ClaimsPrincipal claimsPrincipal)
    {
        var userId = GetUserId(claimsPrincipal);
        return await domainService.ReserveDomainAsync(subdomain, userId);
    }

    public async Task<DomainAllocationResult> AllocateDomain(
        string subdomain,
        Guid siteId,
        [Service] IDomainService domainService,
        ClaimsPrincipal claimsPrincipal)
    {
        var userId = GetUserId(claimsPrincipal);
        return await domainService.AllocateDomainAsync(subdomain, siteId, userId);
    }
}
```

## 🎨 前端界面设计

### 域名选择组件

#### 主要组件架构
```typescript
// DomainSelector.tsx
interface DomainSelectorProps {
    siteName: string;
    siteType: SiteType;
    onDomainSelected: (domain: string) => void;
    onValidationChange: (isValid: boolean) => void;
}

export const DomainSelector: React.FC<DomainSelectorProps> = ({
    siteName,
    siteType,
    onDomainSelected,
    onValidationChange
}) => {
    const [inputDomain, setInputDomain] = useState('');
    const [suggestions, setSuggestions] = useState<DomainSuggestion[]>([]);
    const [availability, setAvailability] = useState<AvailabilityStatus>('unknown');
    const [isChecking, setIsChecking] = useState(false);

    // 实时域名建议
    useEffect(() => {
        if (siteName) {
            generateSuggestions();
        }
    }, [siteName, siteType]);

    // 实时可用性检查
    useEffect(() => {
        if (inputDomain && inputDomain.length >= 3) {
            checkAvailability();
        }
    }, [inputDomain]);

    return (
        <div className="domain-selector">
            <DomainInput
                value={inputDomain}
                onChange={setInputDomain}
                availability={availability}
                isChecking={isChecking}
            />
            <DomainSuggestions
                suggestions={suggestions}
                onSelect={onDomainSelected}
            />
            <DomainRules />
            <DomainPreview domain={inputDomain} />
        </div>
    );
};
```

#### 域名输入组件
```typescript
// DomainInput.tsx
interface DomainInputProps {
    value: string;
    onChange: (value: string) => void;
    availability: AvailabilityStatus;
    isChecking: boolean;
}

export const DomainInput: React.FC<DomainInputProps> = ({
    value,
    onChange,
    availability,
    isChecking
}) => {
    const getStatusIcon = () => {
        if (isChecking) return <SpinnerIcon />;
        if (availability === 'available') return <CheckIcon className="text-green-500" />;
        if (availability === 'unavailable') return <XIcon className="text-red-500" />;
        return null;
    };

    const getStatusMessage = () => {
        if (isChecking) return '检查可用性...';
        if (availability === 'available') return `${value}.molysite.com 可用`;
        if (availability === 'unavailable') return `${value}.molysite.com 不可用`;
        return '';
    };

    return (
        <div className="domain-input-container">
            <label className="block text-sm font-medium text-gray-700 mb-2">
                选择您的域名
            </label>
            <div className="flex rounded-md shadow-sm">
                <input
                    type="text"
                    value={value}
                    onChange={(e) => onChange(sanitizeDomainInput(e.target.value))}
                    placeholder="myblog"
                    className="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-l-md border border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                    pattern="[a-z0-9-]+"
                    maxLength={63}
                />
                <span className="inline-flex items-center px-3 py-2 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                    .molysite.com
                </span>
            </div>
            <div className="mt-1 flex items-center">
                {getStatusIcon()}
                <span className={`ml-2 text-sm ${
                    availability === 'available' ? 'text-green-600' :
                    availability === 'unavailable' ? 'text-red-600' :
                    'text-gray-500'
                }`}>
                    {getStatusMessage()}
                </span>
            </div>
        </div>
    );
};
```

#### 域名建议组件
```typescript
// DomainSuggestions.tsx
interface DomainSuggestionsProps {
    suggestions: DomainSuggestion[];
    onSelect: (domain: string) => void;
}

export const DomainSuggestions: React.FC<DomainSuggestionsProps> = ({
    suggestions,
    onSelect
}) => {
    if (suggestions.length === 0) return null;

    return (
        <div className="domain-suggestions mt-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3">推荐域名</h4>
            <div className="grid grid-cols-1 gap-2 max-h-64 overflow-y-auto">
                {suggestions.map((suggestion, index) => (
                    <DomainSuggestionItem
                        key={suggestion.domain}
                        suggestion={suggestion}
                        onSelect={() => onSelect(suggestion.domain)}
                        isRecommended={index === 0}
                    />
                ))}
            </div>
        </div>
    );
};

// DomainSuggestionItem.tsx
interface DomainSuggestionItemProps {
    suggestion: DomainSuggestion;
    onSelect: () => void;
    isRecommended: boolean;
}

export const DomainSuggestionItem: React.FC<DomainSuggestionItemProps> = ({
    suggestion,
    onSelect,
    isRecommended
}) => {
    return (
        <button
            onClick={onSelect}
            className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 text-left"
        >
            <div className="flex-1">
                <div className="flex items-center">
                    <span className="font-medium text-gray-900">
                        {suggestion.domain}.molysite.com
                    </span>
                    {isRecommended && (
                        <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            推荐
                        </span>
                    )}
                </div>
                <div className="flex items-center mt-1 text-sm text-gray-500">
                    <span>SEO评分: {suggestion.seoScore}/100</span>
                    <span className="mx-2">•</span>
                    <span>{getSuggestionTypeLabel(suggestion.type)}</span>
                </div>
            </div>
            <ChevronRightIcon className="h-5 w-5 text-gray-400" />
        </button>
    );
};
```

### 域名管理界面

#### 域名列表组件
```typescript
// DomainList.tsx
export const DomainList: React.FC = () => {
    const [domains, setDomains] = useState<UserDomain[]>([]);
    const [loading, setLoading] = useState(true);
    const [filter, setFilter] = useState<DomainFilter>('all');

    useEffect(() => {
        loadDomains();
    }, [filter]);

    return (
        <div className="domain-list">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-gray-900">我的域名</h2>
                <DomainFilter value={filter} onChange={setFilter} />
            </div>

            {loading ? (
                <DomainListSkeleton />
            ) : (
                <div className="grid grid-cols-1 gap-4">
                    {domains.map(domain => (
                        <DomainCard key={domain.id} domain={domain} />
                    ))}
                </div>
            )}
        </div>
    );
};

// DomainCard.tsx
interface DomainCardProps {
    domain: UserDomain;
}

export const DomainCard: React.FC<DomainCardProps> = ({ domain }) => {
    return (
        <div className="bg-white border rounded-lg p-6 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
                <div className="flex-1">
                    <div className="flex items-center">
                        <h3 className="text-lg font-medium text-gray-900">
                            {domain.subdomain}.molysite.com
                        </h3>
                        <DomainStatusBadge status={domain.status} />
                    </div>
                    <p className="text-sm text-gray-500 mt-1">
                        {domain.siteName}
                    </p>
                    <div className="flex items-center mt-2 text-sm text-gray-400">
                        <span>创建于 {formatDate(domain.createdAt)}</span>
                        <span className="mx-2">•</span>
                        <span>{domain.pageViews} 次访问</span>
                    </div>
                </div>
                <div className="flex items-center space-x-2">
                    <button
                        onClick={() => window.open(`https://${domain.subdomain}.molysite.com`, '_blank')}
                        className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                        <ExternalLinkIcon className="h-4 w-4 mr-1" />
                        访问
                    </button>
                    <DomainActions domain={domain} />
                </div>
            </div>
        </div>
    );
};
```

### 响应式设计

#### 移动端适配
```css
/* 移动端域名选择器 */
@media (max-width: 768px) {
    .domain-selector {
        padding: 1rem;
    }

    .domain-input-container input {
        font-size: 16px; /* 防止iOS缩放 */
    }

    .domain-suggestions {
        max-height: 200px;
    }

    .domain-card {
        padding: 1rem;
    }

    .domain-card .actions {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* 平板端适配 */
@media (min-width: 769px) and (max-width: 1024px) {
    .domain-suggestions {
        grid-template-columns: repeat(2, 1fr);
    }

    .domain-list {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 桌面端优化 */
@media (min-width: 1025px) {
    .domain-suggestions {
        grid-template-columns: repeat(3, 1fr);
    }

    .domain-list {
        grid-template-columns: repeat(3, 1fr);
    }
}
```

## 🛡️ 安全与验证

### 输入验证与清理

#### 前端验证
```typescript
// 域名输入验证
export const validateDomainInput = (input: string): ValidationResult => {
    const errors: string[] = [];

    // 长度检查
    if (input.length < 3) {
        errors.push('域名长度不能少于3个字符');
    }
    if (input.length > 63) {
        errors.push('域名长度不能超过63个字符');
    }

    // 字符检查
    if (!/^[a-z0-9-]+$/.test(input)) {
        errors.push('域名只能包含字母、数字和连字符');
    }

    // 连字符位置检查
    if (input.startsWith('-') || input.endsWith('-')) {
        errors.push('域名不能以连字符开头或结尾');
    }

    // 连续连字符检查
    if (input.includes('--')) {
        errors.push('域名不能包含连续的连字符');
    }

    // 保留词检查
    if (RESERVED_DOMAINS.includes(input.toLowerCase())) {
        errors.push('该域名为系统保留，请选择其他域名');
    }

    return {
        isValid: errors.length === 0,
        errors,
        sanitized: sanitizeDomainInput(input)
    };
};

// 域名输入清理
export const sanitizeDomainInput = (input: string): string => {
    return input
        .toLowerCase()
        .replace(/[^a-z0-9-]/g, '') // 移除非法字符
        .replace(/^-+|-+$/g, '') // 移除开头结尾的连字符
        .replace(/-{2,}/g, '-') // 替换连续连字符为单个
        .substring(0, 63); // 限制长度
};
```

#### 后端验证
```csharp
public class DomainValidator
{
    private static readonly Regex DomainRegex = new Regex(@"^[a-z0-9-]+$", RegexOptions.Compiled);
    private static readonly HashSet<string> ReservedDomains = LoadReservedDomains();
    private static readonly HashSet<string> BannedWords = LoadBannedWords();

    public ValidationResult ValidateDomain(string subdomain)
    {
        var errors = new List<string>();

        // 基础格式验证
        if (string.IsNullOrWhiteSpace(subdomain))
        {
            errors.Add("域名不能为空");
            return new ValidationResult { IsValid = false, Errors = errors };
        }

        subdomain = subdomain.ToLowerInvariant().Trim();

        // 长度验证
        if (subdomain.Length < 3)
            errors.Add("域名长度不能少于3个字符");
        if (subdomain.Length > 63)
            errors.Add("域名长度不能超过63个字符");

        // 字符验证
        if (!DomainRegex.IsMatch(subdomain))
            errors.Add("域名只能包含字母、数字和连字符");

        // 连字符位置验证
        if (subdomain.StartsWith("-") || subdomain.EndsWith("-"))
            errors.Add("域名不能以连字符开头或结尾");

        // 连续连字符验证
        if (subdomain.Contains("--"))
            errors.Add("域名不能包含连续的连字符");

        // 保留词验证
        if (ReservedDomains.Contains(subdomain))
            errors.Add("该域名为系统保留，请选择其他域名");

        // 禁用词验证
        if (BannedWords.Any(word => subdomain.Contains(word)))
            errors.Add("域名包含不允许的内容，请选择其他域名");

        // 数字开头验证 (可选)
        if (char.IsDigit(subdomain[0]))
            errors.Add("建议域名不要以数字开头");

        return new ValidationResult
        {
            IsValid = errors.Count == 0,
            Errors = errors,
            Sanitized = SanitizeDomain(subdomain)
        };
    }

    private string SanitizeDomain(string input)
    {
        return input
            .ToLowerInvariant()
            .Trim()
            .Substring(0, Math.Min(input.Length, 63));
    }
}
```

### 安全防护机制

#### 频率限制
```csharp
public class DomainRateLimitService
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<DomainRateLimitService> _logger;

    // 域名检查频率限制
    public async Task<bool> CanCheckDomainAsync(string userId, string clientIP)
    {
        var key = $"domain_check:{userId}:{clientIP}";
        var attempts = _cache.Get<int>(key);

        if (attempts >= 100) // 每小时最多100次检查
        {
            _logger.LogWarning("域名检查频率超限: {UserId} {ClientIP}", userId, clientIP);
            return false;
        }

        _cache.Set(key, attempts + 1, TimeSpan.FromHours(1));
        return true;
    }

    // 域名预留频率限制
    public async Task<bool> CanReserveDomainAsync(string userId)
    {
        var key = $"domain_reserve:{userId}";
        var attempts = _cache.Get<int>(key);

        if (attempts >= 10) // 每天最多10次预留
        {
            _logger.LogWarning("域名预留频率超限: {UserId}", userId);
            return false;
        }

        _cache.Set(key, attempts + 1, TimeSpan.FromDays(1));
        return true;
    }
}
```

#### 恶意行为检测
```csharp
public class DomainSecurityService
{
    // 检测域名抢注行为
    public async Task<bool> DetectDomainSquattingAsync(string subdomain, string userId)
    {
        // 检查是否为知名品牌域名
        if (await IsKnownBrandDomainAsync(subdomain))
        {
            await LogSuspiciousActivityAsync(userId, "brand_squatting", subdomain);
            return true;
        }

        // 检查是否为恶意变体
        if (await IsMaliciousVariantAsync(subdomain))
        {
            await LogSuspiciousActivityAsync(userId, "malicious_variant", subdomain);
            return true;
        }

        return false;
    }

    // 检测批量注册行为
    public async Task<bool> DetectBulkRegistrationAsync(string userId)
    {
        var recentAllocations = await GetRecentAllocationsAsync(userId, TimeSpan.FromHours(24));

        if (recentAllocations.Count > 20) // 24小时内超过20个域名
        {
            await LogSuspiciousActivityAsync(userId, "bulk_registration", $"count:{recentAllocations.Count}");
            return true;
        }

        return false;
    }
}
```

## ⚡ 性能优化

### 缓存策略

#### 多层缓存架构
```csharp
public class DomainCacheManager
{
    private readonly IMemoryCache _l1Cache; // L1: 内存缓存
    private readonly IDistributedCache _l2Cache; // L2: Redis缓存
    private readonly IDomainRepository _repository; // L3: 数据库

    public async Task<SiteInfo> GetSiteByDomainAsync(string subdomain)
    {
        // L1 缓存查询 (最快)
        var l1Key = $"site:domain:{subdomain}";
        if (_l1Cache.TryGetValue(l1Key, out SiteInfo cachedSite))
        {
            return cachedSite;
        }

        // L2 缓存查询 (较快)
        var l2Key = $"site:domain:{subdomain}";
        var cachedJson = await _l2Cache.GetStringAsync(l2Key);
        if (!string.IsNullOrEmpty(cachedJson))
        {
            var site = JsonSerializer.Deserialize<SiteInfo>(cachedJson);

            // 回填 L1 缓存
            _l1Cache.Set(l1Key, site, TimeSpan.FromMinutes(5));
            return site;
        }

        // 数据库查询 (最慢)
        var dbSite = await _repository.GetSiteByDomainAsync(subdomain);
        if (dbSite != null)
        {
            // 缓存到 L2 (30分钟)
            var options = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30),
                SlidingExpiration = TimeSpan.FromMinutes(10)
            };
            await _l2Cache.SetStringAsync(l2Key, JsonSerializer.Serialize(dbSite), options);

            // 缓存到 L1 (5分钟)
            _l1Cache.Set(l1Key, dbSite, TimeSpan.FromMinutes(5));
        }

        return dbSite;
    }

    // 缓存失效策略
    public async Task InvalidateDomainCacheAsync(string subdomain)
    {
        var l1Key = $"site:domain:{subdomain}";
        var l2Key = $"site:domain:{subdomain}";

        _l1Cache.Remove(l1Key);
        await _l2Cache.RemoveAsync(l2Key);

        // 通知其他实例失效缓存
        await _messageBus.PublishAsync(new CacheInvalidationMessage
        {
            CacheKey = l1Key,
            Timestamp = DateTime.UtcNow
        });
    }
}
```

#### 预热缓存策略
```csharp
public class DomainCacheWarmupService : IHostedService
{
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        // 预热热门域名
        await WarmupPopularDomainsAsync();

        // 定期预热
        _timer = new Timer(WarmupCallback, null, TimeSpan.Zero, TimeSpan.FromHours(1));
    }

    private async Task WarmupPopularDomainsAsync()
    {
        // 获取访问量最高的域名
        var popularDomains = await _analyticsService.GetTopDomainsAsync(1000);

        var tasks = popularDomains.Select(async domain =>
        {
            try
            {
                await _cacheManager.GetSiteByDomainAsync(domain.Subdomain);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "预热域名缓存失败: {Domain}", domain.Subdomain);
            }
        });

        await Task.WhenAll(tasks);
        _logger.LogInformation("完成预热 {Count} 个热门域名", popularDomains.Count);
    }
}
```

### 数据库优化

#### 索引策略
```sql
-- 复合索引优化域名查询
CREATE INDEX CONCURRENTLY idx_domain_allocations_subdomain_status
ON domain_allocations (subdomain, status)
WHERE status = 'allocated';

-- 部分索引优化活跃域名查询
CREATE INDEX CONCURRENTLY idx_domain_allocations_active
ON domain_allocations (subdomain, site_id)
WHERE status = 'allocated' AND expires_at IS NULL;

-- 表达式索引优化模糊查询
CREATE INDEX CONCURRENTLY idx_domain_allocations_subdomain_lower
ON domain_allocations (LOWER(subdomain));

-- 时间范围索引优化统计查询
CREATE INDEX CONCURRENTLY idx_domain_analytics_date_range
ON domain_analytics (subdomain, date DESC)
WHERE date >= CURRENT_DATE - INTERVAL '90 days';
```

#### 查询优化
```csharp
public class OptimizedDomainRepository
{
    // 批量域名可用性检查
    public async Task<Dictionary<string, bool>> CheckBatchAvailabilityAsync(IEnumerable<string> subdomains)
    {
        var subdomainList = subdomains.ToList();

        // 使用 ANY 操作符进行批量查询
        var unavailableSubdomains = await _context.DomainAllocations
            .Where(da => subdomainList.Contains(da.Subdomain) && da.Status == "allocated")
            .Select(da => da.Subdomain)
            .ToListAsync();

        return subdomainList.ToDictionary(
            subdomain => subdomain,
            subdomain => !unavailableSubdomains.Contains(subdomain)
        );
    }

    // 分页查询优化
    public async Task<PagedResult<DomainAllocation>> GetUserDomainsAsync(
        Guid userId,
        int page,
        int pageSize,
        string searchTerm = null)
    {
        var query = _context.DomainAllocations
            .Where(da => da.UserId == userId)
            .AsQueryable();

        // 搜索过滤
        if (!string.IsNullOrEmpty(searchTerm))
        {
            query = query.Where(da => da.Subdomain.Contains(searchTerm.ToLower()));
        }

        // 总数查询 (优化: 使用估算)
        var totalCount = await query.CountAsync();

        // 分页数据查询
        var items = await query
            .OrderByDescending(da => da.AllocatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .Include(da => da.Site)
            .ToListAsync();

        return new PagedResult<DomainAllocation>
        {
            Items = items,
            TotalCount = totalCount,
            Page = page,
            PageSize = pageSize
        };
    }
}
```

### CDN和静态资源优化

#### CDN配置
```yaml
# CloudFlare CDN 配置
cloudflare:
  zones:
    - name: "molysite.com"
      settings:
        cache_level: "aggressive"
        browser_cache_ttl: 31536000  # 1年
        edge_cache_ttl: 86400        # 1天

  page_rules:
    - url: "*.molysite.com/static/*"
      settings:
        cache_level: "cache_everything"
        edge_cache_ttl: 2592000      # 30天

    - url: "api.molysite.com/*"
      settings:
        cache_level: "bypass"

    - url: "*.molysite.com"
      settings:
        cache_level: "standard"
        edge_cache_ttl: 3600         # 1小时
```

#### 静态资源优化
```csharp
public class StaticResourceOptimizer
{
    // 资源版本控制
    public string GetVersionedUrl(string resourcePath)
    {
        var version = GetResourceVersion(resourcePath);
        return $"{resourcePath}?v={version}";
    }

    // 资源压缩
    public async Task<byte[]> CompressResourceAsync(byte[] content, string contentType)
    {
        if (contentType.StartsWith("text/") ||
            contentType.Contains("javascript") ||
            contentType.Contains("json"))
        {
            return await GzipCompressAsync(content);
        }

        return content;
    }

    // 图片优化
    public async Task<byte[]> OptimizeImageAsync(byte[] imageData, string format)
    {
        using var image = Image.Load(imageData);

        // WebP 转换
        if (format.ToLower() == "webp")
        {
            using var stream = new MemoryStream();
            await image.SaveAsWebpAsync(stream, new WebpEncoder
            {
                Quality = 85,
                Method = WebpEncodingMethod.Level6
            });
            return stream.ToArray();
        }

        return imageData;
    }
}
```

## 🚀 部署与运维

### 容器化部署

#### Docker 配置
```dockerfile
# Dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["MolySite.Web/MolySite.Web.csproj", "MolySite.Web/"]
COPY ["MolySite.Core/MolySite.Core.csproj", "MolySite.Core/"]
COPY ["MolySite.Shared/MolySite.Shared.csproj", "MolySite.Shared/"]
RUN dotnet restore "MolySite.Web/MolySite.Web.csproj"

COPY . .
WORKDIR "/src/MolySite.Web"
RUN dotnet build "MolySite.Web.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "MolySite.Web.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/health || exit 1

ENTRYPOINT ["dotnet", "MolySite.Web.dll"]
```

#### Docker Compose 配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  molysite-web:
    build: .
    ports:
      - "80:80"
      - "443:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=molysite;Username=molysite;Password=${DB_PASSWORD}
      - Redis__ConnectionString=redis:6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ./certs:/app/certs:ro
      - ./logs:/app/logs
    restart: unless-stopped

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=molysite
      - POSTGRES_USER=molysite
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./certs:/etc/nginx/certs:ro
    depends_on:
      - molysite-web
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### Kubernetes 部署

#### 应用部署配置
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: molysite-web
  namespace: molysite
spec:
  replicas: 3
  selector:
    matchLabels:
      app: molysite-web
  template:
    metadata:
      labels:
        app: molysite-web
    spec:
      containers:
      - name: molysite-web
        image: molysite/web:latest
        ports:
        - containerPort: 80
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: ConnectionStrings__DefaultConnection
          valueFrom:
            secretKeyRef:
              name: molysite-secrets
              key: database-connection
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: molysite-web-service
  namespace: molysite
spec:
  selector:
    app: molysite-web
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
  type: ClusterIP
```

#### Ingress 配置
```yaml
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: molysite-ingress
  namespace: molysite
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/server-snippet: |
      # 通配符子域名支持
      server_name ~^(?<subdomain>.+)\.molysite\.com$;

      # 根据子域名路由
      if ($subdomain != "www") {
        set $target_service "molysite-web-service";
      }
spec:
  tls:
  - hosts:
    - "*.molysite.com"
    - "molysite.com"
    secretName: molysite-tls
  rules:
  - host: "*.molysite.com"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: molysite-web-service
            port:
              number: 80
  - host: "molysite.com"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: molysite-web-service
            port:
              number: 80
```

### 自动化部署

#### CI/CD Pipeline
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '8.0.x'
    - name: Restore dependencies
      run: dotnet restore
    - name: Build
      run: dotnet build --no-restore
    - name: Test
      run: dotnet test --no-build --verbosity normal

  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Build Docker image
      run: docker build -t molysite/web:${{ github.sha }} .
    - name: Push to registry
      run: |
        echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
        docker push molysite/web:${{ github.sha }}
        docker tag molysite/web:${{ github.sha }} molysite/web:latest
        docker push molysite/web:latest

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    steps:
    - name: Deploy to Kubernetes
      run: |
        kubectl set image deployment/molysite-web molysite-web=molysite/web:${{ github.sha }}
        kubectl rollout status deployment/molysite-web
```

## 📊 监控与分析

### 应用监控

#### 健康检查端点
```csharp
// HealthChecks/DomainHealthCheck.cs
public class DomainHealthCheck : IHealthCheck
{
    private readonly IDomainService _domainService;
    private readonly ILogger<DomainHealthCheck> _logger;

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查域名服务可用性
            var testResult = await _domainService.CheckAvailabilityAsync("health-check-test");

            // 检查数据库连接
            var dbHealthy = await CheckDatabaseHealthAsync();

            // 检查缓存连接
            var cacheHealthy = await CheckCacheHealthAsync();

            if (dbHealthy && cacheHealthy)
            {
                return HealthCheckResult.Healthy("域名服务运行正常");
            }
            else
            {
                return HealthCheckResult.Degraded("域名服务部分功能异常");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "域名健康检查失败");
            return HealthCheckResult.Unhealthy("域名服务不可用", ex);
        }
    }
}

// Program.cs 配置
builder.Services.AddHealthChecks()
    .AddCheck<DomainHealthCheck>("domain")
    .AddNpgSql(connectionString, name: "database")
    .AddRedis(redisConnectionString, name: "cache");

app.MapHealthChecks("/health", new HealthCheckOptions
{
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});
```

#### 性能监控
```csharp
// Metrics/DomainMetrics.cs
public class DomainMetrics
{
    private static readonly Counter DomainChecksTotal = Metrics
        .CreateCounter("domain_checks_total", "域名检查总数", new[] { "result" });

    private static readonly Histogram DomainCheckDuration = Metrics
        .CreateHistogram("domain_check_duration_seconds", "域名检查耗时");

    private static readonly Gauge ActiveDomainsCount = Metrics
        .CreateGauge("active_domains_count", "活跃域名数量");

    private static readonly Counter DomainAllocationsTotal = Metrics
        .CreateCounter("domain_allocations_total", "域名分配总数", new[] { "status" });

    public static void RecordDomainCheck(bool isAvailable, double duration)
    {
        DomainChecksTotal.WithLabels(isAvailable ? "available" : "unavailable").Inc();
        DomainCheckDuration.Observe(duration);
    }

    public static void UpdateActiveDomainsCount(int count)
    {
        ActiveDomainsCount.Set(count);
    }

    public static void RecordDomainAllocation(string status)
    {
        DomainAllocationsTotal.WithLabels(status).Inc();
    }
}

// 使用示例
public async Task<bool> CheckAvailabilityAsync(string subdomain)
{
    using var timer = DomainMetrics.DomainCheckDuration.NewTimer();

    try
    {
        var result = await _repository.IsDomainAvailableAsync(subdomain);
        DomainMetrics.RecordDomainCheck(result, timer.ObserveDuration().TotalSeconds);
        return result;
    }
    catch (Exception ex)
    {
        DomainMetrics.RecordDomainCheck(false, timer.ObserveDuration().TotalSeconds);
        throw;
    }
}
```

### 日志记录

#### 结构化日志
```csharp
// Logging/DomainLogger.cs
public static class DomainLogger
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();

    public static void LogDomainCheck(string subdomain, bool isAvailable, TimeSpan duration, string userId = null)
    {
        Logger.Info("域名检查 {Subdomain} 结果: {IsAvailable}, 耗时: {Duration}ms, 用户: {UserId}",
            subdomain, isAvailable, duration.TotalMilliseconds, userId);
    }

    public static void LogDomainAllocation(string subdomain, Guid siteId, string userId)
    {
        Logger.Info("域名分配 {Subdomain} 给网站 {SiteId}, 用户: {UserId}",
            subdomain, siteId, userId);
    }

    public static void LogDomainRelease(string subdomain, string reason, string userId)
    {
        Logger.Info("域名释放 {Subdomain}, 原因: {Reason}, 用户: {UserId}",
            subdomain, reason, userId);
    }

    public static void LogSuspiciousActivity(string userId, string activity, string details)
    {
        Logger.Warn("可疑活动检测 用户: {UserId}, 活动: {Activity}, 详情: {Details}",
            userId, activity, details);
    }
}
```

#### 日志配置
```xml
<!-- NLog.config -->
<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

  <targets>
    <!-- 文件日志 -->
    <target xsi:type="File" name="fileTarget"
            fileName="logs/molysite-${shortdate}.log"
            layout="${longdate} ${uppercase:${level}} ${logger} ${message} ${exception:format=tostring}" />

    <!-- 结构化日志 (JSON) -->
    <target xsi:type="File" name="jsonTarget"
            fileName="logs/molysite-${shortdate}.json">
      <layout xsi:type="JsonLayout">
        <attribute name="timestamp" layout="${longdate}" />
        <attribute name="level" layout="${level:upperCase=true}" />
        <attribute name="logger" layout="${logger}" />
        <attribute name="message" layout="${message}" />
        <attribute name="exception" layout="${exception:format=tostring}" />
        <attribute name="properties" encode="false">
          <layout xsi:type="JsonLayout" includeAllProperties="true" maxRecursionLimit="2" />
        </attribute>
      </layout>
    </target>

    <!-- 控制台日志 -->
    <target xsi:type="Console" name="consoleTarget"
            layout="${time} [${uppercase:${level}}] ${logger}: ${message} ${exception:format=tostring}" />
  </targets>

  <rules>
    <logger name="MolySite.Web.Services.Domain*" minlevel="Info" writeTo="fileTarget,jsonTarget" />
    <logger name="*" minlevel="Warn" writeTo="consoleTarget" />
  </rules>
</nlog>
```

### 分析和报告

#### 域名使用分析
```csharp
public class DomainAnalyticsService
{
    public async Task<DomainUsageReport> GenerateUsageReportAsync(DateTime startDate, DateTime endDate)
    {
        var report = new DomainUsageReport
        {
            Period = new DateRange(startDate, endDate),
            TotalDomains = await GetTotalDomainsAsync(),
            NewDomains = await GetNewDomainsAsync(startDate, endDate),
            ReleasedDomains = await GetReleasedDomainsAsync(startDate, endDate),
            TopDomains = await GetTopDomainsAsync(startDate, endDate, 100),
            DomainsByType = await GetDomainsByTypeAsync(),
            AvailabilityStats = await GetAvailabilityStatsAsync(startDate, endDate)
        };

        return report;
    }

    public async Task<SEOPerformanceReport> GenerateSEOReportAsync()
    {
        var domains = await GetAllActiveDomainsAsync();
        var seoScores = new List<DomainSEOScore>();

        foreach (var domain in domains)
        {
            var score = _seoCalculator.CalculateScore(domain.Subdomain);
            var traffic = await _analyticsService.GetTrafficDataAsync(domain.Subdomain);

            seoScores.Add(new DomainSEOScore
            {
                Domain = domain.Subdomain,
                SEOScore = score,
                PageViews = traffic.PageViews,
                UniqueVisitors = traffic.UniqueVisitors,
                BounceRate = traffic.BounceRate
            });
        }

        return new SEOPerformanceReport
        {
            AverageScore = seoScores.Average(s => s.SEOScore),
            ScoreDistribution = CalculateScoreDistribution(seoScores),
            TopPerformers = seoScores.OrderByDescending(s => s.SEOScore).Take(10).ToList(),
            Recommendations = GenerateRecommendations(seoScores)
        };
    }
}
```

## 🔮 扩展规划

### 第一阶段：基础功能 (1-2个月)

#### 核心功能实现
- ✅ 智能域名建议系统
- ✅ 实时可用性检查
- ✅ 域名分配和管理
- ✅ 基础安全验证
- ✅ 简单的用户界面

#### 技术债务
- 数据库优化和索引
- 基础缓存实现
- 简单的监控和日志
- 基本的测试覆盖

### 第二阶段：增强功能 (2-3个月)

#### 高级功能
- 🔄 域名预留机制
- 🔄 批量域名操作
- 🔄 域名转移功能
- 🔄 高级SEO分析
- 🔄 域名使用统计

#### 性能优化
- 多层缓存系统
- 数据库分区
- CDN集成
- 负载均衡

#### 安全增强
- 高级威胁检测
- 频率限制
- 审计日志
- 合规性检查

### 第三阶段：企业级功能 (3-4个月)

#### 高级特性
- 🔮 AI驱动的域名推荐
- 🔮 域名价值评估
- 🔮 自动化域名优化
- 🔮 多语言域名支持
- 🔮 域名交易市场

#### 集成功能
- 第三方域名注册商集成
- 社交媒体集成
- 营销工具集成
- 分析平台集成

#### 企业功能
- 白标解决方案
- API开放平台
- 企业级SLA
- 专业技术支持

### 第四阶段：创新功能 (4-6个月)

#### 前沿技术
- 🚀 区块链域名
- 🚀 去中心化身份
- 🚀 智能合约集成
- 🚀 NFT域名
- 🚀 元宇宙域名

#### 生态系统
- 开发者生态
- 插件市场
- 第三方集成
- 社区建设

## 📋 实施时间表

### 里程碑规划

#### 第1个月：基础架构
- Week 1-2: 数据库设计和API接口
- Week 3-4: 核心域名服务实现

#### 第2个月：用户界面
- Week 1-2: 前端组件开发
- Week 3-4: 用户体验优化和测试

#### 第3个月：性能和安全
- Week 1-2: 缓存系统和性能优化
- Week 3-4: 安全机制和监控系统

#### 第4个月：高级功能
- Week 1-2: 域名分析和报告
- Week 3-4: 管理工具和自动化

#### 第5-6个月：企业级功能
- 扩展性优化
- 高可用性部署
- 企业级功能开发

## 🎯 成功指标

### 技术指标
- **可用性**: 99.9%以上
- **响应时间**: 域名检查 < 100ms
- **并发处理**: 支持10,000+并发请求
- **缓存命中率**: 95%以上

### 业务指标
- **域名分配成功率**: 98%以上
- **用户满意度**: 4.5/5以上
- **域名利用率**: 80%以上
- **SEO改善**: 平均提升20%

### 运维指标
- **部署频率**: 每周至少1次
- **故障恢复时间**: < 5分钟
- **监控覆盖率**: 100%
- **自动化程度**: 90%以上

---

## 🎉 总结

这个全面的二级域名设置及分配实施方案涵盖了从技术架构到业务流程的各个方面，为MolySite提供了一个完整、可扩展、高性能的域名管理解决方案。

**核心优势**:
1. **技术先进**: 智能算法和现代架构
2. **用户友好**: 直观的界面和流畅的体验
3. **高性能**: 多层缓存和优化策略
4. **安全可靠**: 全方位的安全保护
5. **可扩展**: 支持未来业务增长

**实施建议**:
- 采用敏捷开发方式，分阶段实施
- 重视用户反馈，持续优化体验
- 建立完善的监控和运维体系
- 保持技术创新，引领行业发展

这个方案将帮助MolySite在竞争激烈的建站平台市场中建立技术优势，为用户提供卓越的域名管理体验！ 🚀
```
```
```
```