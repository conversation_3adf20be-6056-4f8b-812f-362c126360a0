# 🔄 布局命名更新说明

## 📋 更新内容

根据您的要求，已将方案中的布局命名进行了简化：

### 🎯 命名变更

**原命名**: `StandardSiteLayout`
**新命名**: `SiteLayout`

### 📝 更新的文件和位置

1. **架构图中的布局名称**
   - `Sites Frontend` → `SiteLayout`

2. **组件文件路径**
   - `Components/Sites/Layouts/SiteLayout.razor`

3. **代码引用**
   - `<LayoutView Layout="@typeof(SiteLayout)">`
   - `<div class="site-layout" data-site-id="@Site?.Id">`

4. **CSS文件**
   - `wwwroot/css/site-layout.css`
   - `.site-layout` 样式类

5. **实施步骤**
   - 第2周任务：创建SiteLayout

## 🎨 简化后的架构

```
MolySite.Web
├── Platform Frontend (现有)
│   ├── PublicLayout
│   ├── DashboardLayout  
│   └── MainLayout
└── Sites Frontend (新增)
    └── SiteLayout ← 简化命名
        ├── SiteHeader
        ├── SiteNavigation
        ├── SitePageRenderer
        └── SiteFooter
```

## 🔧 技术实现保持不变

所有技术实现细节保持不变，仅仅是命名的简化：

- ✅ 智能路由逻辑不变
- ✅ 组件功能不变
- ✅ 服务接口不变
- ✅ 实施步骤不变
- ✅ 预期效果不变

## 💡 命名优势

**简化后的优势**:
- 🎯 **更简洁**: `SiteLayout` 比 `StandardSiteLayout` 更简洁
- 🎯 **更直观**: 直接表达"网站布局"的含义
- 🎯 **更灵活**: 未来扩展其他类型布局时命名更清晰
- 🎯 **更一致**: 与现有的 `PublicLayout`、`DashboardLayout` 命名风格一致

## 🚀 实施影响

这个命名变更对实施计划没有任何影响：

- ✅ **开发时间**: 4-6周不变
- ✅ **技术复杂度**: 保持简化设计
- ✅ **功能范围**: 标准展示型网站不变
- ✅ **用户体验**: 完全一致

## 📁 文件结构更新

```
Components/Sites/
├── Layouts/
│   └── SiteLayout.razor          ← 更新命名
├── Components/
│   ├── SiteHeader.razor
│   ├── SiteNavigation.razor
│   ├── SitePageRenderer.razor
│   └── SiteFooter.razor
└── Services/
    └── SitePageService.cs
```

## 🎉 总结

命名已成功简化为 `SiteLayout`，保持了方案的简洁性和一致性，同时不影响任何技术实现和功能特性。

这个更新让整个方案更加清晰和专业！🚀
